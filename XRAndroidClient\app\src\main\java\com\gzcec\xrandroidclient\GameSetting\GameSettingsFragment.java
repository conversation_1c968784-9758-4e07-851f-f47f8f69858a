package com.gzcec.xrandroidclient.GameSetting;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.gzcec.xrandroidclient.LogViewerActivity;
import com.gzcec.xrandroidclient.R;
import com.gzcec.xrandroidclient.settings.NetworkSettingsActivity;
import com.gzcec.xrandroidclient.utils.LogManager;

public class GameSettingsFragment extends Fragment {
    private static final String TAG = "GameSettingsFragment";

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_game_settings, container, false);

        // 初始化网络设置按钮
        Button btnNetworkSettings = view.findViewById(R.id.btn_network_settings);
        btnNetworkSettings.setOnClickListener(v -> {
            // 打开网络设置界面
            Intent intent = new Intent(getActivity(), NetworkSettingsActivity.class);
            startActivity(intent);
        });

        // 初始化日志查看按钮
        Button btnViewLogs = view.findViewById(R.id.btn_view_logs);
        if (btnViewLogs != null) {
            btnViewLogs.setOnClickListener(v -> {
                // 打开日志查看器
                Intent intent = new Intent(getActivity(), LogViewerActivity.class);
                startActivity(intent);
                Log.d(TAG, "打开日志查看器");
            });
        }

        return view;
    }

    @Override
    public void onResume() {
        super.onResume();

        // 每次进入游戏设置页面时清理旧日志
        LogManager.getInstance().cleanOldLogs();
        Log.d(TAG, "执行日志清理检查");
    }
}