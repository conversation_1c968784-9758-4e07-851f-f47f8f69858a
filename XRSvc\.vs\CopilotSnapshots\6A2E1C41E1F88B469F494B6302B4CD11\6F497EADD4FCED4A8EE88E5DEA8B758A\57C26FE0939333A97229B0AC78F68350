﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.AccessControl;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Jskj.XRSystem.Common
{
    public class GameInfo : INotifyPropertyChanged
    {

        #region Filed

        /// <summary>
        /// ID编号
        /// </summary>
        public int ID { get; set; }

        private string _name;
        /// <summary>
        /// 影片名称
        /// </summary>
        public string Name
        {
            get { return _name; }
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }
        }

        /// <summary>
        /// 影片类别
        /// </summary>
        public int GameCategoryIndex { get; set; }

        /// <summary>
        /// APK包名
        /// </summary>
        public string PackageName { get; set; }

        /// <summary>
        /// 视频文件相对路径
        /// </summary>
        public string VideoFilePath { get; set; }

        /// <summary>
        /// 影片类型
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public GameType GameType { get; set; }

        /// <summary>
        /// 视频格式
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public VideoFormat VideoFormat { get; set; }

        /// <summary>
        /// 视频时长，单位：秒
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int ShowOrder { get; set; }

        /// <summary>
        /// 是否显示
        /// </summary>
        public bool IsShow { get; set; }

        /// <summary>
        /// 是否播放动作
        /// </summary>
        public bool IsPlayAction { get; set; } = false;

        /// <summary>
        /// 动作文件相对路径
        /// </summary>
        public string ActionFilePath { get; set; }

        #endregion

        public GameInfo()
        {
            

        }

        #region Method Public

        public static List<GameInfo> InitDefault()
        {
            List<GameInfo> list = new List<GameInfo>();

            GameInfo game = new GameInfo();
            game.ID = 10001;
            game.Name = "唐诡";
            game.GameCategoryIndex = 1;
            game.GameType = GameType.Video;
            game.VideoFormat = VideoFormat.TYPE_3D360_TB;
            game.VideoFilePath = "/唐诡/唐诡.mp4";
            game.Duration = 125;
            game.ShowOrder = 50;
            game.IsShow = true;
            game.IsPlayAction = true;
            game.ActionFilePath = "/唐诡/10001.jdz";

            game = new GameInfo();
            game.ID = 10002;
            game.Name = "秦陵";
            game.GameCategoryIndex = 1;
            game.GameType = GameType.Video;
            game.VideoFormat = VideoFormat.TYPE_3D360_TB;
            game.VideoFilePath = "/秦陵/秦陵.mp4";
            game.Duration = 125;
            game.ShowOrder = 50;
            game.IsShow = true;
            game.IsPlayAction = true;
            game.ActionFilePath = "/秦陵/01.jdz";
            list.Add(game);

            game = new GameInfo();
            game.ID = 10003;
            game.Name = "长征";
            game.GameCategoryIndex = 1;
            game.GameType = GameType.Video;
            game.VideoFormat = VideoFormat.TYPE_3D360_TB;
            game.VideoFilePath = "/长征/长征.mp4";
            game.Duration = 125;
            game.ShowOrder = 50;
            game.IsShow = true;
            game.IsPlayAction = true;
            game.ActionFilePath = "/长征/01.jdz";
            list.Add(game);

            return list;
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
