﻿using Jskj.AppLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XRSvc
{
    /// <summary>
    /// 程序入口
    /// </summary>
    public class Program
    {

        #region Field

        /// <summary>
        /// 日志保存目录
        /// </summary>
        private static readonly string LogSaveDirPath = new DirectoryInfo(System.AppDomain.CurrentDomain.BaseDirectory).FullName;

        #endregion

        #region Method

        /// <summary>
        /// 程序入口
        /// </summary>
        /// <param name="Args"></param>
        [System.STAThreadAttribute()]
        private static void Main(string[] Args)
        {
            try
            {
                Log.SetLogFileSaveDirPath(LogSaveDirPath);
                HBAPP app = new HBAPP();
                app.RunMain(Args);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                Environment.Exit(-2);
            }
        }

        #endregion

    }
}
