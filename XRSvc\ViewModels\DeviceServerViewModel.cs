using System;
using System.Collections.ObjectModel;
using System.Linq;
using XRSvc.DataPack;
using XRSvc.DataSource;

namespace XRSvc.ViewModels
{
    public class DeviceServerViewModel : BaseViewModel
    {
        #region Fields

        private ObservableCollection<DeviceServerSource> _source;
        private string _lastSelectedTag;

        #endregion

        #region Properties

        /// <summary>
        /// 设备服务器集合
        /// </summary>
        public ObservableCollection<DeviceServerSource> Source
        {
            get => _source;
            set
            {
                _source = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前选中的设备服务器标签
        /// </summary>
        public string LastSelectedTag
        {
            get => _lastSelectedTag;
            set
            {
                _lastSelectedTag = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// 构造函数，初始化设备服务器集合
        /// </summary>
        public DeviceServerViewModel()
        {
            _source = new ObservableCollection<DeviceServerSource>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 更新设备服务器数据
        /// </summary>
        /// <param name="deviceServerInfos">设备服务器信息数组</param>
        /// <param name="lastSelectedTag">上次选择的标签</param>
        public void UpdateSource(DeviceServerInfo[] deviceServerInfos, string lastSelectedTag = null)
        {
            RunOnUIThread(() =>
            {
                _source.Clear();

                foreach (var info in deviceServerInfos)
                {
                    _source.Add(new DeviceServerSource
                    {
                        Name = info.Name,
                        Tag = info.Tag,
                        IsOnline = info.IsOnline,
                        IsSelected = false
                    });
                }

                // 设置默认选择
                SetDefaultSelection(lastSelectedTag);

                OnPropertyChanged(nameof(Source));
            });
        }

        /// <summary>
        /// 获取设备服务器信息数组
        /// </summary>
        /// <returns>设备服务器信息数组</returns>
        public DeviceServerInfo[] GetDeviceServerInfos()
        {
            var deviceServerInfos = new DeviceServerInfo[_source.Count];

            for (int i = 0; i < _source.Count; i++)
            {
                var server = _source[i];
                deviceServerInfos[i] = new DeviceServerInfo
                {
                    Name = server.Name,
                    Tag = server.Tag,
                    IsOnline = server.IsOnline
                };
            }

            return deviceServerInfos;
        }

        /// <summary>
        /// 获取当前选中的设备服务器标签
        /// </summary>
        /// <returns>选中的标签，如果没有选中则返回空字符串</returns>
        public string GetSelectedTag()
        {
            var selectedServer = _source.FirstOrDefault(s => s.IsSelected);
            return selectedServer?.Tag ?? string.Empty;
        }

        /// <summary>
        /// 选择指定的设备服务器
        /// </summary>
        /// <param name="tag">要选择的服务器标签</param>
        public void SelectServer(string tag)
        {
            RunOnUIThread(() =>
            {
                // 清除所有选择
                foreach (var server in _source)
                {
                    server.IsSelected = false;
                }

                // 设置指定服务器为选中状态
                var targetServer = _source.FirstOrDefault(s => s.Tag == tag);
                if (targetServer != null)
                {
                    targetServer.IsSelected = true;
                    LastSelectedTag = tag;
                }
            });
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 设置默认选择
        /// </summary>
        /// <param name="lastSelectedTag">上次选择的标签</param>
        private void SetDefaultSelection(string lastSelectedTag)
        {
            if (_source.Count == 0) return;

            // 如果有上次选择的标签，尝试选择它
            if (!string.IsNullOrEmpty(lastSelectedTag))
            {
                var lastSelected = _source.FirstOrDefault(s => s.Tag == lastSelectedTag);
                if (lastSelected != null)
                {
                    lastSelected.IsSelected = true;
                    LastSelectedTag = lastSelectedTag;
                    return;
                }
            }

            // 否则选择第一个服务器
            _source[0].IsSelected = true;
            LastSelectedTag = _source[0].Tag;
        }

        #endregion
    }
}