using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Protocol;
using XRSvc.Utils;

namespace XRSvc.MQTT
{
    /// <summary>
    /// MQTT连接池管理器
    /// 优化MQTT连接的创建、复用和销毁，提升系统性能
    /// </summary>
    public class MqttConnectionPool : IDisposable
    {
        private static readonly string TAG = "MqttConnectionPool";
        
        // 单例实例
        private static volatile MqttConnectionPool _instance;
        private static readonly object _lock = new object();
        
        // 连接池配置
        private const int MAX_POOL_SIZE = 5;           // 最大连接数
        private const int MIN_POOL_SIZE = 2;           // 最小连接数
        private const int CONNECTION_TIMEOUT = 30000;  // 连接超时(ms)
        private const int KEEP_ALIVE_INTERVAL = 60;    // 心跳间隔(s)
        private const long CONNECTION_MAX_IDLE = 300000; // 连接最大空闲时间(5分钟)
        
        // 连接池存储
        private readonly ConcurrentQueue<PooledMqttConnection> _availableConnections;
        private readonly ConcurrentDictionary<string, PooledMqttConnection> _activeConnections;
        private readonly SemaphoreSlim _poolSemaphore;
        private int _totalConnections;
        
        // 连接配置
        private string _brokerHost;
        private int _brokerPort;
        private string _clientIdPrefix;
        private MqttClientOptions _defaultOptions;
        
        // 清理任务
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;
        
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private MqttConnectionPool()
        {
            _availableConnections = new ConcurrentQueue<PooledMqttConnection>();
            _activeConnections = new ConcurrentDictionary<string, PooledMqttConnection>();
            _poolSemaphore = new SemaphoreSlim(MAX_POOL_SIZE, MAX_POOL_SIZE);
            _totalConnections = 0;
            
            // 启动定期清理任务
            _cleanupTimer = new Timer(CleanupIdleConnections, null, 
                TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }
        
        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static MqttConnectionPool Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new MqttConnectionPool();
                        }
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// 初始化连接池
        /// </summary>
        public async Task InitializeAsync(string brokerHost, int brokerPort, string clientIdPrefix)
        {
            _brokerHost = brokerHost;
            _brokerPort = brokerPort;
            _clientIdPrefix = clientIdPrefix;
            
            // 配置默认连接选项
            _defaultOptions = new MqttClientOptionsBuilder()
                .WithTcpServer(brokerHost, brokerPort)
                .WithTimeout(TimeSpan.FromMilliseconds(CONNECTION_TIMEOUT))
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(KEEP_ALIVE_INTERVAL))
                .WithCleanSession(true)
                .WithAutomaticReconnect()
                .Build();
            
            // 预创建最小连接数
            await PreCreateConnectionsAsync();
            
            UnifiedLogger.Info(TAG, $"MQTT连接池初始化完成 - 预创建连接数: {MIN_POOL_SIZE}");
        }
        
        /// <summary>
        /// 预创建最小连接数
        /// </summary>
        private async Task PreCreateConnectionsAsync()
        {
            var tasks = new List<Task>();
            
            for (int i = 0; i < MIN_POOL_SIZE; i++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    var connection = await CreateNewConnectionAsync();
                    if (connection != null)
                    {
                        _availableConnections.Enqueue(connection);
                        Interlocked.Increment(ref _totalConnections);
                    }
                }));
            }
            
            await Task.WhenAll(tasks);
        }
        
        /// <summary>
        /// 创建新的连接
        /// </summary>
        private async Task<PooledMqttConnection> CreateNewConnectionAsync()
        {
            try
            {
                string clientId = $"{_clientIdPrefix}_{DateTime.Now.Ticks}_{Thread.CurrentThread.ManagedThreadId}";
                
                var factory = new MqttFactory();
                var client = factory.CreateMqttClient();
                
                var options = new MqttClientOptionsBuilder()
                    .WithTcpServer(_brokerHost, _brokerPort)
                    .WithClientId(clientId)
                    .WithTimeout(TimeSpan.FromMilliseconds(CONNECTION_TIMEOUT))
                    .WithKeepAlivePeriod(TimeSpan.FromSeconds(KEEP_ALIVE_INTERVAL))
                    .WithCleanSession(true)
                    .WithAutomaticReconnect()
                    .Build();
                
                var pooledConnection = new PooledMqttConnection(client, clientId);
                
                UnifiedLogger.Debug(TAG, $"创建新的MQTT连接: {clientId}");
                return pooledConnection;
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "创建MQTT连接失败", ex);
                return null;
            }
        }
        
        /// <summary>
        /// 获取连接
        /// </summary>
        public async Task<PooledMqttConnection> GetConnectionAsync()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(MqttConnectionPool));
            
            await _poolSemaphore.WaitAsync();
            
            try
            {
                // 首先尝试从可用连接池获取
                if (_availableConnections.TryDequeue(out var connection))
                {
                    // 检查连接是否仍然有效
                    if (IsConnectionValid(connection))
                    {
                        connection.MarkAsActive();
                        _activeConnections.TryAdd(connection.ClientId, connection);
                        UnifiedLogger.Debug(TAG, $"复用连接: {connection.ClientId}");
                        return connection;
                    }
                    else
                    {
                        // 连接无效，关闭并重新创建
                        await CloseConnectionAsync(connection);
                        Interlocked.Decrement(ref _totalConnections);
                    }
                }
                
                // 如果没有可用连接且未达到最大连接数，创建新连接
                if (_totalConnections < MAX_POOL_SIZE)
                {
                    connection = await CreateNewConnectionAsync();
                    if (connection != null)
                    {
                        connection.MarkAsActive();
                        _activeConnections.TryAdd(connection.ClientId, connection);
                        Interlocked.Increment(ref _totalConnections);
                        UnifiedLogger.Debug(TAG, $"创建新连接: {connection.ClientId}");
                        return connection;
                    }
                }
                
                UnifiedLogger.Warning(TAG, "无法获取MQTT连接 - 连接池已满或创建失败");
                return null;
            }
            finally
            {
                _poolSemaphore.Release();
            }
        }
        
        /// <summary>
        /// 归还连接到池中
        /// </summary>
        public async Task ReturnConnectionAsync(PooledMqttConnection connection)
        {
            if (connection == null || _disposed) return;
            
            try
            {
                // 从活跃连接中移除
                _activeConnections.TryRemove(connection.ClientId, out _);
                
                // 检查连接是否仍然有效
                if (IsConnectionValid(connection) && _availableConnections.Count < MAX_POOL_SIZE)
                {
                    connection.MarkAsIdle();
                    _availableConnections.Enqueue(connection);
                    UnifiedLogger.Debug(TAG, $"连接归还到池中: {connection.ClientId}");
                }
                else
                {
                    // 连接无效或池已满，关闭连接
                    await CloseConnectionAsync(connection);
                    Interlocked.Decrement(ref _totalConnections);
                    UnifiedLogger.Debug(TAG, $"关闭多余连接: {connection.ClientId}");
                }
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, $"归还连接失败: {connection.ClientId}", ex);
            }
        }
        
        /// <summary>
        /// 检查连接是否有效
        /// </summary>
        private bool IsConnectionValid(PooledMqttConnection connection)
        {
            if (connection?.Client == null)
                return false;
            
            // 检查连接状态
            if (!connection.Client.IsConnected)
                return false;
            
            // 检查空闲时间
            var idleTime = DateTime.Now - connection.LastUsedTime;
            if (idleTime.TotalMilliseconds > CONNECTION_MAX_IDLE)
            {
                UnifiedLogger.Debug(TAG, $"连接空闲时间过长: {connection.ClientId}, 空闲时间: {idleTime.TotalMilliseconds}ms");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 关闭连接
        /// </summary>
        private async Task CloseConnectionAsync(PooledMqttConnection connection)
        {
            if (connection?.Client == null) return;
            
            try
            {
                if (connection.Client.IsConnected)
                {
                    await connection.Client.DisconnectAsync();
                }
                connection.Client.Dispose();
                UnifiedLogger.Debug(TAG, $"连接已关闭: {connection.ClientId}");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, $"关闭连接失败: {connection.ClientId}", ex);
            }
        }
        
        /// <summary>
        /// 清理空闲连接
        /// </summary>
        private async void CleanupIdleConnections(object state)
        {
            if (_disposed) return;
            
            try
            {
                var connectionsToRemove = new List<PooledMqttConnection>();
                var tempConnections = new List<PooledMqttConnection>();
                
                // 收集所有可用连接
                while (_availableConnections.TryDequeue(out var connection))
                {
                    if (IsConnectionValid(connection))
                    {
                        tempConnections.Add(connection);
                    }
                    else
                    {
                        connectionsToRemove.Add(connection);
                    }
                }
                
                // 将有效连接放回队列
                foreach (var connection in tempConnections)
                {
                    _availableConnections.Enqueue(connection);
                }
                
                // 关闭无效连接
                foreach (var connection in connectionsToRemove)
                {
                    await CloseConnectionAsync(connection);
                    Interlocked.Decrement(ref _totalConnections);
                }
                
                if (connectionsToRemove.Count > 0)
                {
                    UnifiedLogger.Info(TAG, $"清理了 {connectionsToRemove.Count} 个空闲连接");
                }
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "清理空闲连接失败", ex);
            }
        }
        
        /// <summary>
        /// 获取连接池状态
        /// </summary>
        public PoolStatus GetPoolStatus()
        {
            return new PoolStatus(
                _totalConnections,
                _availableConnections.Count,
                _activeConnections.Count,
                MAX_POOL_SIZE
            );
        }
        
        /// <summary>
        /// 销毁连接池
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            
            try
            {
                // 停止清理定时器
                _cleanupTimer?.Dispose();
                
                // 关闭所有活跃连接
                var closeTasks = new List<Task>();
                foreach (var connection in _activeConnections.Values)
                {
                    closeTasks.Add(CloseConnectionAsync(connection));
                }
                
                // 关闭所有可用连接
                while (_availableConnections.TryDequeue(out var connection))
                {
                    closeTasks.Add(CloseConnectionAsync(connection));
                }
                
                Task.WaitAll(closeTasks.ToArray(), TimeSpan.FromSeconds(5));
                
                _activeConnections.Clear();
                _poolSemaphore?.Dispose();
                
                UnifiedLogger.Info(TAG, "MQTT连接池已销毁");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "销毁连接池失败", ex);
            }
        }
    }
    
    /// <summary>
    /// 连接池状态类
    /// </summary>
    public class PoolStatus
    {
        public int TotalConnections { get; }
        public int AvailableConnections { get; }
        public int ActiveConnections { get; }
        public int MaxPoolSize { get; }
        
        public PoolStatus(int total, int available, int active, int maxSize)
        {
            TotalConnections = total;
            AvailableConnections = available;
            ActiveConnections = active;
            MaxPoolSize = maxSize;
        }
        
        public override string ToString()
        {
            return $"PoolStatus{{total={TotalConnections}, available={AvailableConnections}, active={ActiveConnections}, max={MaxPoolSize}}}";
        }
    }
}
