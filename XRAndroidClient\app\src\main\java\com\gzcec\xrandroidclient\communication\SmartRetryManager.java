package com.gzcec.xrandroidclient.communication;

import android.util.Log;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

/**
 * 智能重试管理器
 * 实现指数退避、断路器模式、重试策略优化
 */
public class SmartRetryManager {
    private static final String TAG = "SmartRetryManager";
    
    // 重试配置
    private static final int DEFAULT_MAX_RETRIES = 3;
    private static final long DEFAULT_BASE_DELAY = 1000;     // 基础延迟 1秒
    private static final long DEFAULT_MAX_DELAY = 30000;     // 最大延迟 30秒
    private static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0;
    private static final double DEFAULT_JITTER_FACTOR = 0.1;
    
    // 断路器配置
    private static final int CIRCUIT_FAILURE_THRESHOLD = 5;  // 失败阈值
    private static final long CIRCUIT_TIMEOUT = 60000;       // 断路器超时 1分钟
    private static final int CIRCUIT_SUCCESS_THRESHOLD = 3;  // 成功阈值
    
    // 重试策略
    private final RetryPolicy retryPolicy;
    private final ScheduledExecutorService scheduler;
    
    // 断路器状态
    private volatile CircuitState circuitState = CircuitState.CLOSED;
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);
    private final AtomicInteger consecutiveSuccesses = new AtomicInteger(0);
    private final AtomicLong lastFailureTime = new AtomicLong(0);
    
    // 统计信息
    private final AtomicInteger totalAttempts = new AtomicInteger(0);
    private final AtomicInteger totalSuccesses = new AtomicInteger(0);
    private final AtomicInteger totalFailures = new AtomicInteger(0);
    private final AtomicInteger circuitOpenCount = new AtomicInteger(0);
    
    /**
     * 重试策略配置
     */
    public static class RetryPolicy {
        public final int maxRetries;
        public final long baseDelay;
        public final long maxDelay;
        public final double backoffMultiplier;
        public final double jitterFactor;
        
        public RetryPolicy(int maxRetries, long baseDelay, long maxDelay, 
                          double backoffMultiplier, double jitterFactor) {
            this.maxRetries = Math.max(0, maxRetries);
            this.baseDelay = Math.max(100, baseDelay);
            this.maxDelay = Math.max(baseDelay, maxDelay);
            this.backoffMultiplier = Math.max(1.0, backoffMultiplier);
            this.jitterFactor = Math.max(0.0, Math.min(1.0, jitterFactor));
        }
        
        public static RetryPolicy defaultPolicy() {
            return new RetryPolicy(DEFAULT_MAX_RETRIES, DEFAULT_BASE_DELAY, 
                DEFAULT_MAX_DELAY, DEFAULT_BACKOFF_MULTIPLIER, DEFAULT_JITTER_FACTOR);
        }
        
        public static RetryPolicy aggressive() {
            return new RetryPolicy(5, 500, 10000, 1.5, 0.2);
        }
        
        public static RetryPolicy conservative() {
            return new RetryPolicy(2, 2000, 60000, 3.0, 0.05);
        }
    }
    
    /**
     * 断路器状态
     */
    public enum CircuitState {
        CLOSED,     // 关闭状态，正常执行
        OPEN,       // 开启状态，快速失败
        HALF_OPEN   // 半开状态，尝试恢复
    }
    
    /**
     * 重试结果
     */
    public static class RetryResult<T> {
        public final boolean success;
        public final T result;
        public final Exception lastException;
        public final int attemptCount;
        public final long totalDuration;
        
        private RetryResult(boolean success, T result, Exception lastException, 
                           int attemptCount, long totalDuration) {
            this.success = success;
            this.result = result;
            this.lastException = lastException;
            this.attemptCount = attemptCount;
            this.totalDuration = totalDuration;
        }
        
        public static <T> RetryResult<T> success(T result, int attempts, long duration) {
            return new RetryResult<>(true, result, null, attempts, duration);
        }
        
        public static <T> RetryResult<T> failure(Exception exception, int attempts, long duration) {
            return new RetryResult<>(false, null, exception, attempts, duration);
        }
    }
    
    /**
     * 构造函数
     */
    public SmartRetryManager(RetryPolicy policy) {
        this.retryPolicy = policy != null ? policy : RetryPolicy.defaultPolicy();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SmartRetry");
            t.setDaemon(true);
            return t;
        });
        
        Log.i(TAG, "智能重试管理器已初始化 - " + this.retryPolicy);
    }
    
    /**
     * 默认构造函数
     */
    public SmartRetryManager() {
        this(RetryPolicy.defaultPolicy());
    }
    
    /**
     * 执行带重试的操作
     */
    public <T> CompletableFuture<RetryResult<T>> executeWithRetry(Supplier<T> operation) {
        return executeWithRetry(operation, null);
    }
    
    /**
     * 执行带重试的操作（带条件判断）
     */
    public <T> CompletableFuture<RetryResult<T>> executeWithRetry(Supplier<T> operation, 
                                                                 java.util.function.Predicate<Exception> shouldRetry) {
        CompletableFuture<RetryResult<T>> future = new CompletableFuture<>();
        
        // 检查断路器状态
        if (!isCircuitClosed()) {
            Exception circuitException = new Exception("断路器开启，操作被拒绝");
            future.complete(RetryResult.failure(circuitException, 0, 0));
            return future;
        }
        
        executeAttempt(operation, shouldRetry, 0, System.currentTimeMillis(), future);
        return future;
    }
    
    /**
     * 执行单次尝试
     */
    private <T> void executeAttempt(Supplier<T> operation, 
                                   java.util.function.Predicate<Exception> shouldRetry,
                                   int attemptNumber, long startTime, 
                                   CompletableFuture<RetryResult<T>> future) {
        
        totalAttempts.incrementAndGet();
        
        try {
            T result = operation.get();
            
            // 操作成功
            onSuccess();
            totalSuccesses.incrementAndGet();
            
            long duration = System.currentTimeMillis() - startTime;
            future.complete(RetryResult.success(result, attemptNumber + 1, duration));
            
            Log.d(TAG, String.format("操作成功 - 尝试次数: %d, 耗时: %dms", attemptNumber + 1, duration));
            
        } catch (Exception e) {
            totalFailures.incrementAndGet();
            
            // 检查是否应该重试
            boolean shouldRetryOperation = (shouldRetry == null || shouldRetry.test(e)) && 
                                         attemptNumber < retryPolicy.maxRetries;
            
            if (shouldRetryOperation) {
                // 计算延迟时间
                long delay = calculateDelay(attemptNumber);
                
                Log.w(TAG, String.format("操作失败，将在 %dms 后重试 - 尝试: %d/%d", 
                    delay, attemptNumber + 1, retryPolicy.maxRetries + 1), e);
                
                // 调度下次重试
                scheduler.schedule(() -> {
                    executeAttempt(operation, shouldRetry, attemptNumber + 1, startTime, future);
                }, delay, TimeUnit.MILLISECONDS);
                
            } else {
                // 重试次数用尽或不应重试
                onFailure();
                
                long duration = System.currentTimeMillis() - startTime;
                future.complete(RetryResult.failure(e, attemptNumber + 1, duration));
                
                Log.e(TAG, String.format("操作最终失败 - 尝试次数: %d, 耗时: %dms", 
                    attemptNumber + 1, duration), e);
            }
        }
    }
    
    /**
     * 计算延迟时间（指数退避 + 抖动）
     */
    private long calculateDelay(int attemptNumber) {
        // 指数退避
        long delay = (long) (retryPolicy.baseDelay * Math.pow(retryPolicy.backoffMultiplier, attemptNumber));
        
        // 限制最大延迟
        delay = Math.min(delay, retryPolicy.maxDelay);
        
        // 添加抖动
        if (retryPolicy.jitterFactor > 0) {
            double jitter = delay * retryPolicy.jitterFactor * Math.random();
            delay += (long) jitter;
        }
        
        return delay;
    }
    
    /**
     * 检查断路器是否关闭
     */
    private boolean isCircuitClosed() {
        switch (circuitState) {
            case CLOSED:
                return true;
                
            case OPEN:
                // 检查是否应该转为半开状态
                if (System.currentTimeMillis() - lastFailureTime.get() > CIRCUIT_TIMEOUT) {
                    circuitState = CircuitState.HALF_OPEN;
                    consecutiveSuccesses.set(0);
                    Log.i(TAG, "断路器转为半开状态");
                    return true;
                }
                return false;
                
            case HALF_OPEN:
                return true;
                
            default:
                return false;
        }
    }
    
    /**
     * 处理操作成功
     */
    private void onSuccess() {
        consecutiveFailures.set(0);
        
        if (circuitState == CircuitState.HALF_OPEN) {
            int successes = consecutiveSuccesses.incrementAndGet();
            if (successes >= CIRCUIT_SUCCESS_THRESHOLD) {
                circuitState = CircuitState.CLOSED;
                consecutiveSuccesses.set(0);
                Log.i(TAG, "断路器已关闭 - 连续成功次数达到阈值");
            }
        }
    }
    
    /**
     * 处理操作失败
     */
    private void onFailure() {
        consecutiveSuccesses.set(0);
        lastFailureTime.set(System.currentTimeMillis());
        
        int failures = consecutiveFailures.incrementAndGet();
        
        if (circuitState == CircuitState.CLOSED && failures >= CIRCUIT_FAILURE_THRESHOLD) {
            circuitState = CircuitState.OPEN;
            circuitOpenCount.incrementAndGet();
            Log.w(TAG, "断路器已开启 - 连续失败次数: " + failures);
        } else if (circuitState == CircuitState.HALF_OPEN) {
            circuitState = CircuitState.OPEN;
            circuitOpenCount.incrementAndGet();
            Log.w(TAG, "断路器重新开启 - 半开状态下失败");
        }
    }
    
    /**
     * 手动重置断路器
     */
    public void resetCircuit() {
        circuitState = CircuitState.CLOSED;
        consecutiveFailures.set(0);
        consecutiveSuccesses.set(0);
        Log.i(TAG, "断路器已手动重置");
    }
    
    /**
     * 获取当前断路器状态
     */
    public CircuitState getCircuitState() {
        return circuitState;
    }
    
    /**
     * 获取重试统计信息
     */
    public RetryStatistics getStatistics() {
        return new RetryStatistics(
            totalAttempts.get(),
            totalSuccesses.get(),
            totalFailures.get(),
            circuitOpenCount.get(),
            consecutiveFailures.get(),
            circuitState
        );
    }
    
    /**
     * 关闭重试管理器
     */
    public void shutdown() {
        try {
            scheduler.shutdown();
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
            Log.i(TAG, "智能重试管理器已关闭");
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 重试统计信息
     */
    public static class RetryStatistics {
        public final int totalAttempts;
        public final int totalSuccesses;
        public final int totalFailures;
        public final int circuitOpenCount;
        public final int consecutiveFailures;
        public final CircuitState circuitState;
        
        RetryStatistics(int attempts, int successes, int failures, int circuitOpens, 
                       int consecutive, CircuitState state) {
            this.totalAttempts = attempts;
            this.totalSuccesses = successes;
            this.totalFailures = failures;
            this.circuitOpenCount = circuitOpens;
            this.consecutiveFailures = consecutive;
            this.circuitState = state;
        }
        
        public double getSuccessRate() {
            return totalAttempts > 0 ? (double) totalSuccesses / totalAttempts : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("RetryStats{attempts=%d, successes=%d, failures=%d, " +
                "successRate=%.2f%%, circuitOpens=%d, consecutiveFailures=%d, state=%s}", 
                totalAttempts, totalSuccesses, totalFailures, getSuccessRate() * 100,
                circuitOpenCount, consecutiveFailures, circuitState);
        }
    }
}
