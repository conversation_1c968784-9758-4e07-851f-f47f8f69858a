﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Threading;
using XRSvc.DataPack;
using XRSvc.DataSource;

namespace XRSvc.ViewModels
{
    public class GameViewModel : INotifyPropertyChanged
    {
        #region Fields

        private ObservableCollection<GameSource> _games;

        #endregion

        #region Properties

        public ObservableCollection<GameSource> Games
        {
            get => _games;
            set
            {
                _games = value;
                OnPropertyChanged();
            }
        }

        public GameSource this[int index]
        {
            get => _games[index];
        }

        #endregion

        #region Events

        public event PropertyChangedEventHandler PropertyChanged;

        public event Action<DateTime> GamesUpdated;

        #endregion

        #region Constructor

        public GameViewModel()
        {
            _games = new ObservableCollection<GameSource>();
        }

        #endregion

        #region Methods

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void ResetGames()
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;

            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(ResetGames);
                return;
            }

            foreach (var game in _games)
            {
                game.Name = string.Empty;
                game.PackageName = string.Empty;
                game.VideoFilePath = string.Empty;
                game.GameType = GameType.None;
                game.VideoFormat = VideoFormat.TYPE_UNKNOWN;
                game.Duration = 0;
                game.ShowOrder = 0;
                game.IsShow = false;
                game.IsPlayAction = false;
                game.ActionFilePath = string.Empty;
            }

            OnPropertyChanged(nameof(Games));
        }

        public void UpdateGames(GameInfo[] gameInfos)
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;

            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(() => UpdateGames(gameInfos));
                return;
            }

            _games.Clear();

            foreach (var info in gameInfos)
            {
                _games.Add(new GameSource
                {
                    ID = info.ID,
                    Name = info.Name,
                    GameCategoryIndex = info.GameCategoryIndex,
                    PackageName = info.PackageName,
                    VideoFilePath = info.VideoFilePath,
                    GameType = info.GameType,
                    VideoFormat = info.VideoFormat,
                    Duration = info.Duration,
                    ShowOrder = info.ShowOrder,
                    IsShow = info.IsShow,
                    IsPlayAction = info.IsPlayAction,
                    ActionFilePath = info.ActionFilePath
                });
            }

            OnPropertyChanged(nameof(Games));
            GamesUpdated?.Invoke(DateTime.Now);
        }

        public GameInfo[] GetGameInfos()
        {
            var gameInfos = new GameInfo[_games.Count];

            for (int i = 0; i < _games.Count; i++)
            {
                var game = _games[i];
                gameInfos[i] = new GameInfo
                {
                    ID = game.ID,
                    Name = game.Name,
                    GameCategoryIndex = game.GameCategoryIndex,
                    PackageName = game.PackageName,
                    VideoFilePath = game.VideoFilePath,
                    GameType = game.GameType,
                    VideoFormat = game.VideoFormat,
                    Duration = game.Duration,
                    ShowOrder = game.ShowOrder,
                    IsShow = game.IsShow,
                    IsPlayAction = game.IsPlayAction,
                    ActionFilePath = game.ActionFilePath
                };
            }

            return gameInfos;
        }

        #endregion
    }
}
