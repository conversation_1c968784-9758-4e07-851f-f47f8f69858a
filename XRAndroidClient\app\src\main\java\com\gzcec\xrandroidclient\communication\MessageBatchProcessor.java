package com.gzcec.xrandroidclient.communication;

import android.util.Log;
import com.google.gson.Gson;
import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.client.MqttGameClient;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * MQTT消息批处理器
 * 优化消息发送效率，减少网络开销
 */
public class MessageBatchProcessor {
    private static final String TAG = "MessageBatchProcessor";
    
    // 批处理配置
    private static final int DEFAULT_BATCH_SIZE = 10;        // 默认批处理大小
    private static final int DEFAULT_BATCH_TIMEOUT = 100;    // 默认批处理超时(ms)
    private static final int MAX_QUEUE_SIZE = 1000;          // 最大队列大小
    private static final int THREAD_POOL_SIZE = 2;           // 线程池大小
    
    // 批处理队列和配置
    private final BlockingQueue<MessageBatch> messageQueue;
    private final ExecutorService executorService;
    private final ScheduledExecutorService scheduledExecutor;
    private final AtomicInteger queueSize;
    
    // 当前批次
    private volatile MessageBatch currentBatch;
    private final Object batchLock = new Object();
    
    // 配置参数
    private int batchSize;
    private int batchTimeout;
    private MqttGameClient mqttClient;
    private Gson gson;
    
    // 统计信息
    private final AtomicInteger totalMessages = new AtomicInteger(0);
    private final AtomicInteger batchedMessages = new AtomicInteger(0);
    private final AtomicInteger droppedMessages = new AtomicInteger(0);
    
    /**
     * 构造函数
     */
    public MessageBatchProcessor(MqttGameClient mqttClient) {
        this.mqttClient = mqttClient;
        this.gson = new Gson();
        this.batchSize = DEFAULT_BATCH_SIZE;
        this.batchTimeout = DEFAULT_BATCH_TIMEOUT;
        
        this.messageQueue = new LinkedBlockingQueue<>(MAX_QUEUE_SIZE);
        this.queueSize = new AtomicInteger(0);
        
        // 创建线程池
        this.executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE, 
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "MessageBatch-" + threadNumber.getAndIncrement());
                    t.setDaemon(true);
                    return t;
                }
            });
            
        this.scheduledExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> {
                Thread t = new Thread(r, "MessageBatch-Scheduler");
                t.setDaemon(true);
                return t;
            });
        
        // 启动批处理工作线程
        startBatchProcessor();
        
        // 启动定时刷新任务
        startBatchFlushScheduler();
        
        Log.i(TAG, "消息批处理器已启动 - 批大小: " + batchSize + ", 超时: " + batchTimeout + "ms");
    }
    
    /**
     * 配置批处理参数
     */
    public void configure(int batchSize, int batchTimeout) {
        this.batchSize = Math.max(1, Math.min(batchSize, 100));
        this.batchTimeout = Math.max(10, Math.min(batchTimeout, 5000));
        Log.i(TAG, "批处理配置已更新 - 批大小: " + this.batchSize + ", 超时: " + this.batchTimeout + "ms");
    }
    
    /**
     * 添加消息到批处理队列
     */
    public boolean addMessage(String topic, BaseMessage message, int qos) {
        if (message == null || topic == null) {
            Log.w(TAG, "忽略空消息或主题");
            return false;
        }
        
        totalMessages.incrementAndGet();
        
        try {
            MessageItem item = new MessageItem(topic, message, qos, System.currentTimeMillis());
            
            synchronized (batchLock) {
                // 如果当前批次不存在，创建新批次
                if (currentBatch == null) {
                    currentBatch = new MessageBatch();
                }
                
                // 添加消息到当前批次
                currentBatch.addMessage(item);
                
                // 如果批次已满，提交处理
                if (currentBatch.size() >= batchSize) {
                    submitBatch(currentBatch);
                    currentBatch = null;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "添加消息到批处理队列失败", e);
            droppedMessages.incrementAndGet();
            return false;
        }
    }
    
    /**
     * 立即发送消息（绕过批处理）
     */
    public boolean sendImmediately(String topic, BaseMessage message, int qos) {
        try {
            String payload = gson.toJson(message);
            // 这里应该直接调用MQTT客户端发送
            // mqttClient.publish(topic, payload, qos);
            Log.d(TAG, "立即发送消息到主题: " + topic);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "立即发送消息失败", e);
            return false;
        }
    }
    
    /**
     * 强制刷新当前批次
     */
    public void flush() {
        synchronized (batchLock) {
            if (currentBatch != null && !currentBatch.isEmpty()) {
                submitBatch(currentBatch);
                currentBatch = null;
            }
        }
    }
    
    /**
     * 提交批次到处理队列
     */
    private void submitBatch(MessageBatch batch) {
        if (batch == null || batch.isEmpty()) {
            return;
        }
        
        try {
            if (messageQueue.offer(batch)) {
                queueSize.incrementAndGet();
                batchedMessages.addAndGet(batch.size());
                Log.d(TAG, "批次已提交 - 消息数: " + batch.size() + ", 队列大小: " + queueSize.get());
            } else {
                Log.w(TAG, "批处理队列已满，丢弃批次 - 消息数: " + batch.size());
                droppedMessages.addAndGet(batch.size());
            }
        } catch (Exception e) {
            Log.e(TAG, "提交批次失败", e);
            droppedMessages.addAndGet(batch.size());
        }
    }
    
    /**
     * 启动批处理工作线程
     */
    private void startBatchProcessor() {
        executorService.submit(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    MessageBatch batch = messageQueue.take();
                    queueSize.decrementAndGet();
                    processBatch(batch);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "处理批次失败", e);
                }
            }
        });
    }
    
    /**
     * 启动定时刷新调度器
     */
    private void startBatchFlushScheduler() {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            try {
                synchronized (batchLock) {
                    if (currentBatch != null && !currentBatch.isEmpty()) {
                        long age = System.currentTimeMillis() - currentBatch.getCreatedTime();
                        if (age >= batchTimeout) {
                            Log.d(TAG, "批次超时，强制刷新 - 消息数: " + currentBatch.size() + ", 年龄: " + age + "ms");
                            submitBatch(currentBatch);
                            currentBatch = null;
                        }
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "定时刷新失败", e);
            }
        }, batchTimeout, batchTimeout / 2, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 处理批次
     */
    private void processBatch(MessageBatch batch) {
        if (batch == null || batch.isEmpty()) {
            return;
        }
        
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failureCount = 0;
        
        try {
            // 按主题分组处理消息
            for (String topic : batch.getTopics()) {
                List<MessageItem> topicMessages = batch.getMessagesByTopic(topic);
                
                for (MessageItem item : topicMessages) {
                    try {
                        String payload = gson.toJson(item.message);
                        // 这里应该调用MQTT客户端发送消息
                        // mqttClient.publish(topic, payload, item.qos);
                        successCount++;
                    } catch (Exception e) {
                        Log.e(TAG, "发送消息失败 - 主题: " + topic, e);
                        failureCount++;
                    }
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            Log.d(TAG, String.format("批次处理完成 - 成功: %d, 失败: %d, 耗时: %dms", 
                successCount, failureCount, duration));
                
        } catch (Exception e) {
            Log.e(TAG, "批次处理异常", e);
        }
    }
    
    /**
     * 获取统计信息
     */
    public BatchStatistics getStatistics() {
        return new BatchStatistics(
            totalMessages.get(),
            batchedMessages.get(),
            droppedMessages.get(),
            queueSize.get()
        );
    }
    
    /**
     * 关闭批处理器
     */
    public void shutdown() {
        try {
            // 刷新剩余消息
            flush();
            
            // 关闭线程池
            scheduledExecutor.shutdown();
            executorService.shutdown();
            
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
            
            Log.i(TAG, "消息批处理器已关闭");
            
        } catch (Exception e) {
            Log.e(TAG, "关闭批处理器失败", e);
        }
    }
    
    /**
     * 消息项类
     */
    private static class MessageItem {
        final String topic;
        final BaseMessage message;
        final int qos;
        final long timestamp;
        
        MessageItem(String topic, BaseMessage message, int qos, long timestamp) {
            this.topic = topic;
            this.message = message;
            this.qos = qos;
            this.timestamp = timestamp;
        }
    }
    
    /**
     * 消息批次类
     */
    private static class MessageBatch {
        private final List<MessageItem> messages;
        private final long createdTime;
        
        MessageBatch() {
            this.messages = new ArrayList<>();
            this.createdTime = System.currentTimeMillis();
        }
        
        void addMessage(MessageItem item) {
            messages.add(item);
        }
        
        int size() {
            return messages.size();
        }
        
        boolean isEmpty() {
            return messages.isEmpty();
        }
        
        long getCreatedTime() {
            return createdTime;
        }
        
        List<String> getTopics() {
            return messages.stream()
                .map(item -> item.topic)
                .distinct()
                .collect(java.util.stream.Collectors.toList());
        }
        
        List<MessageItem> getMessagesByTopic(String topic) {
            return messages.stream()
                .filter(item -> topic.equals(item.topic))
                .collect(java.util.stream.Collectors.toList());
        }
    }
    
    /**
     * 批处理统计信息
     */
    public static class BatchStatistics {
        public final int totalMessages;
        public final int batchedMessages;
        public final int droppedMessages;
        public final int queueSize;
        
        BatchStatistics(int total, int batched, int dropped, int queue) {
            this.totalMessages = total;
            this.batchedMessages = batched;
            this.droppedMessages = dropped;
            this.queueSize = queue;
        }
        
        @Override
        public String toString() {
            return String.format("BatchStats{total=%d, batched=%d, dropped=%d, queue=%d}", 
                totalMessages, batchedMessages, droppedMessages, queueSize);
        }
    }
}
