﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using XRSvc.DataPack;
using XRSvc.DataSource;
using System.Linq; // Added for FirstOrDefault

namespace XRSvc.ViewModels
{
    public class GameViewModel : BaseViewModel
    {
        #region Fields

        private ObservableCollection<GameSource> _source;
        private ICommand _saveGameNameCommand;
        private GameSource _selectedGame;

        #endregion

        #region Properties

        /// <summary>
        /// 游戏集合，供界面绑定
        /// </summary>
        public ObservableCollection<GameSource> Source
        {
            get => _source;
            set
            {
                _source = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前选中的游戏
        /// </summary>
        public GameSource SelectedGame
        {
            get => _selectedGame;
            set
            {
                if (_selectedGame != value)
                {
                    _selectedGame = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(SelectedGameId));
                }
            }
        }

        /// <summary>
        /// 当前选中游戏的ID
        /// </summary>
        public int SelectedGameId
        {
            get => _selectedGame?.ID ?? 0;
            set
            {
                if (value != 0)
                {
                    var game = _source?.FirstOrDefault(g => g.ID == value);
                    if (game != null && _selectedGame != game)
                    {
                        SelectedGame = game;
                    }
                }
            }
        }

        // 通过索引获取游戏
        public GameSource this[int index]
        {
            get => _source[index];
        }

        // 保存游戏名称命令
        public ICommand SaveGameNameCommand
        {
            get => _saveGameNameCommand;
            set
            {
                _saveGameNameCommand = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Events

        // 游戏集合更新事件
        public event Action<DateTime> GamesUpdated;

        #endregion

        #region Constructor

        // 构造函数，初始化游戏集合
        public GameViewModel()
        {
            _source = new ObservableCollection<GameSource>();
        }

        #endregion

        #region Methods

        /// <summary>
        /// 设置默认选中的游戏
        /// </summary>
        /// <param name="gameId">要选中的游戏ID</param>
        public void SetDefaultSelection(int gameId)
        {
            if (gameId != 0 && _source != null)
            {
                var game = _source.FirstOrDefault(g => g.ID == gameId);
                if (game != null)
                {
                    SelectedGame = game;
                }
                else if (_source.Count > 0)
                {
                    // 如果指定的游戏不存在，选择第一个游戏
                    SelectedGame = _source[0];
                }
            }
            else if (_source != null && _source.Count > 0)
            {
                // 如果没有指定游戏ID，选择第一个游戏
                SelectedGame = _source[0];
            }
        }

        // 重置所有游戏信息
        public void ResetSource()
        {
            RunOnUIThread(() =>
            {
                foreach (var game in _source)
                {
                    game.Name = string.Empty;
                    game.PackageName = string.Empty;
                    game.VideoFilePath = string.Empty;
                    game.GameType = GameType.None;
                    game.VideoFormat = VideoFormat.TYPE_UNKNOWN;
                    game.Duration = 0;
                    game.ShowOrder = 0;
                    game.IsShow = false;
                    game.IsPlayAction = false;
                    game.ActionFilePath = string.Empty;
                }
                OnPropertyChanged(nameof(Source));
            });
        }

        /// <summary>
        /// 批量更新游戏集合（数据源），自动切换到UI线程，界面自动刷新。
        /// </summary>
        /// <param name="gameInfos">新的游戏信息数组</param>
        /// <remarks>
        /// 该方法会清空现有游戏集合，并根据传入的数组重新填充，
        /// 保证操作在UI线程执行，适用于外部数据同步、批量导入等场景。
        /// 更新后会自动恢复原有选中项，并触发GamesUpdated事件，界面会自动响应刷新。
        /// </remarks>
        public void UpdateSource(GameInfo[] gameInfos)
        {
            RunOnUIThread(() =>
            {
                var currentSelectedId = SelectedGameId;
                _source.Clear();
                foreach (var info in gameInfos)
                {
                    _source.Add(new GameSource
                    {
                        ID = info.ID,
                        Name = info.Name,
                        GameCategoryIndex = info.GameCategoryIndex,
                        PackageName = info.PackageName,
                        VideoFilePath = info.VideoFilePath,
                        GameType = info.GameType,
                        VideoFormat = info.VideoFormat,
                        Duration = info.Duration,
                        ShowOrder = info.ShowOrder,
                        IsShow = info.IsShow,
                        IsPlayAction = info.IsPlayAction,
                        ActionFilePath = info.ActionFilePath
                    });
                }
                OnPropertyChanged(nameof(Source));
                // 恢复选中状态
                SetDefaultSelection(currentSelectedId);
                GamesUpdated?.Invoke(DateTime.Now);
            });
        }

        // 获取游戏信息数组
        public GameInfo[] GetSourceInfos()
        {
            var gameInfos = new GameInfo[_source.Count];
            for (int i = 0; i < _source.Count; i++)
            {
                var game = _source[i];
                gameInfos[i] = new GameInfo
                {
                    ID = game.ID,
                    Name = game.Name,
                    GameCategoryIndex = game.GameCategoryIndex,
                    PackageName = game.PackageName,
                    VideoFilePath = game.VideoFilePath,
                    GameType = game.GameType,
                    VideoFormat = game.VideoFormat,
                    Duration = game.Duration,
                    ShowOrder = game.ShowOrder,
                    IsShow = game.IsShow,
                    IsPlayAction = game.IsPlayAction,
                    ActionFilePath = game.ActionFilePath
                };
            }
            return gameInfos;
        }

        // 异步加载默认游戏
        public async Task LoadDefaultGamesAsync(CancellationToken cancellationToken)
        {
            IsLoading = true;
            var currentSelectedId = SelectedGameId;

            var defaultList = new[]
            {
                new GameInfo {
                    ID = 10001, Name = "唐韵", GameCategoryIndex = 1, GameType = GameType.Video, VideoFormat = VideoFormat.TYPE_3D360_TB,
                    VideoFilePath = "/唐韵/唐韵.mp4", Duration = 125, ShowOrder = 50, IsShow = true, IsPlayAction = true, ActionFilePath = "/唐韵/10001.jdz"
                },
                new GameInfo {
                    ID = 10002, Name = "秦陵", GameCategoryIndex = 1, GameType = GameType.Video, VideoFormat = VideoFormat.TYPE_3D360_TB,
                    VideoFilePath = "/秦陵/秦陵.mp4", Duration = 125, ShowOrder = 50, IsShow = true, IsPlayAction = true, ActionFilePath = "/秦陵/01.jdz"
                },
                new GameInfo {
                    ID = 10003, Name = "长征", GameCategoryIndex = 1, GameType = GameType.Video, VideoFormat = VideoFormat.TYPE_3D360_TB,
                    VideoFilePath = "/长征/长征.mp4", Duration = 125, ShowOrder = 50, IsShow = true, IsPlayAction = true, ActionFilePath = "/长征/01.jdz"
                }
            };

            // 在后台线程准备数据
            var gameSources = new List<GameSource>();
            foreach (var info in defaultList)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    IsLoading = false;
                    return;
                }

                await Task.Delay(50, cancellationToken); // 模拟异步操作

                gameSources.Add(new GameSource
                {
                    ID = info.ID,
                    Name = info.Name,
                    GameCategoryIndex = info.GameCategoryIndex,
                    PackageName = info.PackageName,
                    VideoFilePath = info.VideoFilePath,
                    GameType = info.GameType,
                    VideoFormat = info.VideoFormat,
                    Duration = info.Duration,
                    ShowOrder = info.ShowOrder,
                    IsShow = info.IsShow,
                    IsPlayAction = info.IsPlayAction,
                    ActionFilePath = info.ActionFilePath
                });
            }

            // 在UI线程更新界面
            await RunOnUIThreadAsync(() =>
            {
                _source.Clear();
                foreach (var gameSource in gameSources)
                {
                    _source.Add(gameSource);
                }

                OnPropertyChanged(nameof(Source));

                // 恢复选中状态
                SetDefaultSelection(currentSelectedId);

                GamesUpdated?.Invoke(DateTime.Now);
                IsLoading = false;
            });
        }

        #endregion
    }
}
