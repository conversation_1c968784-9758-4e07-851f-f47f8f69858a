﻿using System.Windows;
using System.Windows.Controls;

namespace XRSvc.CustomControl
{
    public partial class GameStyle : UserControl
    {
        public GameStyle()
        {
            InitializeComponent();
            this.DataContextChanged += GameStyle_DataContextChanged;
        }

        private void GameStyle_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            this.DataContext = e.New<PERSON>alue;
        }
    }
}
