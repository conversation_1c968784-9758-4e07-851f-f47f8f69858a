using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using XRSvc.Utils;

namespace XRSvc.Config
{
    /// <summary>
    /// 分层配置管理器
    /// 支持环境变量、配置热更新、配置验证等功能
    /// </summary>
    public class ConfigurationManager : IDisposable
    {
        private static readonly string TAG = "ConfigurationManager";
        
        // 单例实例
        private static volatile ConfigurationManager _instance;
        private static readonly object _lock = new object();
        
        // 配置层级
        private readonly Dictionary<ConfigLayer, Dictionary<string, object>> _configLayers;
        private readonly Dictionary<string, ConfigWatcher> _watchers;
        private readonly List<IConfigurationChangeListener> _listeners;
        
        // 配置文件路径
        private const string DEFAULT_CONFIG_PATH = "Config/default.json";
        private const string USER_CONFIG_PATH = "Config/user.json";
        private const string RUNTIME_CONFIG_PATH = "Config/runtime.json";
        
        // 配置验证器
        private readonly Dictionary<string, IConfigValidator> _validators;
        
        // 配置缓存
        private readonly Dictionary<string, object> _cache;
        private readonly object _cacheLock = new object();
        
        /// <summary>
        /// 配置层级枚举
        /// </summary>
        public enum ConfigLayer
        {
            Default = 0,    // 默认配置
            Environment = 1, // 环境变量
            User = 2,       // 用户配置
            Runtime = 3     // 运行时配置
        }
        
        /// <summary>
        /// 配置变更监听器接口
        /// </summary>
        public interface IConfigurationChangeListener
        {
            void OnConfigurationChanged(string key, object oldValue, object newValue);
        }
        
        /// <summary>
        /// 配置验证器接口
        /// </summary>
        public interface IConfigValidator
        {
            bool Validate(object value, out string errorMessage);
        }
        
        /// <summary>
        /// 私有构造函数
        /// </summary>
        private ConfigurationManager()
        {
            _configLayers = new Dictionary<ConfigLayer, Dictionary<string, object>>();
            _watchers = new Dictionary<string, ConfigWatcher>();
            _listeners = new List<IConfigurationChangeListener>();
            _validators = new Dictionary<string, IConfigValidator>();
            _cache = new Dictionary<string, object>();
            
            // 初始化配置层
            foreach (ConfigLayer layer in Enum.GetValues(typeof(ConfigLayer)))
            {
                _configLayers[layer] = new Dictionary<string, object>();
            }
            
            // 注册内置验证器
            RegisterBuiltinValidators();
        }
        
        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static ConfigurationManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ConfigurationManager();
                        }
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// 初始化配置管理器
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                UnifiedLogger.Info(TAG, "开始初始化配置管理器");
                
                // 加载默认配置
                await LoadDefaultConfigurationAsync();
                
                // 加载环境变量配置
                LoadEnvironmentConfiguration();
                
                // 加载用户配置
                await LoadUserConfigurationAsync();
                
                // 启动配置文件监控
                StartConfigurationWatching();
                
                UnifiedLogger.Info(TAG, "配置管理器初始化完成");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "配置管理器初始化失败", ex);
                throw;
            }
        }
        
        /// <summary>
        /// 获取配置值
        /// </summary>
        public T GetValue<T>(string key, T defaultValue = default(T))
        {
            try
            {
                lock (_cacheLock)
                {
                    // 检查缓存
                    if (_cache.TryGetValue(key, out var cachedValue))
                    {
                        return ConvertValue<T>(cachedValue, defaultValue);
                    }
                }
                
                // 按优先级查找配置值
                object value = null;
                foreach (ConfigLayer layer in Enum.GetValues(typeof(ConfigLayer)))
                {
                    if (_configLayers[layer].TryGetValue(key, out value))
                    {
                        break;
                    }
                }
                
                if (value == null)
                {
                    value = defaultValue;
                }
                
                // 缓存结果
                lock (_cacheLock)
                {
                    _cache[key] = value;
                }
                
                return ConvertValue<T>(value, defaultValue);
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, $"获取配置值失败: {key}", ex);
                return defaultValue;
            }
        }
        
        /// <summary>
        /// 设置配置值
        /// </summary>
        public async Task<bool> SetValueAsync(string key, object value, ConfigLayer layer = ConfigLayer.Runtime)
        {
            try
            {
                // 验证配置值
                if (!ValidateValue(key, value, out string errorMessage))
                {
                    UnifiedLogger.Warning(TAG, $"配置值验证失败: {key} - {errorMessage}");
                    return false;
                }
                
                // 获取旧值
                var oldValue = GetValue<object>(key);
                
                // 设置新值
                _configLayers[layer][key] = value;
                
                // 清除缓存
                lock (_cacheLock)
                {
                    _cache.Remove(key);
                }
                
                // 保存到文件（如果是用户或运行时配置）
                if (layer == ConfigLayer.User)
                {
                    await SaveUserConfigurationAsync();
                }
                else if (layer == ConfigLayer.Runtime)
                {
                    await SaveRuntimeConfigurationAsync();
                }
                
                // 通知监听器
                NotifyConfigurationChanged(key, oldValue, value);
                
                UnifiedLogger.Debug(TAG, $"配置值已更新: {key} = {value} (层级: {layer})");
                return true;
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, $"设置配置值失败: {key}", ex);
                return false;
            }
        }
        
        /// <summary>
        /// 注册配置变更监听器
        /// </summary>
        public void RegisterListener(IConfigurationChangeListener listener)
        {
            if (listener != null && !_listeners.Contains(listener))
            {
                _listeners.Add(listener);
            }
        }
        
        /// <summary>
        /// 注销配置变更监听器
        /// </summary>
        public void UnregisterListener(IConfigurationChangeListener listener)
        {
            _listeners.Remove(listener);
        }
        
        /// <summary>
        /// 注册配置验证器
        /// </summary>
        public void RegisterValidator(string key, IConfigValidator validator)
        {
            _validators[key] = validator;
        }
        
        /// <summary>
        /// 重新加载配置
        /// </summary>
        public async Task ReloadConfigurationAsync()
        {
            try
            {
                UnifiedLogger.Info(TAG, "开始重新加载配置");
                
                // 清除缓存
                lock (_cacheLock)
                {
                    _cache.Clear();
                }
                
                // 重新加载所有配置层
                await LoadDefaultConfigurationAsync();
                LoadEnvironmentConfiguration();
                await LoadUserConfigurationAsync();
                
                UnifiedLogger.Info(TAG, "配置重新加载完成");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "重新加载配置失败", ex);
                throw;
            }
        }
        
        /// <summary>
        /// 获取所有配置
        /// </summary>
        public Dictionary<string, object> GetAllConfigurations()
        {
            var result = new Dictionary<string, object>();
            
            // 按优先级合并所有配置层
            foreach (ConfigLayer layer in Enum.GetValues(typeof(ConfigLayer)))
            {
                foreach (var kvp in _configLayers[layer])
                {
                    result[kvp.Key] = kvp.Value;
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 加载默认配置
        /// </summary>
        private async Task LoadDefaultConfigurationAsync()
        {
            try
            {
                if (File.Exists(DEFAULT_CONFIG_PATH))
                {
                    var json = await File.ReadAllTextAsync(DEFAULT_CONFIG_PATH);
                    var config = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
                    
                    _configLayers[ConfigLayer.Default].Clear();
                    foreach (var kvp in config)
                    {
                        _configLayers[ConfigLayer.Default][kvp.Key] = kvp.Value;
                    }
                    
                    UnifiedLogger.Debug(TAG, $"默认配置已加载: {config.Count} 项");
                }
                else
                {
                    // 创建默认配置文件
                    await CreateDefaultConfigurationAsync();
                }
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "加载默认配置失败", ex);
            }
        }
        
        /// <summary>
        /// 加载环境变量配置
        /// </summary>
        private void LoadEnvironmentConfiguration()
        {
            try
            {
                _configLayers[ConfigLayer.Environment].Clear();
                
                // 加载以 XR_ 开头的环境变量
                foreach (var envVar in Environment.GetEnvironmentVariables().Keys)
                {
                    string key = envVar.ToString();
                    if (key.StartsWith("XR_"))
                    {
                        string configKey = key.Substring(3).ToLower().Replace('_', '.');
                        string value = Environment.GetEnvironmentVariable(key);
                        _configLayers[ConfigLayer.Environment][configKey] = value;
                    }
                }
                
                UnifiedLogger.Debug(TAG, $"环境变量配置已加载: {_configLayers[ConfigLayer.Environment].Count} 项");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "加载环境变量配置失败", ex);
            }
        }
        
        /// <summary>
        /// 加载用户配置
        /// </summary>
        private async Task LoadUserConfigurationAsync()
        {
            try
            {
                if (File.Exists(USER_CONFIG_PATH))
                {
                    var json = await File.ReadAllTextAsync(USER_CONFIG_PATH);
                    var config = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
                    
                    _configLayers[ConfigLayer.User].Clear();
                    foreach (var kvp in config)
                    {
                        _configLayers[ConfigLayer.User][kvp.Key] = kvp.Value;
                    }
                    
                    UnifiedLogger.Debug(TAG, $"用户配置已加载: {config.Count} 项");
                }
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "加载用户配置失败", ex);
            }
        }
        
        /// <summary>
        /// 创建默认配置文件
        /// </summary>
        private async Task CreateDefaultConfigurationAsync()
        {
            try
            {
                var defaultConfig = new Dictionary<string, object>
                {
                    ["mqtt.host"] = "localhost",
                    ["mqtt.port"] = 1883,
                    ["mqtt.timeout"] = 30000,
                    ["mqtt.keepalive"] = 60,
                    ["log.level"] = "INFO",
                    ["log.maxfiles"] = 10,
                    ["log.maxsize"] = 10485760, // 10MB
                    ["device.maxcount"] = 30,
                    ["device.heartbeat"] = 30000,
                    ["game.timeout"] = 300000,
                    ["ui.language"] = "zh-CN",
                    ["ui.theme"] = "dark"
                };
                
                // 确保目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(DEFAULT_CONFIG_PATH));
                
                var json = JsonConvert.SerializeObject(defaultConfig, Formatting.Indented);
                await File.WriteAllTextAsync(DEFAULT_CONFIG_PATH, json);
                
                // 加载到配置层
                foreach (var kvp in defaultConfig)
                {
                    _configLayers[ConfigLayer.Default][kvp.Key] = kvp.Value;
                }
                
                UnifiedLogger.Info(TAG, "默认配置文件已创建");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "创建默认配置文件失败", ex);
            }
        }
        
        /// <summary>
        /// 保存用户配置
        /// </summary>
        private async Task SaveUserConfigurationAsync()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(USER_CONFIG_PATH));
                
                var json = JsonConvert.SerializeObject(_configLayers[ConfigLayer.User], Formatting.Indented);
                await File.WriteAllTextAsync(USER_CONFIG_PATH, json);
                
                UnifiedLogger.Debug(TAG, "用户配置已保存");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "保存用户配置失败", ex);
            }
        }
        
        /// <summary>
        /// 保存运行时配置
        /// </summary>
        private async Task SaveRuntimeConfigurationAsync()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(RUNTIME_CONFIG_PATH));
                
                var json = JsonConvert.SerializeObject(_configLayers[ConfigLayer.Runtime], Formatting.Indented);
                await File.WriteAllTextAsync(RUNTIME_CONFIG_PATH, json);
                
                UnifiedLogger.Debug(TAG, "运行时配置已保存");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "保存运行时配置失败", ex);
            }
        }
        
        /// <summary>
        /// 启动配置文件监控
        /// </summary>
        private void StartConfigurationWatching()
        {
            try
            {
                var configFiles = new[] { DEFAULT_CONFIG_PATH, USER_CONFIG_PATH };
                
                foreach (var configFile in configFiles)
                {
                    if (File.Exists(configFile))
                    {
                        var watcher = new ConfigWatcher(configFile, OnConfigFileChanged);
                        _watchers[configFile] = watcher;
                        watcher.Start();
                    }
                }
                
                UnifiedLogger.Debug(TAG, $"配置文件监控已启动: {_watchers.Count} 个文件");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "启动配置文件监控失败", ex);
            }
        }
        
        /// <summary>
        /// 配置文件变更回调
        /// </summary>
        private async void OnConfigFileChanged(string filePath)
        {
            try
            {
                UnifiedLogger.Info(TAG, $"检测到配置文件变更: {filePath}");
                
                // 延迟一点时间，确保文件写入完成
                await Task.Delay(500);
                
                // 重新加载配置
                await ReloadConfigurationAsync();
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, $"处理配置文件变更失败: {filePath}", ex);
            }
        }
        
        /// <summary>
        /// 验证配置值
        /// </summary>
        private bool ValidateValue(string key, object value, out string errorMessage)
        {
            errorMessage = null;
            
            if (_validators.TryGetValue(key, out var validator))
            {
                return validator.Validate(value, out errorMessage);
            }
            
            return true;
        }
        
        /// <summary>
        /// 转换配置值类型
        /// </summary>
        private T ConvertValue<T>(object value, T defaultValue)
        {
            if (value == null)
                return defaultValue;
            
            try
            {
                if (value is T directValue)
                    return directValue;
                
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        
        /// <summary>
        /// 通知配置变更
        /// </summary>
        private void NotifyConfigurationChanged(string key, object oldValue, object newValue)
        {
            foreach (var listener in _listeners)
            {
                try
                {
                    listener.OnConfigurationChanged(key, oldValue, newValue);
                }
                catch (Exception ex)
                {
                    UnifiedLogger.Error(TAG, $"通知配置变更监听器失败: {listener.GetType().Name}", ex);
                }
            }
        }
        
        /// <summary>
        /// 注册内置验证器
        /// </summary>
        private void RegisterBuiltinValidators()
        {
            // MQTT端口验证器
            RegisterValidator("mqtt.port", new PortValidator());
            
            // 日志级别验证器
            RegisterValidator("log.level", new LogLevelValidator());
            
            // 设备数量验证器
            RegisterValidator("device.maxcount", new RangeValidator(1, 100));
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 停止文件监控
                foreach (var watcher in _watchers.Values)
                {
                    watcher?.Dispose();
                }
                _watchers.Clear();
                
                // 清除监听器
                _listeners.Clear();
                
                UnifiedLogger.Info(TAG, "配置管理器已释放");
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error(TAG, "释放配置管理器失败", ex);
            }
        }
    }

    /// <summary>
    /// 配置文件监控器
    /// </summary>
    public class ConfigWatcher : IDisposable
    {
        private readonly FileSystemWatcher _watcher;
        private readonly Action<string> _onChanged;
        private readonly string _filePath;
        private DateTime _lastWriteTime;

        public ConfigWatcher(string filePath, Action<string> onChanged)
        {
            _filePath = filePath;
            _onChanged = onChanged;
            _lastWriteTime = File.GetLastWriteTime(filePath);

            var directory = Path.GetDirectoryName(filePath);
            var fileName = Path.GetFileName(filePath);

            _watcher = new FileSystemWatcher(directory, fileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                EnableRaisingEvents = false
            };

            _watcher.Changed += OnFileChanged;
        }

        public void Start()
        {
            _watcher.EnableRaisingEvents = true;
        }

        public void Stop()
        {
            _watcher.EnableRaisingEvents = false;
        }

        private void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                var currentWriteTime = File.GetLastWriteTime(_filePath);
                if (currentWriteTime != _lastWriteTime)
                {
                    _lastWriteTime = currentWriteTime;
                    _onChanged?.Invoke(_filePath);
                }
            }
            catch (Exception ex)
            {
                UnifiedLogger.Error("ConfigWatcher", $"文件变更处理失败: {_filePath}", ex);
            }
        }

        public void Dispose()
        {
            _watcher?.Dispose();
        }
    }

    /// <summary>
    /// 端口验证器
    /// </summary>
    public class PortValidator : ConfigurationManager.IConfigValidator
    {
        public bool Validate(object value, out string errorMessage)
        {
            errorMessage = null;

            if (int.TryParse(value?.ToString(), out int port))
            {
                if (port >= 1 && port <= 65535)
                {
                    return true;
                }
                errorMessage = "端口号必须在1-65535之间";
            }
            else
            {
                errorMessage = "端口号必须是有效的整数";
            }

            return false;
        }
    }

    /// <summary>
    /// 日志级别验证器
    /// </summary>
    public class LogLevelValidator : ConfigurationManager.IConfigValidator
    {
        private readonly string[] _validLevels = { "DEBUG", "INFO", "WARNING", "ERROR" };

        public bool Validate(object value, out string errorMessage)
        {
            errorMessage = null;
            string level = value?.ToString()?.ToUpper();

            if (Array.IndexOf(_validLevels, level) >= 0)
            {
                return true;
            }

            errorMessage = $"日志级别必须是: {string.Join(", ", _validLevels)}";
            return false;
        }
    }

    /// <summary>
    /// 范围验证器
    /// </summary>
    public class RangeValidator : ConfigurationManager.IConfigValidator
    {
        private readonly int _min;
        private readonly int _max;

        public RangeValidator(int min, int max)
        {
            _min = min;
            _max = max;
        }

        public bool Validate(object value, out string errorMessage)
        {
            errorMessage = null;

            if (int.TryParse(value?.ToString(), out int intValue))
            {
                if (intValue >= _min && intValue <= _max)
                {
                    return true;
                }
                errorMessage = $"值必须在{_min}-{_max}之间";
            }
            else
            {
                errorMessage = "值必须是有效的整数";
            }

            return false;
        }
    }
}
