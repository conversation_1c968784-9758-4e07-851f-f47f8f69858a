﻿<UserControl x:Class="XRSvc.CustomControl.DeviceStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="90" d:DesignWidth="180">
    <UserControl.Resources>
        <!-- 卡片阴影效果 -->
        <DropShadowEffect x:Key="CardShadow" Color="Black" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
        
        <!-- 在线状态指示器样式 -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="10"/>
            <Setter Property="Height" Value="10"/>
            <Setter Property="Fill" Value="#FF6B6B6B"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsOnline}" Value="True">
                    <Setter Property="Fill" Value="#FF4CAF50"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 电池进度条样式 -->
        <Style x:Key="ModernBatteryStyle" TargetType="ProgressBar">
            <Setter Property="Background" Value="#FFE0E0E0"/>
            <Setter Property="Foreground" Value="#FF4CAF50"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding BatteryLevel}" Value="0">
                    <Setter Property="Foreground" Value="#FFF44336"/>
                </DataTrigger>
                <MultiDataTrigger>
                    <MultiDataTrigger.Conditions>
                        <Condition Binding="{Binding BatteryLevel}" Value="0"/>
                        <Condition Binding="{Binding IsOnline}" Value="True"/>
                    </MultiDataTrigger.Conditions>
                    <Setter Property="Foreground" Value="#FFFF9800"/>
                </MultiDataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 设备类型图标样式 -->
        <Style x:Key="DeviceIconStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="Foreground" Value="#FF666666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 主标题样式 -->
        <Style x:Key="DeviceTitleStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#FF333333"/>
            <Setter Property="Margin" Value="0,0,0,2"/>
        </Style>

        <!-- 副标题样式 -->
        <Style x:Key="DeviceSubtitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#FF888888"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <!-- 状态文本样式 -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#FF666666"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 转换器实例 -->
        <local:OnlineStatusConverter x:Key="OnlineStatusConverter"/>
    </UserControl.Resources>

    <!-- 主容器 -->
    <Border Background="White" 
            BorderBrush="#FFE0E0E0" 
            BorderThickness="1" 
            CornerRadius="6" 
            Margin="0"
            Effect="{StaticResource CardShadow}">
        
        <Grid Margin="6">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 顶部：勾选框、设备图标和状态 -->
            <Grid Grid.Row="0" Margin="0,0,0,4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 勾选框 -->
                <CheckBox Grid.Column="0" 
                          x:Name="CheckBox_Select" 
                          IsChecked="{Binding IsEnabled, Mode=TwoWay}" 
                          IsEnabled="{Binding IsOnline}"
                          VerticalAlignment="Center" 
                          Margin="0,0,6,0"/>

                <!-- 设备图标 -->
                <Border Grid.Column="1" 
                        Background="#FFF5F5F5" 
                        CornerRadius="4" 
                        Width="28" 
                        Height="28" 
                        Margin="0,0,8,0">
                    <TextBlock Text="&#xE770;" FontFamily="Segoe MDL2 Assets" FontSize="18" Foreground="#FF666666" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>

                <!-- 设备ID（如有需要） -->
                <StackPanel Grid.Column="2" VerticalAlignment="Center">
                    <TextBlock Text="{Binding ID}" FontWeight="SemiBold" FontSize="15" Foreground="#FF333333" Margin="0,0,0,0"/>
                </StackPanel>

                <!-- 在线状态指示器和文字（彩色） -->
                <StackPanel Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Width="12" Height="12" Margin="0,0,0,0">
                        <Ellipse.Style>
                            <Style TargetType="Ellipse">
                                <Setter Property="Fill" Value="#FFF44336"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsOnline}" Value="True">
                                        <Setter Property="Fill" Value="#FF4CAF50"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Ellipse.Style>
                    </Ellipse>
                    <TextBlock Text="{Binding IsOnline, Converter={StaticResource OnlineStatusConverter}}" FontSize="13" VerticalAlignment="Center" Margin="5,0,0,0">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Foreground" Value="#FFF44336"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsOnline}" Value="True">
                                        <Setter Property="Foreground" Value="#FF4CAF50"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </StackPanel>
            </Grid>

            <!-- 中间：SN码 -->
            <Grid Grid.Row="1" Margin="0,2">
                <!-- 只显示SN码编号，无提示文字 -->
                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                    <TextBlock Text="{Binding SerialNumber}" FontSize="13" Foreground="#FF555555" TextTrimming="CharacterEllipsis"/>
                </StackPanel>
            </Grid>

            <!-- 底部：电池状态 -->
            <Grid Grid.Row="2" Margin="0,4,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 电池图标 -->
                <TextBlock Grid.Column="0" 
                           Text="&#xE996;" 
                           FontFamily="Segoe MDL2 Assets" 
                           FontSize="13" 
                           Foreground="#FF666666" 
                           VerticalAlignment="Center"
                           Margin="0,0,6,0"/>

                <!-- 电池进度条 -->
                <ProgressBar Grid.Column="1" 
                             Height="7" 
                             Value="{Binding BatteryLevel}" 
                             Maximum="100" 
                             Style="{StaticResource ModernBatteryStyle}"
                             VerticalAlignment="Center"
                             Visibility="Visible"/>

                <!-- 电池百分比 -->
                <TextBlock Grid.Column="2" 
                           Text="{Binding BatteryLevel, StringFormat={}{0}%}" 
                           FontSize="12" Foreground="#FF666666" VerticalAlignment="Center" Margin="6,0,0,0"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>