﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Globalization;
using XRSvc.DataSource;
using XRSvc.Services;
using XRSvc.ViewModels;
using Jskj.AppLog;

namespace XRSvc.CustomControl
{
    /// <summary>
    /// XRClientStyle.xaml 的交互逻辑
    /// </summary>
    public partial class XRClientStyle : UserControl
    {
        /// <summary>
        /// 游戏启动服务实例
        /// </summary>
        private readonly GameLaunchService _gameLaunchService;

        /// <summary>
        /// 当前设备数据源
        /// </summary>
        private DeviceSource _currentDevice;

        /// <summary>
        /// 设备序列号，用于标识设备
        /// </summary>
        private string _deviceSerialNumber;

        // 注意：_lastIsGameRunning 字段已移除，因为未被使用

        public XRClientStyle()
        {
            InitializeComponent();
            _gameLaunchService = new GameLaunchService();
            
            // 监听数据上下文变化
            this.DataContextChanged += XRClientStyle_DataContextChanged;
        }

        /// <summary>
        /// 数据上下文变化事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void XRClientStyle_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 确保在UI线程中处理DataContext变化
            if (Dispatcher.CheckAccess())
            {
                HandleDataContextChanged(e);
            }
            else
            {
                Dispatcher.BeginInvoke(new Action(() => HandleDataContextChanged(e)));
            }
        }

        /// <summary>
        /// 处理数据上下文变化的实际逻辑
        /// </summary>
        /// <param name="e">事件参数</param>
        private void HandleDataContextChanged(DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue is DeviceSource device)
            {
                // 检查是否是同一个设备
                if (_deviceSerialNumber != device.SerialNumber)
                {
                    _currentDevice = device;
                    _deviceSerialNumber = device.SerialNumber;

                    // 添加调试信息
                    System.Diagnostics.Debug.WriteLine($"XRClientStyle DataContextChanged: Device={device.SerialNumber}, IsGameRunning={device.IsGameRunning}");

                    // 根据设备状态更新UI
                    UpdateGameStatus();
                }
            }
        }

        /// <summary>
        /// 更新游戏状态显示
        /// </summary>
        private void UpdateGameStatus()
        {
            // 确保在UI线程上执行
            Dispatcher.Invoke(() =>
            {
                if (_currentDevice == null) return;

                // 检查设备是否在线
                if (!_currentDevice.IsOnline)
                {
                    TextBlock_GameStatus.Text = "未连接游戏服务器";
                    TextBlock_GameStatus.Foreground = Brushes.Gray;
                    Btn_JoinGame.IsEnabled = false;
                    Btn_ExitGame.IsEnabled = false;
                }
                else if (_currentDevice.IsGameStarting)
                {
                    TextBlock_GameStatus.Text = "游戏启动中";
                    TextBlock_GameStatus.Foreground = Brushes.Orange;
                    Btn_JoinGame.IsEnabled = false;
                    Btn_ExitGame.IsEnabled = false;
                }
                else if (_currentDevice.IsGameRunning)
                {
                    TextBlock_GameStatus.Text = "游戏已启动";
                    TextBlock_GameStatus.Foreground = Brushes.Green;
                    Btn_JoinGame.IsEnabled = false;
                    Btn_ExitGame.IsEnabled = true;
                }
                else
                {
                    TextBlock_GameStatus.Text = "游戏未启动";
                    TextBlock_GameStatus.Foreground = Brushes.Red;
                    Btn_JoinGame.IsEnabled = true;
                    Btn_ExitGame.IsEnabled = false;
                }
                // 调试信息可以保留
                System.Diagnostics.Debug.WriteLine($"XRClientStyle UpdateGameStatus: Device={_currentDevice?.SerialNumber}, IsOnline={_currentDevice?.IsOnline}, IsGameStarting={_currentDevice?.IsGameStarting}, IsGameRunning={_currentDevice?.IsGameRunning}");
            });
        }

        /// <summary>
        /// 加入游戏按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private async void Btn_JoinGame_Click(object sender, RoutedEventArgs e)
        {
            if (_currentDevice == null)
            {
                Log.Write(Level.ERROR, "设备信息无效");
                return;
            }

            if (!_currentDevice.IsOnline)
            {
                Log.Write(Level.INFO, "设备离线，无法启动游戏");
                return;
            }

            try
            {
                // 获取当前选中的游戏
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Log.Write(Level.ERROR, "无法获取主窗口信息");
                    return;
                }

                var selectedGame = mainWindow.GameViewModel.Source?.FirstOrDefault(g => g.ID == mainWindow.SelectedGameId);
                if (selectedGame == null)
                {
                    Log.Write(Level.INFO, "请先选择一个游戏");
                    return;
                }

                // 设置游戏启动中状态
                _currentDevice.IsGameStarting = true;
                UpdateGameStatus();
                Log.Write(Level.INFO, $"开始启动游戏 {selectedGame.Name}");

                // 调用公共方法启动游戏
                var success = await _gameLaunchService.LaunchGameForDeviceAsync(selectedGame, _currentDevice);

                // 清除启动中状态
                _currentDevice.IsGameStarting = false;

                if (success)
                {
                    UpdateGameStatus();
                    Log.Write(Level.INFO, $"游戏 {selectedGame.Name} 启动成功");
                }
                else
                {
                    UpdateGameStatus();
                    Log.Write(Level.ERROR, "游戏启动失败");
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"启动游戏时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 退出游戏按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private async void Btn_ExitGame_Click(object sender, RoutedEventArgs e)
        {
            if (_currentDevice == null)
            {
                Log.Write(Level.ERROR, "设备信息无效");
                return;
            }

            if (!_currentDevice.IsOnline)
            {
                Log.Write(Level.INFO, "设备离线，无法停止游戏");
                return;
            }

            try
            {
                // 获取当前选中的游戏
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow == null)
                {
                    Log.Write(Level.ERROR, "无法获取主窗口信息");
                    return;
                }

                var selectedGame = mainWindow.GameViewModel.Source?.FirstOrDefault(g => g.ID == mainWindow.SelectedGameId);
                if (selectedGame == null)
                {
                    Log.Write(Level.INFO, "请先选择一个游戏");
                    return;
                }

                // 调用公共方法停止/退出游戏
                var success = await _gameLaunchService.StopGameForDeviceAsync(selectedGame, _currentDevice);
                if (success)
                {
                    UpdateGameStatus();
                    Log.Write(Level.INFO, $"游戏 {selectedGame.Name} 停止成功");
                }
                else
                {
                    Log.Write(Level.ERROR, "游戏停止失败");
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"停止游戏时发生错误: {ex.Message}");
            }
        }
    }

    public class BoolToStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isOnline)
            {
                return isOnline ? "在线" : "离线";
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isOnline)
            {
                return isOnline ? Brushes.Green : Brushes.Red;
            }
            return Brushes.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class NameIdFormatConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && values[0] != null && values[1] != null)
                return $"{values[0]}({values[1]})";
            return string.Empty;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
