using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Jskj.AppLog;

namespace XRSvc.Utils
{
    /// <summary>
    /// 简化版UI优化测试工具
    /// </summary>
    public static class UIOptimizationTest
    {
        /// <summary>
        /// 测试UI线程切换性能对比
        /// </summary>
        public static async Task RunBasicPerformanceTest()
        {
            Log.Write(Level.INFO, "=== 开始UI优化基础性能测试 ===");

            const int testIterations = 100;
            var scheduler = UIThreadScheduler.Instance;

            // 测试1: 传统Dispatcher.Invoke方式
            Log.Write(Level.INFO, "测试1: 传统Dispatcher.Invoke方式");
            var stopwatch1 = Stopwatch.StartNew();
            
            for (int i = 0; i < testIterations; i++)
            {
                if (Application.Current?.Dispatcher != null)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // 模拟UI操作
                        var dummy = DateTime.Now.ToString();
                    });
                }
            }
            
            stopwatch1.Stop();
            var traditionalTime = stopwatch1.Elapsed;
            Log.Write(Level.INFO, $"传统方式耗时: {traditionalTime.TotalMilliseconds:F2}ms");

            // 等待一秒
            await Task.Delay(1000);

            // 测试2: 优化的UIThreadScheduler方式
            Log.Write(Level.INFO, "测试2: 优化的UIThreadScheduler方式");
            var stopwatch2 = Stopwatch.StartNew();
            
            for (int i = 0; i < testIterations; i++)
            {
                await scheduler.InvokeOnUIThreadAsync(() =>
                {
                    // 模拟UI操作
                    var dummy = DateTime.Now.ToString();
                });
            }
            
            stopwatch2.Stop();
            var optimizedTime = stopwatch2.Elapsed;
            Log.Write(Level.INFO, $"优化方式耗时: {optimizedTime.TotalMilliseconds:F2}ms");

            // 等待一秒
            await Task.Delay(1000);

            // 测试3: 批量处理方式
            Log.Write(Level.INFO, "测试3: 批量处理方式");
            var stopwatch3 = Stopwatch.StartNew();
            
            for (int i = 0; i < testIterations; i++)
            {
                scheduler.BatchInvokeOnUIThread(() =>
                {
                    // 模拟UI操作
                    var dummy = DateTime.Now.ToString();
                }, $"batch_test_{i}");
            }
            
            // 等待批量处理完成
            await scheduler.FlushBatchAsync();
            stopwatch3.Stop();
            var batchTime = stopwatch3.Elapsed;
            Log.Write(Level.INFO, $"批量处理耗时: {batchTime.TotalMilliseconds:F2}ms");

            // 计算性能提升
            var improvementVsTraditional = traditionalTime.TotalMilliseconds > 0 ? 
                ((traditionalTime.TotalMilliseconds - optimizedTime.TotalMilliseconds) / traditionalTime.TotalMilliseconds) * 100 : 0;
            var improvementVsBatch = traditionalTime.TotalMilliseconds > 0 ? 
                ((traditionalTime.TotalMilliseconds - batchTime.TotalMilliseconds) / traditionalTime.TotalMilliseconds) * 100 : 0;

            Log.Write(Level.INFO, "=== 性能对比结果 ===");
            Log.Write(Level.INFO, $"传统方式: {traditionalTime.TotalMilliseconds:F2}ms");
            Log.Write(Level.INFO, $"优化方式: {optimizedTime.TotalMilliseconds:F2}ms (提升 {improvementVsTraditional:F1}%)");
            Log.Write(Level.INFO, $"批量方式: {batchTime.TotalMilliseconds:F2}ms (提升 {improvementVsBatch:F1}%)");

            Log.Write(Level.INFO, "=== UI优化基础性能测试完成 ===");
        }

        /// <summary>
        /// 测试设备状态更新性能
        /// </summary>
        public static async Task TestDeviceStatusUpdatePerformance()
        {
            Log.Write(Level.INFO, "=== 开始设备状态更新性能测试 ===");

            const int deviceCount = 20;
            const int updateCycles = 5;
            var scheduler = UIThreadScheduler.Instance;

            // 模拟设备状态更新
            var stopwatch = Stopwatch.StartNew();
            
            for (int cycle = 0; cycle < updateCycles; cycle++)
            {
                for (int deviceId = 0; deviceId < deviceCount; deviceId++)
                {
                    var deviceIdCopy = deviceId;
                    var cycleCopy = cycle;
                    
                    // 模拟设备状态变化
                    scheduler.BatchInvokeOnUIThread(() =>
                    {
                        // 模拟UI更新操作
                        var status = $"Device_{deviceIdCopy}_Status_{cycleCopy}";
                        var dummy = status.GetHashCode();
                    }, $"device_update_{deviceIdCopy}_{cycleCopy}");
                }
                
                // 刷新批量操作
                await scheduler.FlushBatchAsync();
                
                // 模拟设备状态变化间隔
                await Task.Delay(50);
            }
            
            stopwatch.Stop();
            
            Log.Write(Level.INFO, $"设备状态更新测试完成");
            Log.Write(Level.INFO, $"设备数量: {deviceCount}");
            Log.Write(Level.INFO, $"更新周期: {updateCycles}");
            Log.Write(Level.INFO, $"总耗时: {stopwatch.Elapsed.TotalMilliseconds:F2}ms");
            Log.Write(Level.INFO, $"平均每次更新: {stopwatch.Elapsed.TotalMilliseconds / (deviceCount * updateCycles):F2}ms");

            Log.Write(Level.INFO, "=== 设备状态更新性能测试完成 ===");
        }

        /// <summary>
        /// 运行基础性能测试套件
        /// </summary>
        public static async Task RunBasicTestSuite()
        {
            Log.Write(Level.INFO, "========================================");
            Log.Write(Level.INFO, "开始基础UI优化性能测试套件");
            Log.Write(Level.INFO, "========================================");

            try
            {
                await RunBasicPerformanceTest();
                await Task.Delay(1000);
                
                await TestDeviceStatusUpdatePerformance();
                
                Log.Write(Level.INFO, "========================================");
                Log.Write(Level.INFO, "基础性能测试套件执行完成");
                Log.Write(Level.INFO, "========================================");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"性能测试执行失败: {ex.Message}");
            }
        }
    }
}
