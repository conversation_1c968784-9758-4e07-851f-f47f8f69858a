package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.communication.data.GameInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 游戏停止请求消息
 */
public class GameStopRequestMessage extends BaseMessage {
    private GameInfo selectedGame;
    private List<String> selectedDeviceIds;
    private boolean forceStop; // 是否强制停止

    public GameStopRequestMessage() {
        setType(MessageType.GAME_STOP_REQUEST);
        this.selectedDeviceIds = new ArrayList<>();
        this.forceStop = false;
    }

    // Getter和Setter方法
    public GameInfo getSelectedGame() { return selectedGame; }
    public void setSelectedGame(GameInfo selectedGame) { this.selectedGame = selectedGame; }
    
    public List<String> getSelectedDeviceIds() { return selectedDeviceIds; }
    public void setSelectedDeviceIds(List<String> selectedDeviceIds) { this.selectedDeviceIds = selectedDeviceIds; }
    
    public boolean isForceStop() { return forceStop; }
    public void setForceStop(boolean forceStop) { this.forceStop = forceStop; }
}
