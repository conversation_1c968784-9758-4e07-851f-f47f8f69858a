﻿﻿using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Markup;

namespace XRSvc.CustomControl
{
    /// <summary>
    /// DeviceStyle.xaml 的交互逻辑
    /// </summary>
    public partial class DeviceStyle : UserControl
    {
        public DeviceStyle()
        {
            InitializeComponent();
        }
    }

    /// <summary>
    /// 在线状态转换器
    /// </summary>
    public class OnlineStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isOnline)
            {
                return isOnline ? "在线" : "离线";
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}