# XR系统 Android端快速入门指南

## 🚀 5分钟快速开始

### 1. 环境准备
```bash
# 检查环境
java -version    # 需要Java 11+
android --version # 需要Android SDK

# 克隆项目
git clone [项目地址]
cd XRSystem/XRAndroidClient
```

### 2. 导入项目
1. 打开Android Studio
2. 选择 "Open an existing Android Studio project"
3. 选择 `XRAndroidClient` 目录
4. 等待Gradle同步完成

### 3. 配置MQTT连接
```java
// 在MqttGameClient构造函数中修改Broker地址
public MqttGameClient(Context context, String brokerHost, int brokerPort) {
    this.brokerUrl = "tcp://" + brokerHost + ":" + brokerPort;
    // 默认: tcp://*************:1883
}
```

### 4. 运行应用
1. 连接Android设备或启动模拟器
2. 点击 "Run" 按钮 (Shift+F10)
3. 应用将自动安装并启动

## 📱 核心功能使用

### 游戏进度管理
- **查看设备**: 底部导航 → "游戏进度"
- **启动游戏**: 选择设备 → 点击"启动游戏"
- **停止游戏**: 点击"停止游戏"

### 设备状态监控
- **设备列表**: 底部导航 → "设备状态"
- **在线状态**: 绿点=在线，红点=离线
- **电池电量**: 显示在设备卡片右上角

### 硬件检测
- **检测设备**: 底部导航 → "硬件检测"
- **查看详情**: 点击设备卡片查看详细信息

### 游戏设置
- **系统配置**: 底部导航 → "游戏设置"
- **MQTT配置**: 修改Broker地址和端口

## 🔧 常见问题

### Q: 应用无法连接到MQTT Broker
**A**: 检查以下几点：
1. 确认PC服务端正在运行
2. 检查网络连接是否正常
3. 验证Broker地址和端口配置
4. 查看Logcat日志: `adb logcat | grep "XRSystem_MQTT"`

### Q: 设备列表为空
**A**: 可能原因：
1. PC服务端未启动
2. 网络连接问题
3. MQTT主题订阅失败
4. 设备数据未同步

### Q: 游戏启动失败
**A**: 检查步骤：
1. 确认设备在线状态
2. 检查设备电量是否充足
3. 验证游戏服务是否正常
4. 查看错误日志获取详细信息

## 📋 开发调试

### 查看日志
```bash
# 查看所有应用日志
adb logcat | grep "XRSystem"

# 查看MQTT通信日志
adb logcat | grep "XRSystem_MQTT"

# 查看设备管理日志
adb logcat | grep "XRSystem_Device"
```

### 调试网络连接
```java
// 在MqttGameClient中添加调试日志
private void debugConnection() {
    LogUtils.d("MQTT", "Broker URL: " + brokerUrl);
    LogUtils.d("MQTT", "Client ID: " + clientId);
    LogUtils.d("MQTT", "Connection Status: " + isConnected);
}
```

### 模拟设备数据
```java
// 使用DeviceInfo.generateAllDevices()生成测试数据
List<DeviceInfo> testDevices = DeviceInfo.generateAllDevices();
LogUtils.i("Device", "生成测试设备数量: " + testDevices.size());
```

## 🏗️ 项目结构速览

```
XRAndroidClient/
├── app/src/main/java/com/gzcec/xrandroidclient/
│   ├── MainActivity.java              # 主界面
│   ├── communication/                 # 通信模块
│   │   ├── MqttGameClient.java       # MQTT客户端
│   │   └── MqttTopics.java           # 主题定义
│   ├── device/                       # 设备模块
│   │   ├── DeviceInfo.java           # 设备信息
│   │   └── DeviceType.java           # 设备类型
│   ├── gameprogress/                 # 游戏进度
│   ├── devicestatus/                 # 设备状态
│   ├── hardwarecheck/                # 硬件检测
│   └── GameSetting/                  # 游戏设置
└── app/src/main/res/                 # 资源文件
    ├── layout/                       # 布局文件
    ├── drawable/                     # 图标资源
    └── values/                       # 值资源
```

## 🎯 关键代码片段

### MQTT连接
```java
// 连接到MQTT Broker
mqttClient.connect();

// 设置连接监听器
mqttClient.setConnectionListener(new MqttGameClient.ConnectionListener() {
    @Override
    public void onConnected() {
        LogUtils.i("MQTT", "连接成功");
    }
    
    @Override
    public void onDisconnected(String reason) {
        LogUtils.w("MQTT", "连接断开: " + reason);
    }
    
    @Override
    public void onError(String error) {
        LogUtils.e("MQTT", "连接错误: " + error);
    }
});
```

### 请求设备列表
```java
mqttClient.requestDeviceList(true, response -> {
    LogUtils.i("Device", "收到设备列表响应");
    // 处理设备列表数据
    parseDeviceListResponse(response);
});
```

### 启动游戏
```java
GameInfo gameInfo = new GameInfo("game_001", "VR冒险");
List<String> deviceIds = Arrays.asList("device_001", "device_002");

mqttClient.requestStartGame(gameInfo, deviceIds, response -> {
    LogUtils.i("Game", "游戏启动响应: " + response);
    // 处理启动结果
    handleGameStartResponse(response);
});
```

## 📚 下一步学习

### 深入了解
1. 阅读完整的 [Android开发文档](./Android开发文档.md)
2. 学习MQTT协议和消息格式
3. 了解设备管理和游戏控制流程
4. 掌握UI组件和Fragment使用

### 实践项目
1. 添加新的设备类型支持
2. 实现设备分组功能
3. 优化UI界面和用户体验
4. 添加数据统计和报表功能

### 参考资源
- [Android官方文档](https://developer.android.com/)
- [Material Design指南](https://material.io/design)
- [MQTT协议文档](https://mqtt.org/)
- [项目完整文档](./Android开发文档.md)

---

**快速入门指南版本**: v1.0  
**适用于**: XR系统 Android端 v1.0+  
**更新时间**: 2024-07-24

🎉 **恭喜！** 你已经完成了XR系统Android端的快速入门。如有问题，请参考完整开发文档或联系开发团队。
