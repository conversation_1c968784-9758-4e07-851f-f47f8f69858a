<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="88dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:layout_marginStart="16dp"
    android:layout_marginEnd="16dp"
    android:foreground="@drawable/card_border"
    app:cardBackgroundColor="#23272F">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:background="@android:color/transparent"
        android:gravity="center_vertical"
        android:padding="12dp">

        <!-- 设备图标 -->
        <ImageView
            android:id="@+id/iv_device_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_fan_device"
            app:tint="#FFFFFF"
            android:padding="6dp" />

        <!-- 中间信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="12dp"
            android:gravity="center_vertical">

            <!-- 设备名称 -->
            <TextView
                android:id="@+id/tv_device_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="动感平台"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="2dp" />

            <!-- 状态信息行 -->

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="2dp">

                <!-- 在线状态 -->
                <View
                    android:id="@+id/iv_online_status"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:background="@drawable/ic_red_dot_alt"
                    android:layout_gravity="center_vertical"/>
                <TextView
                    android:id="@+id/tv_online_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="离线"
                    android:textColor="#FF5722"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:paddingStart="5dp"
                    android:layout_gravity="center_vertical"/>

                <!-- 分隔符 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" | "
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"/>

                <!-- 运行状态 -->
                <View
                    android:id="@+id/iv_running_status"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:background="@drawable/ic_red_dot_alt"
                    android:layout_gravity="center_vertical"/>
                <TextView
                    android:id="@+id/tv_running_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="停止"
                    android:textColor="#FF5722"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:paddingStart="5dp"
                    android:layout_gravity="center_vertical"/>

                <!-- 分隔符 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" | "
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginStart="6dp"
                    android:layout_marginEnd="6dp"/>

                <TextView
                    android:id="@+id/tv_device_ip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:text="*************"
                    android:textColor="#90CAF9"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- IP地址 - 在中间区域居中显示 -->
        </LinearLayout>

        <!-- 右侧按钮 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <Button
                android:id="@+id/btn_start"
                android:layout_width="48dp"
                android:layout_height="44dp"
                android:text="开"
                android:textColor="#FFFFFF"
                android:backgroundTint="#00C853"
                android:textSize="16sp"
                android:layout_marginEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:background="@drawable/btn_green_selector"/>

            <Button
                android:id="@+id/btn_stop"
                android:layout_width="48dp"
                android:layout_height="44dp"
                android:text="关"
                android:textColor="#FFFFFF"
                android:backgroundTint="#F44336"
                android:textSize="16sp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:background="@drawable/btn_red_selector"/>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
