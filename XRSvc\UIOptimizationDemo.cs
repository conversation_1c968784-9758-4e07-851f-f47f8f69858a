using System;
using System.Threading.Tasks;
using XRSvc.Utils;
using Jskj.AppLog;

namespace XRSvc
{
    /// <summary>
    /// UI优化演示类，展示如何使用新的UI线程调度器
    /// </summary>
    public static class UIOptimizationDemo
    {
        /// <summary>
        /// 演示基本的UI线程调度器使用方法
        /// </summary>
        public static async Task DemonstrateUIThreadScheduler()
        {
            Log.Write(Level.INFO, "=== UI线程调度器使用演示 ===");
            
            var scheduler = UIThreadScheduler.Instance;
            
            try
            {
                // 1. 同步UI操作
                Log.Write(Level.INFO, "1. 演示同步UI操作");
                scheduler.InvokeOnUIThread(() =>
                {
                    Log.Write(Level.INFO, "   - 同步UI操作执行成功");
                });
                
                // 2. 异步UI操作
                Log.Write(Level.INFO, "2. 演示异步UI操作");
                await scheduler.InvokeOnUIThreadAsync(() =>
                {
                    Log.Write(Level.INFO, "   - 异步UI操作执行成功");
                });
                
                // 3. 批量UI操作
                Log.Write(Level.INFO, "3. 演示批量UI操作");
                for (int i = 1; i <= 5; i++)
                {
                    var index = i;
                    scheduler.BatchInvokeOnUIThread(() =>
                    {
                        Log.Write(Level.INFO, $"   - 批量操作 {index} 执行");
                    }, $"demo_batch_{index}");
                }
                
                // 4. 刷新批量操作
                Log.Write(Level.INFO, "4. 刷新所有批量操作");
                await scheduler.FlushBatchAsync();
                
                // 5. 演示去重功能
                Log.Write(Level.INFO, "5. 演示操作去重功能");
                for (int i = 1; i <= 3; i++)
                {
                    scheduler.BatchInvokeOnUIThread(() =>
                    {
                        Log.Write(Level.INFO, "   - 相同ID的操作（只会执行一次）");
                    }, "duplicate_operation");
                }
                await scheduler.FlushBatchAsync();
                
                Log.Write(Level.INFO, "=== UI线程调度器演示完成 ===");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"UI线程调度器演示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 演示设备状态更新的优化使用方式
        /// </summary>
        public static async Task DemonstrateDeviceStatusUpdate()
        {
            Log.Write(Level.INFO, "=== 设备状态更新优化演示 ===");
            
            var scheduler = UIThreadScheduler.Instance;
            
            try
            {
                // 模拟多个设备状态同时更新
                Log.Write(Level.INFO, "模拟10个设备状态同时更新");
                
                for (int deviceId = 1; deviceId <= 10; deviceId++)
                {
                    var id = deviceId;
                    
                    // 使用批量处理，相同设备的多次更新会被去重
                    scheduler.BatchInvokeOnUIThread(() =>
                    {
                        Log.Write(Level.INFO, $"   - 设备 {id} 状态已更新");
                    }, $"device_status_{id}");
                }
                
                // 模拟同一设备的多次快速更新（会被去重）
                Log.Write(Level.INFO, "模拟设备1的多次快速更新（演示去重效果）");
                for (int i = 1; i <= 5; i++)
                {
                    scheduler.BatchInvokeOnUIThread(() =>
                    {
                        Log.Write(Level.INFO, "   - 设备1状态更新（最终只执行一次）");
                    }, "device_status_1"); // 相同ID，会被去重
                }
                
                await scheduler.FlushBatchAsync();
                
                Log.Write(Level.INFO, "=== 设备状态更新优化演示完成 ===");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"设备状态更新演示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 演示MQTT消息处理的优化使用方式
        /// </summary>
        public static async Task DemonstrateMqttMessageProcessing()
        {
            Log.Write(Level.INFO, "=== MQTT消息处理优化演示 ===");
            
            var scheduler = UIThreadScheduler.Instance;
            
            try
            {
                // 模拟接收到多条MQTT消息
                Log.Write(Level.INFO, "模拟处理5条MQTT消息");
                
                for (int msgId = 1; msgId <= 5; msgId++)
                {
                    var id = msgId;
                    
                    // 模拟在后台线程处理MQTT消息，然后更新UI
                    await Task.Run(async () =>
                    {
                        // 模拟消息处理时间
                        await Task.Delay(100);
                        
                        // 使用优化的UI线程调度器更新界面
                        await scheduler.InvokeOnUIThreadAsync(() =>
                        {
                            Log.Write(Level.INFO, $"   - MQTT消息 {id} 处理完成，UI已更新");
                        });
                    });
                }
                
                Log.Write(Level.INFO, "=== MQTT消息处理优化演示完成 ===");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"MQTT消息处理演示失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行所有演示
        /// </summary>
        public static async Task RunAllDemonstrations()
        {
            Log.Write(Level.INFO, "========================================");
            Log.Write(Level.INFO, "开始UI优化功能演示");
            Log.Write(Level.INFO, "========================================");
            
            try
            {
                await DemonstrateUIThreadScheduler();
                await Task.Delay(1000);
                
                await DemonstrateDeviceStatusUpdate();
                await Task.Delay(1000);
                
                await DemonstrateMqttMessageProcessing();
                
                Log.Write(Level.INFO, "========================================");
                Log.Write(Level.INFO, "UI优化功能演示完成");
                Log.Write(Level.INFO, "========================================");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"演示执行失败: {ex.Message}");
            }
        }
    }
}
