# cursorrules

# 注释规范
- 所有C#类、方法、属性、字段必须添加中文注释，简要说明其作用和用途。
- 注释需使用三斜杠（///）XML格式，便于文档生成和智能提示。
- XAML文件无需添加注释，但其后台代码（.xaml.cs）同样需要为方法和属性添加中文注释。

# 类文件管理
- 新建C#类文件后，需确保在项目的 .csproj 文件中被正确引用（如未自动添加需手动添加）。

# 模块化与解耦
- 每个类只负责单一功能，避免出现"巨型类"。
- 不同模块之间要解耦，推荐使用接口（Interface）、依赖注入（DI）等方式实现模块间通信。
- 公共方法、工具类、数据模型等应分别放在独立的文件夹和命名空间下，便于维护和复用。

# 代码结构
- 避免在一个类中编写过多代码，单个类文件建议不超过500行。
- 复杂功能应拆分为多个小方法或多个类协作完成。

# 命名规范
- 类名、方法名、属性名、字段名等需语义明确，见名知意，遵循C#命名规范。
- 命名时避免拼音、缩写，除非为行业通用缩写。

# 其他
- 生成代码时，优先考虑可维护性和可扩展性。
- 代码风格需统一，遵循C#官方推荐的代码风格。

# 变更前确认机制
- 每次需要对代码进行新增、修改、优化等操作时，AI需先给出详细的操作方案，包括：
  1. 变更目标（如：新增类、优化方法、重构结构等）
  2. 具体修改点（涉及哪些文件、类、方法、属性等）
  3. 预期效果或优化点说明
  4. 可能的风险或注意事项（如有）
- 方案需以清单或分点形式列出，便于用户审核。
- 仅在用户明确同意后，AI方可自动执行相关代码变更。
- 若用户有补充或调整意见，AI需根据反馈完善方案，再次确认后执行。 