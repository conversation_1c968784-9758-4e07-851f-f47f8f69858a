package com.gzcec.xrandroidclient.communication.messages.system;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;

import java.util.HashMap;
import java.util.Map;

/**
 * 心跳消息
 */
public class HeartbeatMessage extends BaseMessage {
    private String status;
    private Map<String, Object> info;

    public HeartbeatMessage() {
        setType(MessageType.HEARTBEAT);
        this.info = new HashMap<>();
    }

    // Getter和Setter方法
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public Map<String, Object> getInfo() { return info; }
    public void setInfo(Map<String, Object> info) { this.info = info; }
}
