<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#2196F3" /> <!-- 按下/高亮亮蓝色 -->
            <stroke android:width="1dp" android:color="#5A5A5A" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#2196F3" /> <!-- 选中高亮亮蓝色 -->
            <stroke android:width="1dp" android:color="#5A5A5A" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#44474A" /> <!-- 平时灰色 -->
            <stroke android:width="1dp" android:color="#3A3A3A" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>