package com.gzcec.xrandroidclient.communication;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.gzcec.xrandroidclient.config.NetworkConfig;

import java.util.HashSet;
import java.util.Set;

/**
 * 网络配置同步管理器
 * 解决端口切换导致的配置混乱问题
 */
public class NetworkConfigSyncManager {
    private static final String TAG = "NetworkConfigSyncManager";
    
    // SharedPreferences键名
    private static final String PREFS_NAME = "network_config_sync";
    private static final String KEY_SERVER_HISTORY = "server_history";
    private static final String KEY_LAST_SUCCESSFUL_CONFIG = "last_successful_config";
    private static final String KEY_PORT_CHANGE_COUNT = "port_change_count";
    private static final String KEY_LAST_PORT_CHANGE_TIME = "last_port_change_time";
    
    // 配置常量
    private static final int MAX_HISTORY_SIZE = 10;
    private static final long PORT_CHANGE_COOLDOWN = 30000; // 30秒冷却时间
    
    private Context context;
    private NetworkConfig networkConfig;
    private SharedPreferences prefs;
    
    public NetworkConfigSyncManager(Context context, NetworkConfig networkConfig) {
        this.context = context;
        this.networkConfig = networkConfig;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    /**
     * 记录成功的服务器配置
     * 只有在MQTT连接真正建立并稳定后才调用此方法
     */
    public void recordSuccessfulConfig(String host, int port) {
        String config = host + ":" + port;
        Log.i(TAG, "记录成功配置: " + config);

        // 保存最后成功的配置
        prefs.edit()
            .putString(KEY_LAST_SUCCESSFUL_CONFIG, config)
            .apply();

        // 添加到历史记录
        addToHistory(config);

        // 重置端口变化计数，因为找到了稳定的配置
        prefs.edit()
            .putInt(KEY_PORT_CHANGE_COUNT, 0)
            .putLong(KEY_LAST_PORT_CHANGE_TIME, 0)
            .apply();
    }
    
    /**
     * 记录端口变化
     */
    public void recordPortChange(int oldPort, int newPort) {
        long currentTime = System.currentTimeMillis();
        long lastChangeTime = prefs.getLong(KEY_LAST_PORT_CHANGE_TIME, 0);
        
        // 检查是否在冷却时间内
        if (currentTime - lastChangeTime < PORT_CHANGE_COOLDOWN) {
            Log.w(TAG, "端口变化过于频繁，忽略: " + oldPort + " -> " + newPort);
            return;
        }
        
        int changeCount = prefs.getInt(KEY_PORT_CHANGE_COUNT, 0) + 1;
        
        Log.i(TAG, String.format("记录端口变化: %d -> %d (第%d次)", oldPort, newPort, changeCount));
        
        prefs.edit()
            .putInt(KEY_PORT_CHANGE_COUNT, changeCount)
            .putLong(KEY_LAST_PORT_CHANGE_TIME, currentTime)
            .apply();
        
        // 如果端口变化过于频繁，重置为最后成功的配置
        if (changeCount > 3) {
            Log.w(TAG, "端口变化过于频繁，尝试恢复最后成功的配置");
            restoreLastSuccessfulConfig();
        }
    }
    
    /**
     * 获取最后成功的配置
     */
    public String getLastSuccessfulConfig() {
        return prefs.getString(KEY_LAST_SUCCESSFUL_CONFIG, null);
    }
    
    /**
     * 恢复最后成功的配置
     */
    public boolean restoreLastSuccessfulConfig() {
        String lastConfig = getLastSuccessfulConfig();
        if (lastConfig == null) {
            Log.w(TAG, "没有最后成功的配置可恢复");
            return false;
        }
        
        try {
            String[] parts = lastConfig.split(":");
            if (parts.length == 2) {
                String host = parts[0];
                int port = Integer.parseInt(parts[1]);
                
                Log.i(TAG, "恢复最后成功的配置: " + lastConfig);
                networkConfig.setMqttHost(host);
                networkConfig.setMqttPort(port);
                
                // 重置端口变化计数
                prefs.edit()
                    .putInt(KEY_PORT_CHANGE_COUNT, 0)
                    .apply();
                
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "恢复配置失败: " + lastConfig, e);
        }
        
        return false;
    }
    
    /**
     * 获取服务器历史记录
     */
    public Set<String> getServerHistory() {
        return prefs.getStringSet(KEY_SERVER_HISTORY, new HashSet<>());
    }
    
    /**
     * 添加到历史记录
     */
    private void addToHistory(String config) {
        Set<String> history = new HashSet<>(getServerHistory());
        history.add(config);
        
        // 限制历史记录大小
        if (history.size() > MAX_HISTORY_SIZE) {
            // 简单的处理：清除所有历史记录，只保留当前配置
            history.clear();
            history.add(config);
        }
        
        prefs.edit()
            .putStringSet(KEY_SERVER_HISTORY, history)
            .apply();
    }
    
    /**
     * 清除历史记录
     */
    public void clearHistory() {
        Log.i(TAG, "清除服务器历史记录");
        prefs.edit()
            .remove(KEY_SERVER_HISTORY)
            .remove(KEY_LAST_SUCCESSFUL_CONFIG)
            .putInt(KEY_PORT_CHANGE_COUNT, 0)
            .apply();
    }

    /**
     * 清除错误的推荐配置
     * 当检测到推荐配置连接失败时调用
     */
    public void clearRecommendedConfig() {
        String lastConfig = getLastSuccessfulConfig();
        if (lastConfig != null) {
            Log.w(TAG, "清除错误的推荐配置: " + lastConfig);
            prefs.edit()
                .remove(KEY_LAST_SUCCESSFUL_CONFIG)
                .apply();
        }
    }

    /**
     * 标记配置为无效
     * 当连接失败时调用，避免继续使用错误配置
     */
    public void markConfigAsInvalid(String host, int port) {
        String config = host + ":" + port;
        String lastSuccessful = getLastSuccessfulConfig();

        if (config.equals(lastSuccessful)) {
            Log.w(TAG, "标记配置为无效并清除: " + config);
            clearRecommendedConfig();
        }

        // 从历史记录中移除
        Set<String> history = new HashSet<>(getServerHistory());
        if (history.remove(config)) {
            prefs.edit()
                .putStringSet(KEY_SERVER_HISTORY, history)
                .apply();
            Log.i(TAG, "从历史记录中移除无效配置: " + config);
        }
    }
    
    /**
     * 获取端口变化统计
     */
    public int getPortChangeCount() {
        return prefs.getInt(KEY_PORT_CHANGE_COUNT, 0);
    }
    
    /**
     * 检查配置是否稳定
     */
    public boolean isConfigStable() {
        long lastChangeTime = prefs.getLong(KEY_LAST_PORT_CHANGE_TIME, 0);
        long currentTime = System.currentTimeMillis();
        int changeCount = prefs.getInt(KEY_PORT_CHANGE_COUNT, 0);
        
        // 如果最近没有端口变化，或者变化次数较少，认为配置稳定
        return (currentTime - lastChangeTime > PORT_CHANGE_COOLDOWN * 2) || (changeCount <= 1);
    }
    
    /**
     * 获取推荐的服务器配置
     * 基于历史记录和成功率
     */
    public String getRecommendedConfig() {
        // 首先尝试最后成功的配置
        String lastSuccessful = getLastSuccessfulConfig();
        if (lastSuccessful != null && isConfigStable()) {
            return lastSuccessful;
        }
        
        // 如果配置不稳定，从历史记录中选择
        Set<String> history = getServerHistory();
        if (!history.isEmpty()) {
            // 简单策略：返回历史记录中的第一个
            return history.iterator().next();
        }
        
        return null;
    }
    
    /**
     * 验证配置格式
     */
    public static boolean isValidConfig(String config) {
        if (config == null || config.trim().isEmpty()) {
            return false;
        }
        
        try {
            String[] parts = config.split(":");
            if (parts.length != 2) {
                return false;
            }
            
            String host = parts[0].trim();
            int port = Integer.parseInt(parts[1].trim());
            
            return !host.isEmpty() && port > 0 && port <= 65535;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取配置同步状态信息
     */
    public String getStatusInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("配置同步状态:\n");
        sb.append("- 最后成功配置: ").append(getLastSuccessfulConfig()).append("\n");
        sb.append("- 端口变化次数: ").append(getPortChangeCount()).append("\n");
        sb.append("- 配置稳定性: ").append(isConfigStable() ? "稳定" : "不稳定").append("\n");
        sb.append("- 历史记录数量: ").append(getServerHistory().size()).append("\n");
        sb.append("- 推荐配置: ").append(getRecommendedConfig());
        
        return sb.toString();
    }
}
