﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XRSvc.DataPack
{
    public enum DeviceType
    {
        /// <summary>
        /// 服务器
        /// </summary>
        SERVER,

        /// <summary>
        /// 游戏服务端
        /// </summary>
        GAMESVC,

        /// <summary>
        /// 头显
        /// </summary>
        HMD,

        /// <summary>
        /// 动感平台
        /// </summary>
        MotionPlatform,

        /// <summary>
        /// 可调速风扇
        /// </summary>
        Fan,

        /// <summary>
        /// 热感
        /// </summary>
        Hotness,

        /// <summary>
        /// 可推拉门(安装有角度传感器)
        /// </summary>
        DoorManual,

        /// <summary>
        /// 指令控制门
        /// </summary>
        DoorCmdControl,

        /// <summary>
        /// 喷水
        /// </summary>
        WaterSpray,


    }
}
