package com.gzcec.xrandroidclient.device;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 设备初始化测试类
 * 验证Android端设备初始化逻辑是否正确
 */
public class DeviceInitializationTest {

    private List<DeviceInfo> deviceList;

    @Before
    public void setUp() {
        // 获取初始化设备列表
        deviceList = DeviceInfo.createInitialDeviceList();
    }

    @Test
    public void testDeviceListSize() {
        // 验证设备总数：30个头显设备 + 6个外设设备 = 36个设备
        assertEquals("设备总数应为36个", 36, deviceList.size());
    }

    @Test
    public void testHMDDeviceCount() {
        // 验证头显设备数量
        long hmdCount = deviceList.stream()
                .filter(device -> device.getDeviceType() == DeviceType.HMD)
                .count();
        assertEquals("头显设备数量应为30个", 30, hmdCount);
    }

    @Test
    public void testPeripheralDeviceCount() {
        // 验证外设设备数量
        long peripheralCount = deviceList.stream()
                .filter(device -> device.getDeviceType() != DeviceType.HMD 
                        && device.getDeviceType() != DeviceType.SERVER 
                        && device.getDeviceType() != DeviceType.GAMESVC)
                .count();
        assertEquals("外设设备数量应为6个", 6, peripheralCount);
    }

    @Test
    public void testServer80DeviceIds() {
        // 验证80服务器设备ID范围 (8001-8015)
        long server80Count = deviceList.stream()
                .filter(device -> device.getDeviceType() == DeviceType.HMD)
                .filter(device -> device.getId() >= 8001 && device.getId() <= 8015)
                .count();
        assertEquals("80服务器设备数量应为15个", 15, server80Count);
    }

    @Test
    public void testServer81DeviceIds() {
        // 验证81服务器设备ID范围 (8101-8115)
        long server81Count = deviceList.stream()
                .filter(device -> device.getDeviceType() == DeviceType.HMD)
                .filter(device -> device.getId() >= 8101 && device.getId() <= 8115)
                .count();
        assertEquals("81服务器设备数量应为15个", 15, server81Count);
    }

    @Test
    public void testPeripheralDeviceTypes() {
        // 验证外设设备类型
        Map<DeviceType, Integer> peripheralTypes = new HashMap<>();
        
        for (DeviceInfo device : deviceList) {
            DeviceType type = device.getDeviceType();
            if (type != DeviceType.HMD && type != DeviceType.SERVER && type != DeviceType.GAMESVC) {
                peripheralTypes.put(type, peripheralTypes.getOrDefault(type, 0) + 1);
            }
        }

        // 验证每种外设设备类型都存在且只有一个
        assertEquals("应有动感平台设备", 1, (int) peripheralTypes.getOrDefault(DeviceType.MotionPlatform, 0));
        assertEquals("应有风扇设备", 1, (int) peripheralTypes.getOrDefault(DeviceType.Fan, 0));
        assertEquals("应有喷水设备", 1, (int) peripheralTypes.getOrDefault(DeviceType.WaterSpray, 0));
        assertEquals("应有热感设备", 1, (int) peripheralTypes.getOrDefault(DeviceType.Hotness, 0));
        assertEquals("应有可推拉门设备", 1, (int) peripheralTypes.getOrDefault(DeviceType.DoorManual, 0));
        assertEquals("应有指令控制门设备", 1, (int) peripheralTypes.getOrDefault(DeviceType.DoorCmdControl, 0));
    }

    @Test
    public void testDeviceDefaultStates() {
        // 验证设备默认状态
        for (DeviceInfo device : deviceList) {
            assertFalse("设备默认应为离线状态", device.isOnline());
            assertFalse("设备默认应为禁用状态", device.isEnabled());
            assertFalse("设备默认应不在游戏中", device.isInGame());
            assertFalse("设备默认应未选中", device.isSelected());
            assertNotNull("设备名称不应为空", device.getName());
            assertNotNull("设备序列号不应为空", device.getSerialNumber());
            assertNotNull("设备IP地址不应为空", device.getIpAddress());
            assertNotNull("设备类型不应为空", device.getDeviceType());
            assertNotNull("最后更新时间不应为空", device.getLastUpdated());
        }
    }

    @Test
    public void testPeripheralDeviceIds() {
        // 验证外设设备ID范围 (9001-9006)
        long peripheralIdCount = deviceList.stream()
                .filter(device -> device.getId() >= 9001 && device.getId() <= 9006)
                .count();
        assertEquals("外设设备ID应在9001-9006范围内", 6, peripheralIdCount);
    }

    @Test
    public void testDeviceNaming() {
        // 验证设备命名规则
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HMD) {
                assertTrue("头显设备名称应包含'号机'", device.getName().contains("号机"));
            } else {
                // 外设设备应有描述性名称
                assertFalse("外设设备名称不应包含'号机'", device.getName().contains("号机"));
            }
        }
    }
}
