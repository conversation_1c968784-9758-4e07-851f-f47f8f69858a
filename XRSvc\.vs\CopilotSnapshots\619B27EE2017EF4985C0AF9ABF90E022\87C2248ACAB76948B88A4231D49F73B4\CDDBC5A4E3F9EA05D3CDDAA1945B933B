﻿﻿using System;
﻿using System.Collections.Generic;
﻿using System.Linq;
﻿using System.Text;
﻿using System.Threading.Tasks;
﻿using System.Windows;
﻿using System.Windows.Controls;
﻿using System.Windows.Data;
﻿using System.Windows.Documents;
﻿using System.Windows.Input;
﻿using System.Windows.Media;
﻿using System.Windows.Media.Imaging;
﻿using System.Windows.Navigation;
﻿using System.Windows.Shapes;

﻿namespace XRSvc.CustomControl
﻿{
﻿    /// <summary>
﻿    /// DeviceStyle.xaml 的交互逻辑
﻿    /// </summary>
﻿    public partial class DeviceStyle : UserControl
﻿    {
﻿        public static readonly DependencyProperty IsOnlineProperty = DependencyProperty.Register("IsOnline", typeof(bool), typeof(DeviceStyle), new PropertyMetadata(false));
        public static readonly DependencyProperty BatteryLevelProperty = DependencyProperty.Register("BatteryLevel", typeof(double), typeof(DeviceStyle), new PropertyMetadata(0.0));

﻿        public bool IsOnline
﻿        {
﻿            get { return (bool)GetValue(IsOnlineProperty); }
﻿            set { SetValue(IsOnlineProperty, value); }
﻿        }

﻿        public double BatteryLevel
﻿        {
﻿            get { return (double)GetValue(BatteryLevelProperty); }
﻿            set { SetValue(BatteryLevelProperty, value); }
﻿        }

﻿        public DeviceStyle()
﻿        {
﻿            InitializeComponent();
﻿        }
﻿    }
﻿}