package com.gzcec.xrandroidclient.utils;

import android.content.Context;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 日志管理器
 * 负责收集、存储、查看和清理应用日志
 */
public class LogManager {
    private static final String TAG = "LogManager";
    private static LogManager instance;
    
    private Context context;
    private File logDir;
    private ExecutorService executor;
    private ScheduledExecutorService cleanupScheduler;
    private ScheduledFuture<?> cleanupTask;
    private SimpleDateFormat dateFormat;
    private SimpleDateFormat fileNameFormat;
    
    // 日志配置
    private static final String LOG_DIR_NAME = "logs";
    private static final String LOG_FILE_PREFIX = "xr_log_";
    private static final String LOG_FILE_EXTENSION = ".txt";
    private static final int MAX_LOG_DAYS = 3; // 保留3天的日志
    private static final int MAX_LOG_LINES = 1000; // 每个日志文件最大行数
    
    private LogManager() {
        executor = Executors.newSingleThreadExecutor();
        cleanupScheduler = Executors.newSingleThreadScheduledExecutor();
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());
        fileNameFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized LogManager getInstance() {
        if (instance == null) {
            instance = new LogManager();
        }
        return instance;
    }
    
    /**
     * 初始化日志管理器
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        
        // 创建日志目录
        logDir = new File(context.getFilesDir(), LOG_DIR_NAME);
        if (!logDir.exists()) {
            boolean created = logDir.mkdirs();
            Log.d(TAG, "日志目录创建" + (created ? "成功" : "失败") + ": " + logDir.getAbsolutePath());
        }
        
        // 启动时清理旧日志
        cleanOldLogs();

        // 启动定时清理任务（每天执行一次）
        startScheduledCleanup();

        Log.d(TAG, "日志管理器初始化完成");
    }
    
    /**
     * 写入日志到文件
     */
    public void writeLog(String level, String tag, String message) {
        writeLog(level, tag, message, null);
    }

    /**
     * 写入日志到文件（带异常信息）
     */
    public void writeLog(String level, String tag, String message, Throwable throwable) {
        if (context == null || logDir == null) {
            return;
        }

        executor.execute(() -> {
            try {
                String timestamp = dateFormat.format(new Date());
                StringBuilder logEntry = new StringBuilder();
                logEntry.append(String.format("%s %s/%s: %s", timestamp, level, tag, message));

                // 添加异常信息
                if (throwable != null) {
                    logEntry.append("\n").append(getStackTraceString(throwable));
                }
                logEntry.append("\n");

                File logFile = getCurrentLogFile();

                // 检查文件大小，如果太大则创建新文件
                if (logFile.exists() && getLineCount(logFile) >= MAX_LOG_LINES) {
                    logFile = createNewLogFile();
                }

                // 写入日志
                try (FileWriter writer = new FileWriter(logFile, true)) {
                    writer.write(logEntry.toString());
                    writer.flush();
                }

            } catch (IOException e) {
                Log.e(TAG, "写入日志失败", e);
            }
        });
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTraceString(Throwable throwable) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
    
    /**
     * 获取当前日志文件
     */
    private File getCurrentLogFile() {
        String fileName = LOG_FILE_PREFIX + fileNameFormat.format(new Date()) + LOG_FILE_EXTENSION;
        return new File(logDir, fileName);
    }
    
    /**
     * 创建新的日志文件
     */
    private File createNewLogFile() {
        String timestamp = new SimpleDateFormat("yyyy-MM-dd_HHmmss", Locale.getDefault()).format(new Date());
        String fileName = LOG_FILE_PREFIX + timestamp + LOG_FILE_EXTENSION;
        return new File(logDir, fileName);
    }
    
    /**
     * 获取文件行数
     */
    private int getLineCount(File file) {
        int lines = 0;
        try (BufferedReader reader = new BufferedReader(new java.io.FileReader(file))) {
            while (reader.readLine() != null) {
                lines++;
            }
        } catch (IOException e) {
            Log.e(TAG, "读取文件行数失败", e);
        }
        return lines;
    }
    
    /**
     * 获取所有日志内容
     */
    public void getAllLogs(LogCallback callback) {
        executor.execute(() -> {
            List<String> allLogs = new ArrayList<>();
            
            try {
                File[] logFiles = logDir.listFiles((dir, name) -> 
                    name.startsWith(LOG_FILE_PREFIX) && name.endsWith(LOG_FILE_EXTENSION));
                
                if (logFiles != null) {
                    // 按文件名排序（时间顺序）
                    java.util.Arrays.sort(logFiles, (f1, f2) -> f1.getName().compareTo(f2.getName()));
                    
                    for (File logFile : logFiles) {
                        try (BufferedReader reader = new BufferedReader(new java.io.FileReader(logFile))) {
                            String line;
                            while ((line = reader.readLine()) != null) {
                                allLogs.add(line);
                            }
                        }
                    }
                }
                
                // 添加当前运行时日志
                List<String> runtimeLogs = getRuntimeLogs();
                allLogs.addAll(runtimeLogs);
                
            } catch (Exception e) {
                Log.e(TAG, "读取日志失败", e);
                allLogs.add("读取日志失败: " + e.getMessage());
            }
            
            // 回调结果
            if (callback != null) {
                callback.onLogsRetrieved(allLogs);
            }
        });
    }
    
    /**
     * 获取运行时日志（通过logcat）
     */
    private List<String> getRuntimeLogs() {
        List<String> logs = new ArrayList<>();

        try {
            // 获取当前应用的日志，包含更多级别
            String[] commands = {
                "logcat", "-d", "-v", "time",
                "--pid=" + android.os.Process.myPid(),
                "*:V"  // 获取所有级别的日志
            };

            Process process = Runtime.getRuntime().exec(commands);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            int lineCount = 0;
            while ((line = reader.readLine()) != null && lineCount < 1000) { // 增加到1000行
                // 过滤包含应用包名或相关标签的日志
                if (line.contains("com.gzcec.xrandroidclient") ||
                    line.contains("XRAndroidClient") ||
                    line.contains("MqttGameClient") ||
                    line.contains("DataSyncManager") ||
                    line.contains("DeviceStatusManager") ||
                    line.contains("LogManager") ||
                    line.contains("XRLog")) {
                    logs.add("[LOGCAT] " + line);
                    lineCount++;
                }
            }

            reader.close();
            process.destroy();

            if (logs.isEmpty()) {
                logs.add("[LOGCAT] 未找到应用相关的logcat日志");
            } else {
                logs.add(0, "[LOGCAT] ========== LOGCAT日志开始 ==========");
                logs.add("[LOGCAT] ========== LOGCAT日志结束 ==========");
            }

        } catch (Exception e) {
            Log.e(TAG, "获取运行时日志失败", e);
            logs.add("[LOGCAT] 获取运行时日志失败: " + e.getMessage());

            // 尝试备用方法
            try {
                Process process = Runtime.getRuntime().exec("logcat -d -s " +
                    "XRAndroidClient,MqttGameClient,MqttCommunicationManager,LogManager,XRLog");
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

                String line;
                int lineCount = 0;
                while ((line = reader.readLine()) != null && lineCount < 500) {
                    logs.add("[LOGCAT-BACKUP] " + line);
                    lineCount++;
                }

                reader.close();
                process.destroy();

            } catch (Exception e2) {
                logs.add("[LOGCAT] 备用方法也失败: " + e2.getMessage());
            }
        }

        return logs;
    }

    /**
     * 获取最近的logcat日志
     */
    public void getRecentLogcatLogs(LogCallback callback) {
        executor.execute(() -> {
            List<String> logs = new ArrayList<>();

            try {
                // 获取最近5分钟的日志
                String[] commands = {
                    "logcat", "-t", "300",  // 最近300行
                    "-v", "time",
                    "--pid=" + android.os.Process.myPid()
                };

                Process process = Runtime.getRuntime().exec(commands);
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("com.gzcec.xrandroidclient") ||
                        line.contains("XRAndroidClient") ||
                        line.contains("MqttGameClient") ||
                        line.contains("MqttCommunicationManager") ||
                        line.contains("LogManager") ||
                        line.contains("XRLog")) {
                        logs.add("[RECENT] " + line);
                    }
                }

                reader.close();
                process.destroy();

            } catch (Exception e) {
                Log.e(TAG, "获取最近logcat日志失败", e);
                logs.add("[RECENT] 获取最近logcat日志失败: " + e.getMessage());
            }

            if (callback != null) {
                callback.onLogsRetrieved(logs);
            }
        });
    }
    
    /**
     * 清理旧日志
     */
    public void cleanOldLogs() {
        executor.execute(() -> {
            try {
                long cutoffTime = System.currentTimeMillis() - (MAX_LOG_DAYS * 24 * 60 * 60 * 1000L);
                
                File[] logFiles = logDir.listFiles((dir, name) -> 
                    name.startsWith(LOG_FILE_PREFIX) && name.endsWith(LOG_FILE_EXTENSION));
                
                if (logFiles != null) {
                    int deletedCount = 0;
                    for (File logFile : logFiles) {
                        if (logFile.lastModified() < cutoffTime) {
                            if (logFile.delete()) {
                                deletedCount++;
                                Log.d(TAG, "删除旧日志文件: " + logFile.getName());
                            }
                        }
                    }
                    
                    if (deletedCount > 0) {
                        Log.i(TAG, "清理完成，删除了 " + deletedCount + " 个旧日志文件");
                    }
                }
                
            } catch (Exception e) {
                Log.e(TAG, "清理旧日志失败", e);
            }
        });
    }
    
    /**
     * 获取日志统计信息
     */
    public void getLogStatistics(StatisticsCallback callback) {
        executor.execute(() -> {
            try {
                File[] logFiles = logDir.listFiles((dir, name) -> 
                    name.startsWith(LOG_FILE_PREFIX) && name.endsWith(LOG_FILE_EXTENSION));
                
                int fileCount = logFiles != null ? logFiles.length : 0;
                long totalSize = 0;
                int totalLines = 0;
                
                if (logFiles != null) {
                    for (File file : logFiles) {
                        totalSize += file.length();
                        totalLines += getLineCount(file);
                    }
                }
                
                LogStatistics stats = new LogStatistics(fileCount, totalSize, totalLines);
                
                if (callback != null) {
                    callback.onStatisticsRetrieved(stats);
                }
                
            } catch (Exception e) {
                Log.e(TAG, "获取日志统计失败", e);
                if (callback != null) {
                    callback.onStatisticsRetrieved(new LogStatistics(0, 0, 0));
                }
            }
        });
    }
    
    /**
     * 清空所有日志
     */
    public void clearAllLogs() {
        executor.execute(() -> {
            try {
                File[] logFiles = logDir.listFiles((dir, name) -> 
                    name.startsWith(LOG_FILE_PREFIX) && name.endsWith(LOG_FILE_EXTENSION));
                
                if (logFiles != null) {
                    int deletedCount = 0;
                    for (File logFile : logFiles) {
                        if (logFile.delete()) {
                            deletedCount++;
                        }
                    }
                    Log.i(TAG, "清空日志完成，删除了 " + deletedCount + " 个日志文件");
                }
                
            } catch (Exception e) {
                Log.e(TAG, "清空日志失败", e);
            }
        });
    }
    
    /**
     * 启动定时清理任务
     */
    private void startScheduledCleanup() {
        // 每天凌晨2点执行清理任务
        long initialDelay = getInitialDelayToNextCleanup();
        long period = 24 * 60 * 60 * 1000L; // 24小时

        cleanupTask = cleanupScheduler.scheduleAtFixedRate(
            this::cleanOldLogs,
            initialDelay,
            period,
            TimeUnit.MILLISECONDS
        );

        Log.d(TAG, "定时清理任务已启动，下次清理时间: " +
            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                .format(new Date(System.currentTimeMillis() + initialDelay)));
    }

    /**
     * 计算到下次清理的初始延迟时间
     */
    private long getInitialDelayToNextCleanup() {
        java.util.Calendar now = java.util.Calendar.getInstance();
        java.util.Calendar nextCleanup = java.util.Calendar.getInstance();

        // 设置为今天凌晨2点
        nextCleanup.set(java.util.Calendar.HOUR_OF_DAY, 2);
        nextCleanup.set(java.util.Calendar.MINUTE, 0);
        nextCleanup.set(java.util.Calendar.SECOND, 0);
        nextCleanup.set(java.util.Calendar.MILLISECOND, 0);

        // 如果已经过了今天的2点，则设置为明天的2点
        if (nextCleanup.before(now)) {
            nextCleanup.add(java.util.Calendar.DAY_OF_MONTH, 1);
        }

        return nextCleanup.getTimeInMillis() - now.getTimeInMillis();
    }

    /**
     * 停止定时清理任务
     */
    private void stopScheduledCleanup() {
        if (cleanupTask != null && !cleanupTask.isCancelled()) {
            cleanupTask.cancel(false);
            cleanupTask = null;
            Log.d(TAG, "定时清理任务已停止");
        }
    }

    /**
     * 关闭日志管理器
     */
    public void shutdown() {
        // 停止定时清理任务
        stopScheduledCleanup();

        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }

        if (cleanupScheduler != null && !cleanupScheduler.isShutdown()) {
            cleanupScheduler.shutdown();
            try {
                if (!cleanupScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupScheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupScheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    // ========== 回调接口 ==========
    
    /**
     * 日志回调接口
     */
    public interface LogCallback {
        void onLogsRetrieved(List<String> logs);
    }
    
    /**
     * 统计信息回调接口
     */
    public interface StatisticsCallback {
        void onStatisticsRetrieved(LogStatistics statistics);
    }
    
    /**
     * 日志统计信息
     */
    public static class LogStatistics {
        public final int fileCount;
        public final long totalSize;
        public final int totalLines;
        
        public LogStatistics(int fileCount, long totalSize, int totalLines) {
            this.fileCount = fileCount;
            this.totalSize = totalSize;
            this.totalLines = totalLines;
        }
        
        public String getFormattedSize() {
            if (totalSize < 1024) {
                return totalSize + " B";
            } else if (totalSize < 1024 * 1024) {
                return String.format("%.1f KB", totalSize / 1024.0);
            } else {
                return String.format("%.1f MB", totalSize / (1024.0 * 1024.0));
            }
        }
        
        @Override
        public String toString() {
            return String.format("文件数: %d, 大小: %s, 行数: %d", 
                fileCount, getFormattedSize(), totalLines);
        }
    }
}
