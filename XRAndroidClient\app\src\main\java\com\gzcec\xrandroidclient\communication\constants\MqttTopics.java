package com.gzcec.xrandroidclient.communication.constants;

/**
 * MQTT主题定义（Android端）
 * 与PC端保持一致的主题结构
 */
public class MqttTopics {
    
    // 基础主题
    public static final String ROOT = "xr_system";
    public static final String GAME_CONTROL = ROOT + "/game_control";
    public static final String DEVICE_MANAGEMENT = ROOT + "/device_management";
    public static final String SYSTEM_STATUS = ROOT + "/system_status";
    
    // 游戏控制主题
    public static final String GAME_START_REQUEST = GAME_CONTROL + "/start_request";
    public static final String GAME_START_RESPONSE = GAME_CONTROL + "/start_response";
    public static final String GAME_STOP_REQUEST = GAME_CONTROL + "/stop_request";
    public static final String GAME_STOP_RESPONSE = GAME_CONTROL + "/stop_response";
    public static final String GAME_PROGRESS_UPDATE = GAME_CONTROL + "/progress_update";
    public static final String GAME_SERVER_STATUS = GAME_CONTROL + "/server_status";
    
    // 设备管理主题
    public static final String DEVICE_LIST_REQUEST = DEVICE_MANAGEMENT + "/list_request";
    public static final String DEVICE_LIST_RESPONSE = DEVICE_MANAGEMENT + "/list_response";
    public static final String DEVICE_STATUS_UPDATE = DEVICE_MANAGEMENT + "/status_update";
    public static final String DEVICE_SELECTION_CHANGED = DEVICE_MANAGEMENT + "/selection_changed";
    public static final String DEVICE_BATTERY_STATUS = DEVICE_MANAGEMENT + "/battery_status";
    
    // 系统状态主题
    public static final String HEARTBEAT = SYSTEM_STATUS + "/heartbeat";
    public static final String SYSTEM_ERROR = SYSTEM_STATUS + "/error";
    public static final String CONNECTION_STATUS = SYSTEM_STATUS + "/connection";
    public static final String SYSTEM_LOG = SYSTEM_STATUS + "/log";
    
    /**
     * 获取PC端需要订阅的主题列表
     */
    public static String[] getPCSubscriptionTopics() {
        return new String[]{
            GAME_START_REQUEST,
            GAME_STOP_REQUEST,
            DEVICE_LIST_REQUEST,
            DEVICE_SELECTION_CHANGED,
            HEARTBEAT,
            CONNECTION_STATUS
        };
    }
    
    /**
     * 获取Android端需要订阅的主题列表
     */
    public static String[] getAndroidSubscriptionTopics() {
        return new String[]{
            GAME_START_RESPONSE,
            GAME_STOP_RESPONSE,
            GAME_PROGRESS_UPDATE,
            GAME_SERVER_STATUS,
            DEVICE_LIST_RESPONSE,
            DEVICE_STATUS_UPDATE,
            DEVICE_BATTERY_STATUS,
            SYSTEM_ERROR,
            HEARTBEAT,
            CONNECTION_STATUS,
            SYSTEM_LOG
        };
    }
    
    /**
     * 构建设备特定主题
     */
    public static String buildDeviceTopic(String baseTopic, String deviceId) {
        return baseTopic + "/device/" + deviceId;
    }
    
    /**
     * 构建游戏特定主题
     */
    public static String buildGameTopic(String baseTopic, String gameId) {
        return baseTopic + "/game/" + gameId;
    }
    
    /**
     * 构建客户端特定主题
     */
    public static String buildClientTopic(String baseTopic, String clientId) {
        return baseTopic + "/client/" + clientId;
    }
    
    /**
     * 验证主题格式是否正确
     */
    public static boolean isValidTopic(String topic) {
        if (topic == null || topic.isEmpty()) return false;
        if (!topic.startsWith(ROOT)) return false;
        
        // 检查主题层级不超过10层
        String[] parts = topic.split("/");
        return parts.length <= 10;
    }
}
