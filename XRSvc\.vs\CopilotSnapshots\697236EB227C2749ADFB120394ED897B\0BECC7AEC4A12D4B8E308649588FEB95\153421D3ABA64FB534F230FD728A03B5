﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Data;
using System.Threading;
using System.Threading.Tasks;
using XRSvc.CustomControl;
using XRSvc.DataPack; // Ensure GameInfo is referenced correctly
using XRSvc.ViewModels;
using XRSvc.DataSource;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public ICommand SaveGameNameCommand { get; }

        private GameViewModel _gameViewModel;
        private DeviceViewModel _deviceViewModel;
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        public GameViewModel GameViewModel => _gameViewModel;
        public DeviceViewModel DeviceViewModel => _deviceViewModel;

        public MainWindow()
        {
            InitializeComponent();

            _gameViewModel = new GameViewModel();
            _deviceViewModel = new DeviceViewModel();

            this.DataContext = this;

            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            try
            {
                _gameViewModel.IsLoading = true;
                _deviceViewModel.IsLoading = true;

                await Task.WhenAll(
                    _gameViewModel.LoadDefaultGamesAsync(_cancellationTokenSource.Token),
                    _deviceViewModel.LoadDefaultDevicesAsync(_cancellationTokenSource.Token)
                );
            }
            catch (OperationCanceledException)
            {
                // Handle cancellation if needed
            }
            finally
            {
                _gameViewModel.IsLoading = false;
                _deviceViewModel.IsLoading = false;
            }
        }

        private void SaveGameName(GameSource gameSource)
        {
            if (gameSource != null)
            {
                MessageBox.Show($"游戏名称已修改为: {gameSource.Name}", "修改成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            base.OnClosed(e);
        }
    }
}
