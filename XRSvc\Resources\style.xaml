﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:input="clr-namespace:System.Windows.Input;assembly=PresentationCore"
                   xmlns:Themes="clr-namespace:Microsoft.Windows.Themes;assembly=PresentationFramework.Aero2">

    <!--全局字体-->
    <Style TargetType="{x:Type TextElement }">
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
    </Style>
    <Style TargetType="{x:Type TextBlock }">
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
    </Style>

    <!--按钮全局风格-->
    <!--<Style TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border Background="{TemplateBinding Background}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="Red"/>
            </Trigger>
        </Style.Triggers>
    </Style>-->

    <!--标题栏风格-->
    <Style x:Key="TitleBarStyle" TargetType="{x:Type Grid}">
        <Setter Property="Background" Value="#FF2D2D30"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FF4A505F"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--关闭按钮 part 1-->
    <Style x:Key="WinCloseStyle" TargetType="{x:Type Grid}">
        <Setter Property="Background" Value="#363941"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#3F3F41"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--关闭按钮 part 2-->
    <Style x:Key="LineStyle" TargetType="{x:Type Line}">
        <Setter Property="Stroke" Value="white"/>
        <Setter Property="StrokeThickness" Value="0.7"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Style.Triggers>
            <DataTrigger Binding="{Binding ElementName=Btn_WinClose,Path=IsMouseOver}" Value="True">
                <Setter Property="Stroke" Value="White"/>
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <!--TextBox-->
    <Style x:Key="TextBoxStyle" TargetType="{x:Type TextBox}">
        <Style.Setters>
            <Setter Property="Foreground" Value="#F1F1EC"/>
            <Setter Property="Background" Value="#333337"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="5,0,0,0"/>
            <Setter Property="Margin" Value="0,0,0,0"/>
            <Setter Property="TextWrapping" Value="NoWrap"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="input:InputMethod.IsInputMethodEnabled" Value="False"/>
        </Style.Setters>
    </Style>

    <!--TextBlock-->
    <Style x:Key="TextBlockStyle" TargetType="{x:Type TextBlock}">
        <Style.Setters>
            <Setter Property="Foreground" Value="#F1F1EC"/>
            <!--<Setter Property="Background" Value="#252526"/>-->
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style.Setters>
    </Style>

    <!--FocusVisual-->
    <Style x:Key="FocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="2" SnapsToDevicePixels="true" Stroke="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" StrokeThickness="1" StrokeDashArray="1 2"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--CheckBox 开始-->

    <Style x:Key="OptionMarkFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="14,0,0,0" SnapsToDevicePixels="true" Stroke="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" StrokeThickness="1" StrokeDashArray="1 2"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--控制单选框里面的背景色-->
    <SolidColorBrush x:Key="OptionMark.Static.Background"/>
    <!--控制单选框的边框颜色-->
    <SolidColorBrush x:Key="OptionMark.Static.Border" Color="#FFF6D18D"/>

    <!--控制鼠标经过单选框时单选框内部的颜色-->
    <SolidColorBrush x:Key="OptionMark.MouseOver.Background"/>
    <!--控制单选框的边框颜色-->
    <SolidColorBrush x:Key="OptionMark.MouseOver.Border" Color="#FFF6D18D"/>
    <!--控制打勾之后，鼠标经过时钩子的颜色-->
    <SolidColorBrush x:Key="OptionMark.MouseOver.Glyph" Color="White"/>

    <SolidColorBrush x:Key="OptionMark.Disabled.Background" Color="#FFE6E6E6"/>

    <SolidColorBrush x:Key="OptionMark.Disabled.Border" Color="#FFBCBCBC"/>

    <SolidColorBrush x:Key="OptionMark.Disabled.Glyph" Color="#FF707070"/>
    <!--控制鼠标点击单选框时，单选框的背景色-->
    <SolidColorBrush x:Key="OptionMark.Pressed.Background"/>
    <!--控制鼠标点击单选框时，单选框的边框颜色-->
    <SolidColorBrush x:Key="OptionMark.Pressed.Border" Color="#FFF6D18D"/>
    <!--控制鼠标点击单选框，进行取消选择的操作时，点击时的钩子颜色-->
    <SolidColorBrush x:Key="OptionMark.Pressed.Glyph" Color="White"/>
    <!--控制打勾后，鼠标离开后，钩子的颜色-->
    <SolidColorBrush x:Key="OptionMark.Static.Glyph" Color="White"/>


    <Style x:Key="CheckBoxStyle1" TargetType="{x:Type CheckBox}">
        <Setter Property="FocusVisualStyle" Value="{StaticResource FocusVisual}"/>
        <Setter Property="Background" Value="{StaticResource OptionMark.Static.Background}"/>
        <Setter Property="BorderBrush" Value="{StaticResource OptionMark.Static.Border}"/>
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <!--文本垂直居中-->
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type CheckBox}">
                    <!--此处的Background，控制整个CheckBox的背景颜色-->
                    <Grid x:Name="templateRoot" Background="Transparent" SnapsToDevicePixels="True">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border x:Name="checkBoxBorder" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="1" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" CornerRadius="1,1,1,1">
                            <Grid x:Name="markGrid">
                                <!--此处的Margin控制单选框的大小-->
                                <Path x:Name="optionMark" Data="F1 M 9.97498,1.22334L 4.6983,9.09834L 4.52164,9.09834L 0,5.19331L 1.27664,3.52165L 4.255,6.08833L 8.33331,1.52588e-005L 9.97498,1.22334 Z " Fill="{StaticResource OptionMark.Static.Glyph}" Margin="3" Opacity="0" Stretch="None"/>
                                <!--此处控制的是单选框内部钩子的矩形框-->
                                <Rectangle x:Name="indeterminateMark" Fill="{StaticResource OptionMark.Static.Glyph}" Margin="2" Opacity="0"/>
                            </Grid>
                        </Border>
                        <ContentPresenter x:Name="contentPresenter" Grid.Column="1" Focusable="False" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasContent" Value="true">
                            <Setter Property="FocusVisualStyle" Value="{StaticResource OptionMarkFocusVisual}"/>
                            <Setter Property="Padding" Value="4,-1,0,0"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource OptionMark.MouseOver.Background}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource OptionMark.MouseOver.Border}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource OptionMark.MouseOver.Glyph}"/>
                            <Setter Property="Fill" TargetName="indeterminateMark" Value="{StaticResource OptionMark.MouseOver.Glyph}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource OptionMark.Disabled.Background}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource OptionMark.Disabled.Border}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource OptionMark.Disabled.Glyph}"/>
                            <Setter Property="Fill" TargetName="indeterminateMark" Value="{StaticResource OptionMark.Disabled.Glyph}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="Background" TargetName="checkBoxBorder" Value="{StaticResource OptionMark.Pressed.Background}"/>
                            <Setter Property="BorderBrush" TargetName="checkBoxBorder" Value="{StaticResource OptionMark.Pressed.Border}"/>
                            <Setter Property="Fill" TargetName="optionMark" Value="{StaticResource OptionMark.Pressed.Glyph}"/>
                            <Setter Property="Fill" TargetName="indeterminateMark" Value="{StaticResource OptionMark.Pressed.Glyph}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="true">
                            <Setter Property="Opacity" TargetName="optionMark" Value="1"/>
                            <Setter Property="Opacity" TargetName="indeterminateMark" Value="0"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="{x:Null}">
                            <Setter Property="Opacity" TargetName="optionMark" Value="0"/>
                            <Setter Property="Opacity" TargetName="indeterminateMark" Value="1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--CheckBox结束-->


    <!--  ######################## GroupBox部分 ######################### -->

    <BorderGapMaskConverter x:Key="BorderGapMaskConverter"/>

    <Style x:Key="GroupboxWithoutWhiteBorder" TargetType="{x:Type GroupBox}">
        <Setter Property="BorderBrush" Value="#D5DFE5" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type GroupBox}">
                    <Grid SnapsToDevicePixels="true">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="6" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="6" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="6" />
                        </Grid.RowDefinitions>
                        <Border CornerRadius="4" Grid.Row="1" Grid.RowSpan="3" Grid.Column="0" Grid.ColumnSpan="4" BorderThickness="{TemplateBinding BorderThickness}" BorderBrush="Transparent" Background="{TemplateBinding Background}" />
                        <Border Name="Header" Padding="3,1,3,0" Grid.Row="0" Grid.RowSpan="2" Grid.Column="1">
                            <ContentPresenter ContentSource="Header" RecognizesAccessKey="true" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>
                        <ContentPresenter Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="{TemplateBinding Padding}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        <Border Grid.Row="1" Grid.RowSpan="3" Grid.ColumnSpan="4"  BorderThickness="{TemplateBinding BorderThickness}" BorderBrush="{TemplateBinding BorderBrush}" CornerRadius="3">
                            <Border.OpacityMask>
                                <MultiBinding Converter="{StaticResource BorderGapMaskConverter}" ConverterParameter="7">
                                    <Binding ElementName="Header" Path="ActualWidth" />
                                    <Binding Path="ActualWidth" RelativeSource="{RelativeSource Self}" />
                                    <Binding Path="ActualHeight" RelativeSource="{RelativeSource Self}" />
                                </MultiBinding>
                            </Border.OpacityMask>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  ######################## 下拉框部分 ######################### -->

    <!--下拉框开始-->

    <LinearGradientBrush x:Key="ComboBox.Static.Background" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FFF0F0F0" Offset="0.0"/>
        <GradientStop Color="#FFE5E5E5" Offset="1.0"/>
    </LinearGradientBrush>

    <!--控制鼠标离开后，主体框的边框颜色-->
    <SolidColorBrush x:Key="ComboBox.Static.Border" Color="White"/>

    <SolidColorBrush x:Key="ComboBox.Static.Editable.Background" Color="#FFFFFFFF"/>

    <SolidColorBrush x:Key="ComboBox.Static.Editable.Border" Color="#FFABADB3"/>

    <SolidColorBrush x:Key="ComboBox.Static.Editable.Button.Background" Color="Transparent"/>

    <SolidColorBrush x:Key="ComboBox.Static.Editable.Button.Border" Color="Transparent"/>
    <!--控制鼠标经过时，下拉箭头的颜色-->
    <SolidColorBrush x:Key="ComboBox.MouseOver.Glyph" Color="White"/>

    <!--控制鼠标经过时，主体框的背景颜色-->
    <LinearGradientBrush x:Key="ComboBox.MouseOver.Background">
        <GradientStop Color="#FF5A5A62"/>
        <GradientStop Color="#FF5A5A62"/>
    </LinearGradientBrush>

    <!--鼠标经过时主体框的边框颜色-->
    <SolidColorBrush x:Key="ComboBox.MouseOver.Border" Color="#FF7EB4EA"/>

    <SolidColorBrush x:Key="ComboBox.MouseOver.Editable.Background" Color="#FFFFFFFF"/>
    <SolidColorBrush x:Key="ComboBox.MouseOver.Editable.Border" Color="#FF7EB4EA"/>

    <LinearGradientBrush x:Key="ComboBox.MouseOver.Editable.Button.Background" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FFEBF4FC" Offset="0.0"/>
        <GradientStop Color="#FFDCECFC" Offset="1.0"/>
    </LinearGradientBrush>

    <!--长按主体框的时候，下拉箭头的颜色-->
    <SolidColorBrush x:Key="ComboBox.MouseOver.Editable.Button.Border" Color="White"/>
    <SolidColorBrush x:Key="ComboBox.Pressed.Glyph" Color="White"/>

    <!--长按主体框的时候，主体框的背景颜色-->
    <LinearGradientBrush x:Key="ComboBox.Pressed.Background">
        <GradientStop Color="#FF5A5A62"/>
        <GradientStop Color="#FF5A5A62"/>
    </LinearGradientBrush>

    <!--长按主体框时，主体框的边框颜色-->
    <SolidColorBrush x:Key="ComboBox.Pressed.Border" Color="#FF569DE5"/>
    <SolidColorBrush x:Key="ComboBox.Pressed.Editable.Background" Color="#FFFFFFFF"/>
    <SolidColorBrush x:Key="ComboBox.Pressed.Editable.Border" Color="#FF569DE5"/>

    <LinearGradientBrush x:Key="ComboBox.Pressed.Editable.Button.Background" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FFDAEBFC" Offset="0.0"/>
        <GradientStop Color="#FFC4E0FC" Offset="1.0"/>
    </LinearGradientBrush>


    <SolidColorBrush x:Key="ComboBox.Pressed.Editable.Button.Border" Color="#FF569DE5"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Glyph" Color="#FFBFBFBF"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Background" Color="#FF5A5A62"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Border" Color="#FF979191"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Editable.Background" Color="#FFFFFFFF"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Editable.Border" Color="#FFBFBFBF"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Editable.Button.Background" Color="Transparent"/>

    <SolidColorBrush x:Key="ComboBox.Disabled.Editable.Button.Border" Color="Yellow"/>
    <!--控制鼠标离开主体框时下拉箭头的颜色-->
    <SolidColorBrush x:Key="ComboBox.Static.Glyph" Color="White"/>


    <Style x:Key="ComboBoxToggleButton" TargetType="{x:Type ToggleButton}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Focusable" Value="false"/>
        <Setter Property="ClickMode" Value="Press"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">

                    <!--控制主体框-->
                    <Border x:Name="templateRoot" BorderBrush="Gray" BorderThickness="1" Background="#FF5A5A62" SnapsToDevicePixels="true" CornerRadius="3">
                        <!--控制下拉箭头-->
                        <Border x:Name="splitBorder" BorderBrush="Gray" BorderThickness="0" HorizontalAlignment="Right" Margin="0" SnapsToDevicePixels="true" Width="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}">
                            <!--Path画下拉箭头-->
                            <Path x:Name="arrow" Data="M 5,0 L 10,7 L 0,7 z" Fill="White" HorizontalAlignment="Center" Margin="5,18,0,0" VerticalAlignment="Center">
                                <Path.RenderTransform>
                                    <TransformGroup>
                                        <ScaleTransform/>
                                        <SkewTransform/>
                                        <RotateTransform Angle="180"/>
                                        <TranslateTransform/>
                                    </TransformGroup>
                                </Path.RenderTransform>
                            </Path>
                        </Border>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="true"/>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="false"/>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Self}}" Value="false"/>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Self}}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.Static.Editable.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.Static.Editable.Border}"/>
                            <Setter Property="Background" TargetName="splitBorder" Value="{StaticResource ComboBox.Static.Editable.Button.Background}"/>
                            <Setter Property="BorderBrush" TargetName="splitBorder" Value="{StaticResource ComboBox.Static.Editable.Button.Border}"/>
                        </MultiDataTrigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Fill" TargetName="arrow" Value="{StaticResource ComboBox.MouseOver.Glyph}"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="true"/>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="false"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.MouseOver.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.MouseOver.Border}"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Self}}" Value="true"/>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.MouseOver.Editable.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.MouseOver.Editable.Border}"/>
                            <Setter Property="Background" TargetName="splitBorder" Value="{StaticResource ComboBox.MouseOver.Editable.Button.Background}"/>
                            <Setter Property="BorderBrush" TargetName="splitBorder" Value="{StaticResource ComboBox.MouseOver.Editable.Button.Border}"/>
                        </MultiDataTrigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="Fill" TargetName="arrow" Value="{StaticResource ComboBox.Pressed.Glyph}"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Self}}" Value="true"/>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="false"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.Pressed.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.Pressed.Border}"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsPressed, RelativeSource={RelativeSource Self}}" Value="true"/>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.Pressed.Editable.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.Pressed.Editable.Border}"/>
                            <Setter Property="Background" TargetName="splitBorder" Value="{StaticResource ComboBox.Pressed.Editable.Button.Background}"/>
                            <Setter Property="BorderBrush" TargetName="splitBorder" Value="{StaticResource ComboBox.Pressed.Editable.Button.Border}"/>
                        </MultiDataTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Fill" TargetName="arrow" Value="{StaticResource ComboBox.Disabled.Glyph}"/>
                        </Trigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Self}}" Value="false"/>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="false"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.Disabled.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.Disabled.Border}"/>
                        </MultiDataTrigger>
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding IsEnabled, RelativeSource={RelativeSource Self}}" Value="false"/>
                                <Condition Binding="{Binding IsEditable, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}" Value="true"/>
                            </MultiDataTrigger.Conditions>
                            <Setter Property="Background" TargetName="templateRoot" Value="{StaticResource ComboBox.Disabled.Editable.Background}"/>
                            <Setter Property="BorderBrush" TargetName="templateRoot" Value="{StaticResource ComboBox.Disabled.Editable.Border}"/>
                            <Setter Property="Background" TargetName="splitBorder" Value="{StaticResource ComboBox.Disabled.Editable.Button.Background}"/>
                            <Setter Property="BorderBrush" TargetName="splitBorder" Value="{StaticResource ComboBox.Disabled.Editable.Button.Border}"/>
                        </MultiDataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="ComboBoxTemplate" TargetType="{x:Type ComboBox}">
        <Grid x:Name="templateRoot" SnapsToDevicePixels="true">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition MinWidth="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" Width="0"/>
            </Grid.ColumnDefinitions>
            <Popup x:Name="PART_Popup" AllowsTransparency="true" Grid.ColumnSpan="2" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{DynamicResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                <Themes:SystemDropShadowChrome x:Name="shadow" Color="Transparent" MaxHeight="{TemplateBinding MaxDropDownHeight}" MinWidth="{Binding ActualWidth, ElementName=templateRoot}">

                    <!--控制下拉选框-->
                    <Border x:Name="dropDownBorder" BorderBrush="Gray" BorderThickness="1" Background="#FFDEA02C">
                        <ScrollViewer x:Name="DropDownScrollViewer">
                            <Grid x:Name="grid" RenderOptions.ClearTypeHint="Enabled">
                                <Canvas x:Name="canvas" HorizontalAlignment="Left" Height="0" VerticalAlignment="Top" Width="0">
                                    <Rectangle x:Name="opaqueRect" Fill="#FF3C3B3A" Height="{Binding ActualHeight, ElementName=dropDownBorder}" Width="{Binding ActualWidth, ElementName=dropDownBorder}"/>
                                </Canvas>
                                <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </Grid>
                        </ScrollViewer>
                    </Border>
                </Themes:SystemDropShadowChrome>
            </Popup>
            <ToggleButton x:Name="toggleButton" BorderBrush="White" BorderThickness="1" Background="#FF5A5A62" Grid.ColumnSpan="2" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxToggleButton}"/>
            <ContentPresenter x:Name="contentPresenter" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" Content="{TemplateBinding SelectionBoxItem}" ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsHitTestVisible="false" Margin="{TemplateBinding Padding}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasDropShadow" SourceName="PART_Popup" Value="true">
                <Setter Property="Margin" TargetName="shadow" Value="0,0,5,5"/>
                <Setter Property="Color" TargetName="shadow" Value="#71000000"/>
            </Trigger>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsGrouping" Value="true"/>
                    <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping" Value="false"/>
                </MultiTrigger.Conditions>
                <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
            </MultiTrigger>
            <Trigger Property="ScrollViewer.CanContentScroll" SourceName="DropDownScrollViewer" Value="false">
                <Setter Property="Canvas.Top" TargetName="opaqueRect" Value="{Binding VerticalOffset, ElementName=DropDownScrollViewer}"/>
                <Setter Property="Canvas.Left" TargetName="opaqueRect" Value="{Binding HorizontalOffset, ElementName=DropDownScrollViewer}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <SolidColorBrush x:Key="TextBox.Static.Background" Color="#FFFFFFFF"/>


    <Style x:Key="ComboBoxEditableTextBox" TargetType="{x:Type TextBox}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="MinWidth" Value="0"/>
        <Setter Property="MinHeight" Value="0"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <ScrollViewer x:Name="PART_ContentHost" Background="Transparent" Focusable="false" HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="ComboBoxEditableTemplate" TargetType="{x:Type ComboBox}">
        <Grid x:Name="templateRoot" SnapsToDevicePixels="true">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition MinWidth="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" Width="0"/>
            </Grid.ColumnDefinitions>
            <Popup x:Name="PART_Popup" AllowsTransparency="true" Grid.ColumnSpan="2" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{DynamicResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                <Themes:SystemDropShadowChrome x:Name="shadow" Color="Transparent" MaxHeight="{TemplateBinding MaxDropDownHeight}" MinWidth="{Binding ActualWidth, ElementName=templateRoot}">
                    <Border x:Name="dropDownBorder" BorderBrush="{DynamicResource {x:Static SystemColors.WindowFrameBrushKey}}" BorderThickness="1" Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}">
                        <ScrollViewer x:Name="DropDownScrollViewer">
                            <Grid x:Name="grid" RenderOptions.ClearTypeHint="Enabled">
                                <Canvas x:Name="canvas" HorizontalAlignment="Left" Height="0" VerticalAlignment="Top" Width="0">
                                    <Rectangle x:Name="opaqueRect" Fill="{Binding Background, ElementName=dropDownBorder}" Height="{Binding ActualHeight, ElementName=dropDownBorder}" Width="{Binding ActualWidth, ElementName=dropDownBorder}"/>
                                </Canvas>
                                <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </Grid>
                        </ScrollViewer>
                    </Border>
                </Themes:SystemDropShadowChrome>
            </Popup>
            <ToggleButton x:Name="toggleButton" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Grid.ColumnSpan="2" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxToggleButton}"/>
            <Border x:Name="border" Background="{StaticResource TextBox.Static.Background}" Margin="{TemplateBinding BorderThickness}">
                <TextBox x:Name="PART_EditableTextBox" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Margin="{TemplateBinding Padding}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"/>
            </Border>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" TargetName="border" Value="0.56"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocusWithin" Value="true">
                <Setter Property="Foreground" Value="Black"/>
            </Trigger>
            <Trigger Property="HasDropShadow" SourceName="PART_Popup" Value="true">
                <Setter Property="Margin" TargetName="shadow" Value="0,0,5,5"/>
                <Setter Property="Color" TargetName="shadow" Value="#71000000"/>
            </Trigger>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsGrouping" Value="true"/>
                    <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping" Value="false"/>
                </MultiTrigger.Conditions>
                <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
            </MultiTrigger>
            <Trigger Property="ScrollViewer.CanContentScroll" SourceName="DropDownScrollViewer" Value="false">
                <Setter Property="Canvas.Top" TargetName="opaqueRect" Value="{Binding VerticalOffset, ElementName=DropDownScrollViewer}"/>
                <Setter Property="Canvas.Left" TargetName="opaqueRect" Value="{Binding HorizontalOffset, ElementName=DropDownScrollViewer}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="ComboBoxItemStyle" TargetType="ComboBoxItem">
        <Setter Property="MinHeight" Value="22"></Setter>
        <Setter Property="MinWidth" Value="60"></Setter>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBoxItem">
                    <Border Name="Back" Background="Transparent"  BorderThickness="0,0,0,0" BorderBrush="#81D779" >
                        <ContentPresenter ContentSource="{Binding Source}" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="5,0,0,0"></ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Back" Property="Background" Value="LightGray"></Setter>
                        </Trigger>
                        <Trigger Property="IsHighlighted" Value="True">
                            <Setter TargetName="Back" Property="Background" Value="LightGray"></Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ComboBoxStyle1" TargetType="{x:Type ComboBox}">
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <!--ComboBoxItem-->
                <Style TargetType="ComboBoxItem">
                    <Setter Property="MinHeight" Value="22"></Setter>
                    <Setter Property="MinWidth" Value="60"></Setter>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ComboBoxItem">
                                <!--Background，控制下拉框的背景颜色-->
                                <Border Name="Back" Background="Transparent"  BorderThickness="0,0,0,0" BorderBrush="#81D779">
                                    <ContentPresenter ContentSource="{TemplateBinding Content}" VerticalAlignment="Center" HorizontalAlignment="Left" Height="22" Margin="5,10,0,0"></ContentPresenter>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Back" Property="Background" Value="#000000"></Setter>

                                    </Trigger>

                                    <!--Value="Gray",修改这一处的值，则可以改变鼠标经过下拉框时，当下选项的背景色-->
                                    <Trigger Property="IsHighlighted" Value="True">
                                        <Setter TargetName="Back" Property="Background" Value="#775A5A62"></Setter>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="FocusVisualStyle" Value="{StaticResource FocusVisual}"/>
        <Setter Property="Background" Value="{StaticResource ComboBox.Static.Background}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ComboBox.Static.Border}"/>
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="Padding" Value="6,3,5,3"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Template" Value="{StaticResource ComboBoxTemplate}"/>

        <Style.Triggers>
            <Trigger Property="IsEditable" Value="true">
                <Setter Property="IsTabStop" Value="false"/>
                <Setter Property="Padding" Value="2"/>
                <Setter Property="Template" Value="{StaticResource ComboBoxEditableTemplate}"/>
            </Trigger>
        </Style.Triggers>

    </Style>


    <!--下拉框结束-->

    <!--  ######################## 菜单部分 ######################### -->

    <!--菜单部分-->
    <Style TargetType="{x:Type MenuItem}">
        <Setter Property="Background" Value="#2D2D30" />
        <Setter Property="Foreground" Value="white" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type MenuItem}">
                    <Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="0">
                        <Grid Background="{TemplateBinding Background}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition x:Name="Col0" SharedSizeGroup="MenuItemIconColumnGroup" MinWidth="30" Width="Auto"/>
                                <ColumnDefinition x:Name="col1" Width="Auto"  SharedSizeGroup="MenuTextColumnGroup"/>
                                <ColumnDefinition Width="Auto"  SharedSizeGroup="MenuItemIGTColumnGroup"/>
                                <ColumnDefinition x:Name="Col3" Width="14"/>
                            </Grid.ColumnDefinitions>

                            <!-- ContentPresenter to show an Icon if needed -->
                            <ContentPresenter Grid.Column="0" Margin="5,0,6,0" x:Name="Icon" HorizontalAlignment="Left" VerticalAlignment="Center" ContentSource="Icon"/>

                            <!-- Glyph is a checkmark if needed for a checkable menu -->
                            <Grid Grid.Column="0" Visibility="Hidden" Margin="4,0,6,0" x:Name="GlyphPanel" VerticalAlignment="Center">
                                <Path x:Name="GlyphPanelpath" VerticalAlignment="Center" Fill="{TemplateBinding Foreground}" 
                                          Data="M0,2 L0,4.8 L2.5,7.4 L7.1,2.8 L7.1,0 L2.5,4.6 z" FlowDirection="LeftToRight"/>
                            </Grid>

                            <!-- Content for the menu text etc -->
                            <ContentPresenter Grid.Column="1"
                                          Margin="{TemplateBinding Padding}"
                                          x:Name="HeaderHost"
                                          RecognizesAccessKey="True"
                                          VerticalAlignment="Center"
                                          ContentSource="Header"/>

                            <!-- Content for the menu IGT -->
                            <ContentPresenter Grid.Column="2"
                                          Margin="0,1,8,1"
                                          x:Name="IGTHost"
                                          ContentSource="InputGestureText"
                                          VerticalAlignment="Center"/>

                            <!-- Arrow drawn path which points to the next level of the menu -->
                            <Grid Grid.Column="3" Margin="4,0,6,0" x:Name="ArrowPanel" VerticalAlignment="Center">
                                <Path x:Name="ArrowPanelPath" HorizontalAlignment="Right" VerticalAlignment="Center" 
                                          Fill="{TemplateBinding Foreground}" Data="M0,0 L0,8 L4,4 z"/>
                            </Grid>

                            <!-- The Popup is the body of the menu which expands 
                                     down or across depending on the level of the item -->
                            <Popup IsOpen="{Binding Path=IsSubmenuOpen, RelativeSource ={RelativeSource TemplatedParent}}" 
                                       Placement="Right" x:Name="SubMenuPopup" Focusable="false" 
                                       PopupAnimation="{DynamicResource {x:Static SystemParameters.MenuPopupAnimationKey}}">
                                <Border x:Name="SubMenuBorder" BorderBrush="{Binding Path=Background,RelativeSource={RelativeSource AncestorType={x:Type Menu}}}" BorderThickness="0">
                                    <Grid x:Name="SubMenu" Grid.IsSharedSizeScope="True">
                                        <!-- StackPanel holds children of the menu. This is set by IsItemsHost=True  KeyboardNavigationMode = "Cycle" -->
                                        <StackPanel x:Name="dsdfs" IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Cycle"/>
                                    </Grid>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>



                    <!-- These triggers re-configure the four arrangements 
                             of MenuItem to show different levels of menu via Role -->
                    <ControlTemplate.Triggers>
                        <!-- Role = TopLevelHeader : this is the root menu item in a menu; the Popup expands down -->
                        <Trigger Property="Role" Value="TopLevelHeader">
                            <Setter Property="Padding" Value="6,1,6,1"/>
                            <Setter Property="Placement" Value="bottom" TargetName="SubMenuPopup"/>
                            <Setter Property="MinWidth" Value="0" TargetName="Col0"/>
                            <Setter Property="Width" Value="Auto" TargetName="Col3"/>
                            <Setter Property="MinHeight" Value="25"/>
                            <!--<Setter Property="CornerRadius" Value="5,5,0,0" TargetName="Border"/>-->
                            <Setter Property="Visibility" Value="Collapsed" TargetName="Icon"/>
                            <!--<Setter Property="Visibility" Value="Collapsed" TargetName="GlyphPanel"/>
                                <Setter Property="Visibility" Value="Collapsed" TargetName="IGTHost"/>-->
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel"/>
                        </Trigger>

                        <!-- Role = TopLevelItem :  this is a child menu item from the top level without any child items-->
                        <Trigger Property="Role" Value="TopLevelItem">
                            <Setter Property="Padding" Value="6,1,6,1"/>
                            <Setter Property="MinHeight" Value="30"/>
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel"/>
                        </Trigger>

                        <!-- Role = SubMenuHeader : this is a child menu item which does not have children -->
                        <Trigger Property="Role" Value="SubmenuHeader">
                            <Setter Property="DockPanel.Dock" Value="Top"/>
                            <Setter Property="Padding" Value="0,2,0,2"/>
                            <Setter Property="Background" Value="#1B1B1C"/>
                            <Setter Property="MinHeight" Value="30"/>
                        </Trigger>

                        <!-- Role = SubMenuItem : this is a child menu item which has children-->
                        <Trigger Property="Role" Value="SubmenuItem">
                            <Setter Property="DockPanel.Dock" Value="Top"/>
                            <Setter Property="Padding" Value="0,2,0,2"/>
                            <Setter Property="MinHeight" Value="30"/>
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel"/>
                            <Setter Property="Background" Value="#1B1B1C"/>
                        </Trigger>

                        <Trigger Property="IsSuspendingPopupAnimation" Value="true">
                            <Setter Property="PopupAnimation" Value="None" TargetName="SubMenuPopup"/>
                        </Trigger>

                        <!-- If no Icon is present the we collapse the Icon Content -->
                        <Trigger Property="Icon" Value="{x:Null}">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="Icon"/>
                        </Trigger>

                        <!-- The GlyphPanel contains the CheckMark -->
                        <Trigger Property="IsChecked" Value="true">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="GlyphPanel"/>
                            <Setter Property="Visibility" Value="Collapsed" TargetName="Icon"/>
                        </Trigger>

                        <!-- Using the system colors for the Menu Highlight and IsEnabled-->
                        <Trigger Property="IsHighlighted" Value="true">
                            <Setter Property="Background" Value="#3E3E40" TargetName="Border"/>
                            <Setter Property="Foreground" Value="white"/>
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="Gray"/>

                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#3E3E40"/>
                            <Setter Property="Background" Value="#3E3E40" TargetName="Border"/>
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSubmenuOpen" Value="True"></Condition>
                                <Condition Property="IsHighlighted" Value="True"></Condition>
                            </MultiTrigger.Conditions>
                            <MultiTrigger.Setters>
                                <Setter Property="Background" Value="#1B1B1C"/>
                            </MultiTrigger.Setters>
                        </MultiTrigger>


                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  ######################## 滑块部分 ######################### -->

    <SolidColorBrush x:Key="SliderThumb.Static.Foreground" Color="#FFE5E5E5"/>
    <SolidColorBrush x:Key="SliderThumb.MouseOver.Background" Color="#FFDCECFC"/>
    <SolidColorBrush x:Key="SliderThumb.MouseOver.Border" Color="#FF7Eb4EA"/>
    <SolidColorBrush x:Key="SliderThumb.Pressed.Background" Color="#FFDAECFC"/>
    <SolidColorBrush x:Key="SliderThumb.Pressed.Border" Color="#FF569DE5"/>
    <SolidColorBrush x:Key="SliderThumb.Disabled.Background" Color="#FFF0F0F0"/>
    <SolidColorBrush x:Key="SliderThumb.Disabled.Border" Color="#FFD9D9D9"/>
    <SolidColorBrush x:Key="SliderThumb.Static.Background" Color="#FFF0F0F0"/>
    <SolidColorBrush x:Key="SliderThumb.Static.Border" Color="#FFACACAC"/>

    <SolidColorBrush x:Key="SliderThumb.Track.Border" Color="#FFD6D6D6"/>
    <SolidColorBrush x:Key="SliderThumb.Track.Background" Color="#FFE7EAEA"/>

    <Style x:Key="RepeatButtonTransparent" TargetType="{x:Type RepeatButton}">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Focusable" Value="false"/>
        <Setter Property="IsTabStop" Value="false"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Rectangle Fill="{TemplateBinding Background}" Height="{TemplateBinding Height}" Width="{TemplateBinding Width}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="SliderThumbHorizontalTop" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Path x:Name="grip" Data="M 0,6 C0,6 5.5,0 5.5,0 5.5,0 11,6 11,6 11,6 11,18 11,18 11,18 0,18 0,18 0,18 0,6 0,6 z" Fill="{StaticResource SliderThumb.Static.Background}" Stretch="Fill" SnapsToDevicePixels="True" Stroke="{StaticResource SliderThumb.Static.Border}" StrokeThickness="1" UseLayoutRounding="True" VerticalAlignment="Center"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Border}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Border}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Border}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderThumbHorizontalBottom" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Path x:Name="grip" Data="M 0,12 C0,12 5.5,18 5.5,18 5.5,18 11,12 11,12 11,12 11,0 11,0 11,0 0,0 0,0 0,0 0,12 0,12 z" Fill="{StaticResource SliderThumb.Static.Background}" Stretch="Fill" SnapsToDevicePixels="True" Stroke="{StaticResource SliderThumb.Static.Border}" StrokeThickness="1" UseLayoutRounding="True" VerticalAlignment="Center"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Border}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Border}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Border}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderThumbHorizontalDefault" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Path x:Name="grip" Data="M 0,0 C0,0 11,0 11,0 11,0 11,18 11,18 11,18 0,18 0,18 0,18 0,0 0,0 z" Fill="#68686E" Stretch="Fill" SnapsToDevicePixels="True" Stroke="#68686E" StrokeThickness="1" UseLayoutRounding="True" VerticalAlignment="Center"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="#A6A6AB"/>
                <Setter Property="Stroke" TargetName="grip" Value="#68686E"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="#A6A6AB"/>
                <Setter Property="Stroke" TargetName="grip" Value="#68686E"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Border}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderHorizontal" TargetType="{x:Type Slider}">
        <Border x:Name="border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" SnapsToDevicePixels="True">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <TickBar x:Name="TopTick" Fill="{TemplateBinding Foreground}" Height="4" Margin="0,0,0,2" Placement="Top" Grid.Row="0" Visibility="Collapsed"/>
                <TickBar x:Name="BottomTick" Fill="{TemplateBinding Foreground}" Height="4" Margin="0,2,0,0" Placement="Bottom" Grid.Row="2" Visibility="Collapsed"/>
                <Border x:Name="TrackBackground"  BorderThickness="1"  Height="4.0" Margin="5,0" Grid.Row="1" VerticalAlignment="center">
                    <Canvas Margin="-6,-1">
                        <Rectangle x:Name="PART_SelectionRange" Fill="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" Height="4.0" Visibility="Hidden"/>
                    </Canvas>
                </Border>
                <Track x:Name="PART_Track" Grid.Row="1">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.DecreaseLarge}" Style="{StaticResource RepeatButtonTransparent}"/>
                    </Track.DecreaseRepeatButton>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.IncreaseLarge}" Style="{StaticResource RepeatButtonTransparent}"/>
                    </Track.IncreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb x:Name="Thumb" Focusable="False" OverridesDefaultStyle="True" Template="{StaticResource SliderThumbHorizontalDefault}" Width="60" VerticalAlignment="Center"/>
                    </Track.Thumb>
                </Track>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbHorizontalTop}"/>
                <Setter Property="Margin" TargetName="TrackBackground" Value="5,2,5,0"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbHorizontalBottom}"/>
                <Setter Property="Margin" TargetName="TrackBackground" Value="5,0,5,2"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsSelectionRangeEnabled" Value="true">
                <Setter Property="Visibility" TargetName="PART_SelectionRange" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">
                <Setter Property="Foreground" TargetName="Thumb" Value="Blue"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderThumbVerticalLeft" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Path x:Name="grip" Data="M 6,11 C6,11 0,5.5 0,5.5 0,5.5 6,0 6,0 6,0 18,0 18,0 18,0 18,11 18,11 18,11 6,11 6,11 z" Fill="{StaticResource SliderThumb.Static.Background}" Stretch="Fill" Stroke="{StaticResource SliderThumb.Static.Border}"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Border}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Border}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Border}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderThumbVerticalRight" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Path x:Name="grip" Data="M 12,11 C12,11 18,5.5 18,5.5 18,5.5 12,0 12,0 12,0 0,0 0,0 0,0 0,11 0,11 0,11 12,11 12,11 z" Fill="{StaticResource SliderThumb.Static.Background}" Stretch="Fill" Stroke="{StaticResource SliderThumb.Static.Border}"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Border}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Border}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Border}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderThumbVerticalDefault" TargetType="{x:Type Thumb}">
        <Grid HorizontalAlignment="Center" UseLayoutRounding="True" VerticalAlignment="Center">
            <Path x:Name="grip" Data="M0.5,0.5 L18.5,0.5 18.5,11.5 0.5,11.5z" Fill="{StaticResource SliderThumb.Static.Background}" Stretch="Fill" Stroke="{StaticResource SliderThumb.Static.Border}"/>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.MouseOver.Border}"/>
            </Trigger>
            <Trigger Property="IsDragging" Value="true">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Pressed.Border}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Fill" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Background}"/>
                <Setter Property="Stroke" TargetName="grip" Value="{StaticResource SliderThumb.Disabled.Border}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="SliderVertical" TargetType="{x:Type Slider}">
        <Border x:Name="border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" SnapsToDevicePixels="True">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition MinWidth="{TemplateBinding MinWidth}" Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TickBar x:Name="TopTick" Grid.Column="0" Fill="{TemplateBinding Foreground}" Margin="0,0,2,0" Placement="Left" Visibility="Collapsed" Width="4"/>
                <TickBar x:Name="BottomTick" Grid.Column="2" Fill="{TemplateBinding Foreground}" Margin="2,0,0,0" Placement="Right" Visibility="Collapsed" Width="4"/>
                <Border x:Name="TrackBackground" BorderBrush="{StaticResource SliderThumb.Track.Border}" BorderThickness="1" Background="{StaticResource SliderThumb.Track.Background}" Grid.Column="1" HorizontalAlignment="center" Margin="0,5" Width="4.0">
                    <Canvas Margin="-1,-6">
                        <Rectangle x:Name="PART_SelectionRange" Fill="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" Visibility="Hidden" Width="4.0"/>
                    </Canvas>
                </Border>
                <Track x:Name="PART_Track" Grid.Column="1">
                    <Track.DecreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.DecreaseLarge}" Style="{StaticResource RepeatButtonTransparent}"/>
                    </Track.DecreaseRepeatButton>
                    <Track.IncreaseRepeatButton>
                        <RepeatButton Command="{x:Static Slider.IncreaseLarge}" Style="{StaticResource RepeatButtonTransparent}"/>
                    </Track.IncreaseRepeatButton>
                    <Track.Thumb>
                        <Thumb x:Name="Thumb" Focusable="False" Height="11" OverridesDefaultStyle="True" Template="{StaticResource SliderThumbVerticalDefault}" VerticalAlignment="Top" Width="18"/>
                    </Track.Thumb>
                </Track>
            </Grid>
        </Border>
        <ControlTemplate.Triggers>
            <Trigger Property="TickPlacement" Value="TopLeft">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbVerticalLeft}"/>
                <Setter Property="Margin" TargetName="TrackBackground" Value="2,5,0,5"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="BottomRight">
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
                <Setter Property="Template" TargetName="Thumb" Value="{StaticResource SliderThumbVerticalRight}"/>
                <Setter Property="Margin" TargetName="TrackBackground" Value="0,5,2,5"/>
            </Trigger>
            <Trigger Property="TickPlacement" Value="Both">
                <Setter Property="Visibility" TargetName="TopTick" Value="Visible"/>
                <Setter Property="Visibility" TargetName="BottomTick" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsSelectionRangeEnabled" Value="true">
                <Setter Property="Visibility" TargetName="PART_SelectionRange" Value="Visible"/>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">
                <Setter Property="Foreground" TargetName="Thumb" Value="Blue"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style x:Key="GraperSlider" TargetType="{x:Type Slider}">
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="false"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource SliderThumb.Static.Foreground}"/>
        <Setter Property="Template" Value="{StaticResource SliderHorizontal}"/>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Template" Value="{StaticResource SliderVertical}"/>
            </Trigger>
        </Style.Triggers>
    </Style>


</ResourceDictionary>