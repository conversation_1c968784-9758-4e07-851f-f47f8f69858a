package com.gzcec.xrandroidclient.communication.constants;

/**
 * MQTT消息类型枚举
 * 定义XR系统中所有支持的消息类型，与PC端保持一致
 */
public enum MessageType {

    // ==================== 设备管理消息 ====================
    DEVICE_LIST_REQUEST("device_list_request", "设备列表请求"),
    DEVICE_LIST_RESPONSE("device_list_response", "设备列表响应"),
    DEVICE_STATUS_UPDATE("device_status_update", "设备状态更新"),
    DEVICE_SELECTION_CHANGED("device_selection_changed", "设备选择变更"),
    DEVICE_BATTERY_STATUS("device_battery_status", "设备电池状态"),

    // ==================== 游戏控制消息 ====================
    GAME_LIST_REQUEST("game_list_request", "游戏列表请求"),
    GAME_LIST_RESPONSE("game_list_response", "游戏列表响应"),
    GAME_START_REQUEST("game_start_request", "游戏启动请求"),
    GAME_START_RESPONSE("game_start_response", "游戏启动响应"),
    GAME_STOP_REQUEST("game_stop_request", "游戏停止请求"),
    GAME_STOP_RESPONSE("game_stop_response", "游戏停止响应"),
    GAME_PROGRESS_UPDATE("game_progress_update", "游戏进度更新"),
    GAME_SERVER_STATUS("game_server_status", "游戏服务器状态"),

    // ==================== 系统状态消息 ====================
    HEARTBEAT("heartbeat", "心跳消息"),
    SYSTEM_ERROR("system_error", "系统错误"),
    CONNECTION_STATUS("connection_status", "连接状态"),
    SYSTEM_LOG("system_log", "系统日志"),

    // ==================== 通用消息 ====================
    ACKNOWLEDGMENT("acknowledgment", "确认消息"),
    UNKNOWN("unknown", "未知消息类型");

    private final String identifier;
    private final String description;

    MessageType(String identifier, String description) {
        this.identifier = identifier;
        this.description = description;
    }

    public String getIdentifier() { return identifier; }
    public String getDescription() { return description; }

    public static MessageType fromIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return UNKNOWN;
        }
        for (MessageType type : values()) {
            if (type.identifier.equalsIgnoreCase(identifier.trim())) {
                return type;
            }
        }
        return UNKNOWN;
    }

    public boolean isDeviceMessage() {
        return this == DEVICE_LIST_REQUEST || this == DEVICE_LIST_RESPONSE || 
               this == DEVICE_STATUS_UPDATE || this == DEVICE_SELECTION_CHANGED || 
               this == DEVICE_BATTERY_STATUS;
    }

    public boolean isGameMessage() {
        return this == GAME_LIST_REQUEST || this == GAME_LIST_RESPONSE ||
               this == GAME_START_REQUEST || this == GAME_START_RESPONSE ||
               this == GAME_STOP_REQUEST || this == GAME_STOP_RESPONSE ||
               this == GAME_PROGRESS_UPDATE || this == GAME_SERVER_STATUS;
    }

    public boolean isSystemMessage() {
        return this == HEARTBEAT || this == SYSTEM_ERROR || 
               this == CONNECTION_STATUS || this == SYSTEM_LOG;
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", name(), description);
    }
}
