﻿using System;
using System.Collections.ObjectModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using XRSvc.DataPack;
using XRSvc.DataSource;

namespace XRSvc.ViewModels
{
    public class GameViewModel : BaseViewModel
    {
        #region Fields

        private ObservableCollection<GameSource> _games;
        private ICommand _saveGameNameCommand;

        #endregion

        #region Properties

        public ObservableCollection<GameSource> Games
        {
            get => _games;
            set
            {
                _games = value;
                OnPropertyChanged();
            }
        }

        public GameSource this[int index]
        {
            get => _games[index];
        }

        public ICommand SaveGameNameCommand
        {
            get => _saveGameNameCommand;
            set
            {
                _saveGameNameCommand = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Events

        public event Action<DateTime> GamesUpdated;

        #endregion

        #region Constructor

        public GameViewModel()
        {
            _games = new ObservableCollection<GameSource>();
        }

        #endregion

        #region Methods

        public void ResetGames()
        {
            RunOnUIThread(() =>
            {
                foreach (var game in _games)
                {
                    game.Name = string.Empty;
                    game.PackageName = string.Empty;
                    game.VideoFilePath = string.Empty;
                    game.GameType = GameType.None;
                    game.VideoFormat = VideoFormat.TYPE_UNKNOWN;
                    game.Duration = 0;
                    game.ShowOrder = 0;
                    game.IsShow = false;
                    game.IsPlayAction = false;
                    game.ActionFilePath = string.Empty;
                }

                OnPropertyChanged(nameof(Games));
            });
        }

        public void UpdateGames(GameInfo[] gameInfos)
        {
            RunOnUIThread(() =>
            {
                _games.Clear();

                foreach (var info in gameInfos)
                {
                    _games.Add(new GameSource
                    {
                        ID = info.ID,
                        Name = info.Name,
                        GameCategoryIndex = info.GameCategoryIndex,
                        PackageName = info.PackageName,
                        VideoFilePath = info.VideoFilePath,
                        GameType = info.GameType,
                        VideoFormat = info.VideoFormat,
                        Duration = info.Duration,
                        ShowOrder = info.ShowOrder,
                        IsShow = info.IsShow,
                        IsPlayAction = info.IsPlayAction,
                        ActionFilePath = info.ActionFilePath
                    });
                }

                OnPropertyChanged(nameof(Games));
                GamesUpdated?.Invoke(DateTime.Now);
            });
        }

        public GameInfo[] GetGameInfos()
        {
            var gameInfos = new GameInfo[_games.Count];

            for (int i = 0; i < _games.Count; i++)
            {
                var game = _games[i];
                gameInfos[i] = new GameInfo
                {
                    ID = game.ID,
                    Name = game.Name,
                    GameCategoryIndex = game.GameCategoryIndex,
                    PackageName = game.PackageName,
                    VideoFilePath = game.VideoFilePath,
                    GameType = game.GameType,
                    VideoFormat = game.VideoFormat,
                    Duration = game.Duration,
                    ShowOrder = game.ShowOrder,
                    IsShow = game.IsShow,
                    IsPlayAction = game.IsPlayAction,
                    ActionFilePath = game.ActionFilePath
                };
            }

            return gameInfos;
        }

        public async Task LoadDefaultGamesAsync(CancellationToken cancellationToken)
        {
            IsLoading = true;
            await RunOnUIThreadAsync(async () =>
            {
                _games.Clear();

                var defaultList = new[]
                {
                    new GameInfo {
                        ID = 10001, Name = "唐诡", GameCategoryIndex = 1, GameType = GameType.Video, VideoFormat = VideoFormat.TYPE_3D360_TB,
                        VideoFilePath = "/唐诡/唐诡.mp4", Duration = 125, ShowOrder = 50, IsShow = true, IsPlayAction = true, ActionFilePath = "/唐诡/10001.jdz"
                    },
                    new GameInfo {
                        ID = 10002, Name = "秦陵", GameCategoryIndex = 1, GameType = GameType.Video, VideoFormat = VideoFormat.TYPE_3D360_TB,
                        VideoFilePath = "/秦陵/秦陵.mp4", Duration = 125, ShowOrder = 50, IsShow = true, IsPlayAction = true, ActionFilePath = "/秦陵/01.jdz"
                    },
                    new GameInfo {
                        ID = 10003, Name = "长征", GameCategoryIndex = 1, GameType = GameType.Video, VideoFormat = VideoFormat.TYPE_3D360_TB,
                        VideoFilePath = "/长征/长征.mp4", Duration = 125, ShowOrder = 50, IsShow = true, IsPlayAction = true, ActionFilePath = "/长征/01.jdz"
                    }
                };

                foreach (var info in defaultList)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        IsLoading = false;
                        return;
                    }

                    await Task.Delay(50, cancellationToken); // Simulate async operation

                    _games.Add(new GameSource
                    {
                        ID = info.ID,
                        Name = info.Name,
                        GameCategoryIndex = info.GameCategoryIndex,
                        PackageName = info.PackageName,
                        VideoFilePath = info.VideoFilePath,
                        GameType = info.GameType,
                        VideoFormat = info.VideoFormat,
                        Duration = info.Duration,
                        ShowOrder = info.ShowOrder,
                        IsShow = info.IsShow,
                        IsPlayAction = info.IsPlayAction,
                        ActionFilePath = info.ActionFilePath
                    });
                }

                OnPropertyChanged(nameof(Games));
                GamesUpdated?.Invoke(DateTime.Now);
                IsLoading = false;
            });
        }

        #endregion
    }
}
