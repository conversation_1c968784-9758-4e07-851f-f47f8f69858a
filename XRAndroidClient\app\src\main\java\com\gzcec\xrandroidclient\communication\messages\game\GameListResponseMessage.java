package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.communication.data.GameInfo;

import java.util.List;

/**
 * 游戏列表响应消息
 */
public class GameListResponseMessage extends BaseMessage {
    private String requestId;
    private List<GameInfo> games;
    private int totalCount;
    private boolean success;
    private String errorMessage;
    
    public GameListResponseMessage() {
        setType(MessageType.GAME_LIST_RESPONSE);
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public List<GameInfo> getGames() {
        return games;
    }
    
    public void setGames(List<GameInfo> games) {
        this.games = games;
        this.totalCount = games != null ? games.size() : 0;
    }
    
    public int getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
