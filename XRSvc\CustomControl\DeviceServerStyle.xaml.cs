﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using XRSvc.DataSource;

namespace XRSvc.CustomControl
{
    /// <summary>
    /// DeviceServerStyle.xaml 的交互逻辑
    /// </summary>
    public partial class DeviceServerStyle : UserControl
    {
        #region Events

        /// <summary>
        /// 设备服务器选择改变事件
        /// </summary>
        public static readonly RoutedEvent SelectionChangedEvent = EventManager.RegisterRoutedEvent(
            "SelectionChanged", RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(DeviceServerStyle));

        /// <summary>
        /// 设备服务器选择改变事件
        /// </summary>
        public event RoutedEventHandler SelectionChanged
        {
            add { AddHandler(SelectionChangedEvent, value); }
            remove { RemoveHandler(SelectionChangedEvent, value); }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceServerStyle()
        {
            InitializeComponent();
        }

        #endregion

        #region Dependency Properties

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string ServerName
        {
            get { return (string)GetValue(ServerNameProperty); }
            set { SetValue(ServerNameProperty, value); }
        }
        public static readonly DependencyProperty ServerNameProperty =
            DependencyProperty.Register("ServerName", typeof(string), typeof(DeviceServerStyle), new PropertyMetadata(""));

        /// <summary>
        /// 服务器标签
        /// </summary>
        public string ServerTag
        {
            get { return (string)GetValue(ServerTagProperty); }
            set { SetValue(ServerTagProperty, value); }
        }
        public static readonly DependencyProperty ServerTagProperty =
            DependencyProperty.Register("ServerTag", typeof(string), typeof(DeviceServerStyle), new PropertyMetadata(""));

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StateText
        {
            get { return (string)GetValue(StateTextProperty); }
            set { SetValue(StateTextProperty, value); }
        }
        public static readonly DependencyProperty StateTextProperty =
            DependencyProperty.Register("StateText", typeof(string), typeof(DeviceServerStyle), new PropertyMetadata("离线"));

        /// <summary>
        /// 状态颜色
        /// </summary>
        public Brush StateColor
        {
            get { return (Brush)GetValue(StateColorProperty); }
            set { SetValue(StateColorProperty, value); }
        }
        public static readonly DependencyProperty StateColorProperty =
            DependencyProperty.Register("StateColor", typeof(Brush), typeof(DeviceServerStyle), new PropertyMetadata(Brushes.Red));

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get { return (bool)GetValue(IsSelectedProperty); }
            set { SetValue(IsSelectedProperty, value); }
        }
        public static readonly DependencyProperty IsSelectedProperty =
            DependencyProperty.Register("IsSelected", typeof(bool), typeof(DeviceServerStyle), new PropertyMetadata(false));

        #endregion

        #region Event Handlers

        /// <summary>
        /// RadioButton点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void RadioButton_GameName_Click(object sender, RoutedEventArgs e)
        {
            // 获取当前绑定的数据源
            if (DataContext is DeviceServerSource serverSource)
            {
                // 设置选中状态
                IsSelected = true;
                serverSource.IsSelected = true;

                // 触发选择改变事件
                RaiseEvent(new RoutedEventArgs(SelectionChangedEvent, this));
            }
        }

        #endregion
    }
}
