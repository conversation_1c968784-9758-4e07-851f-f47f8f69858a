package com.gzcec.xrandroidclient.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.UnknownHostException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MQTT连接诊断工具
 * 专门用于诊断MQTT连接问题
 */
public class MqttConnectionDiagnostics {
    private static final String TAG = "MqttConnectionDiagnostics";
    private static final int PING_TIMEOUT = 5000; // 5秒超时
    private static final int PORT_TEST_TIMEOUT = 10000; // 10秒超时
    
    private final Context context;
    private final ExecutorService executor;
    
    public MqttConnectionDiagnostics(Context context) {
        this.context = context;
        this.executor = Executors.newCachedThreadPool();
    }
    
    /**
     * 诊断结果回调接口
     */
    public interface DiagnosticsCallback {
        void onResult(DiagnosticsResult result);
    }
    
    /**
     * 执行MQTT连接诊断
     */
    public void diagnoseMqttConnection(String mqttHost, int mqttPort, DiagnosticsCallback callback) {
        executor.execute(() -> {
            DiagnosticsResult result = new DiagnosticsResult();
            
            Log.d(TAG, "开始MQTT连接诊断: " + mqttHost + ":" + mqttPort);
            
            // 1. 检查网络连接状态
            result.isNetworkConnected = checkNetworkConnection();
            result.networkType = getNetworkType();
            
            // 2. 获取本地IP地址
            result.localIpAddress = getLocalIpAddress();
            
            // 3. 检查DNS解析
            result.isDnsResolvable = checkDnsResolution(mqttHost);
            result.resolvedIpAddress = getResolvedIpAddress(mqttHost);
            
            // 4. 检查主机可达性
            result.isHostReachable = checkHostReachability(mqttHost);
            
            // 5. 检查端口连通性
            result.isPortOpen = checkPortConnectivity(mqttHost, mqttPort);
            
            // 6. 网络段分析
            result.networkSegmentAnalysis = analyzeNetworkSegment(result.localIpAddress, result.resolvedIpAddress);
            
            // 7. 生成诊断建议
            result.suggestions = generateSuggestions(result);
            
            Log.d(TAG, "MQTT连接诊断完成:\n" + result.toString());
            
            // 回调结果
            callback.onResult(result);
        });
    }
    
    /**
     * 检查网络连接状态
     */
    private boolean checkNetworkConnection() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            boolean isConnected = activeNetwork != null && activeNetwork.isConnectedOrConnecting();
            Log.d(TAG, "网络连接状态: " + isConnected);
            return isConnected;
        } catch (Exception e) {
            Log.e(TAG, "检查网络连接状态失败", e);
            return false;
        }
    }
    
    /**
     * 获取网络类型
     */
    private String getNetworkType() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork != null) {
                switch (activeNetwork.getType()) {
                    case ConnectivityManager.TYPE_WIFI:
                        return "WiFi";
                    case ConnectivityManager.TYPE_MOBILE:
                        return "移动数据";
                    case ConnectivityManager.TYPE_ETHERNET:
                        return "以太网";
                    default:
                        return "其他(" + activeNetwork.getTypeName() + ")";
                }
            }
            return "未连接";
        } catch (Exception e) {
            Log.e(TAG, "获取网络类型失败", e);
            return "未知";
        }
    }
    
    /**
     * 获取本地IP地址
     */
    private String getLocalIpAddress() {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            int ipAddress = wifiInfo.getIpAddress();
            
            String ip = String.format("%d.%d.%d.%d",
                (ipAddress & 0xff),
                (ipAddress >> 8 & 0xff),
                (ipAddress >> 16 & 0xff),
                (ipAddress >> 24 & 0xff));
            
            Log.d(TAG, "本地IP地址: " + ip);
            return ip;
        } catch (Exception e) {
            Log.e(TAG, "获取本地IP地址失败", e);
            return "未知";
        }
    }
    
    /**
     * 检查DNS解析
     */
    private boolean checkDnsResolution(String hostname) {
        try {
            InetAddress address = InetAddress.getByName(hostname);
            Log.d(TAG, "DNS解析成功: " + hostname + " -> " + address.getHostAddress());
            return true;
        } catch (UnknownHostException e) {
            Log.e(TAG, "DNS解析失败: " + hostname, e);
            return false;
        }
    }
    
    /**
     * 获取解析后的IP地址
     */
    private String getResolvedIpAddress(String hostname) {
        try {
            InetAddress address = InetAddress.getByName(hostname);
            return address.getHostAddress();
        } catch (UnknownHostException e) {
            Log.e(TAG, "获取解析IP地址失败: " + hostname, e);
            return "解析失败";
        }
    }
    
    /**
     * 检查主机可达性
     */
    private boolean checkHostReachability(String hostname) {
        try {
            InetAddress address = InetAddress.getByName(hostname);
            boolean reachable = address.isReachable(PING_TIMEOUT);
            Log.d(TAG, "主机可达性测试: " + hostname + " -> " + reachable);
            return reachable;
        } catch (Exception e) {
            Log.e(TAG, "主机可达性测试失败: " + hostname, e);
            return false;
        }
    }
    
    /**
     * 检查端口连通性
     */
    private boolean checkPortConnectivity(String hostname, int port) {
        Socket socket = null;
        try {
            socket = new Socket();
            socket.connect(new InetSocketAddress(hostname, port), PORT_TEST_TIMEOUT);
            Log.d(TAG, "端口连通性测试成功: " + hostname + ":" + port);
            return true;
        } catch (IOException e) {
            Log.e(TAG, "端口连通性测试失败: " + hostname + ":" + port, e);
            return false;
        } finally {
            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException e) {
                    Log.e(TAG, "关闭测试socket失败", e);
                }
            }
        }
    }
    
    /**
     * 分析网络段
     */
    private String analyzeNetworkSegment(String localIp, String remoteIp) {
        try {
            if (localIp == null || remoteIp == null || localIp.equals("未知") || remoteIp.equals("解析失败")) {
                return "无法分析网络段";
            }
            
            String[] localParts = localIp.split("\\.");
            String[] remoteParts = remoteIp.split("\\.");
            
            if (localParts.length != 4 || remoteParts.length != 4) {
                return "IP地址格式错误";
            }
            
            // 检查是否在同一网络段
            String localNetwork = localParts[0] + "." + localParts[1] + "." + localParts[2];
            String remoteNetwork = remoteParts[0] + "." + remoteParts[1] + "." + remoteParts[2];
            
            if (localNetwork.equals(remoteNetwork)) {
                return "同一网络段 (" + localNetwork + ".x)";
            } else {
                return "不同网络段 (本地:" + localNetwork + ".x, 远程:" + remoteNetwork + ".x)";
            }
        } catch (Exception e) {
            Log.e(TAG, "网络段分析失败", e);
            return "分析失败";
        }
    }
    
    /**
     * 生成诊断建议
     */
    private String generateSuggestions(DiagnosticsResult result) {
        StringBuilder suggestions = new StringBuilder();
        
        if (!result.isNetworkConnected) {
            suggestions.append("1. 检查设备网络连接\n");
            suggestions.append("   - 确保WiFi已连接\n");
            suggestions.append("   - 检查网络设置\n\n");
        }
        
        if (!result.isDnsResolvable) {
            suggestions.append("2. DNS解析问题\n");
            suggestions.append("   - 尝试使用IP地址代替域名\n");
            suggestions.append("   - 检查DNS设置\n\n");
        }
        
        if (!result.isHostReachable) {
            suggestions.append("3. 主机不可达\n");
            suggestions.append("   - 检查PC端是否在线\n");
            suggestions.append("   - 检查防火墙设置\n");
            suggestions.append("   - 确保PC和Android设备在同一网络\n\n");
        }
        
        if (!result.isPortOpen) {
            suggestions.append("4. MQTT端口不可访问\n");
            suggestions.append("   - 检查PC端MQTT服务是否启动\n");
            suggestions.append("   - 确认端口1883是否开放\n");
            suggestions.append("   - 检查防火墙是否阻止端口访问\n");
            suggestions.append("   - 尝试关闭Windows防火墙测试\n\n");
        }
        
        if (result.networkSegmentAnalysis != null && result.networkSegmentAnalysis.contains("不同网络段")) {
            suggestions.append("5. 网络段问题\n");
            suggestions.append("   - Android设备和PC不在同一网络段\n");
            suggestions.append("   - 检查路由器配置\n");
            suggestions.append("   - 确保允许跨网段通信\n\n");
        }
        
        // 添加通用建议
        suggestions.append("通用解决方案:\n");
        suggestions.append("• 重启路由器和设备\n");
        suggestions.append("• 检查网络配置\n");
        suggestions.append("• 使用网络扫描工具查找PC端IP\n");
        suggestions.append("• 在PC端运行 'netstat -an | findstr 1883' 检查MQTT服务\n");
        
        return suggestions.toString();
    }
    
    /**
     * 关闭诊断工具
     */
    public void shutdown() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
    
    /**
     * MQTT连接诊断结果
     */
    public static class DiagnosticsResult {
        public boolean isNetworkConnected;
        public String networkType;
        public String localIpAddress;
        public boolean isDnsResolvable;
        public String resolvedIpAddress;
        public boolean isHostReachable;
        public boolean isPortOpen;
        public String networkSegmentAnalysis;
        public String suggestions;
        
        /**
         * 判断是否可以尝试连接
         */
        public boolean canAttemptConnection() {
            return isNetworkConnected && isDnsResolvable && isPortOpen;
        }
        
        /**
         * 获取主要问题
         */
        public String getPrimaryIssue() {
            if (!isNetworkConnected) {
                return "网络未连接";
            }
            if (!isDnsResolvable) {
                return "DNS解析失败";
            }
            if (!isHostReachable) {
                return "主机不可达";
            }
            if (!isPortOpen) {
                return "MQTT端口不可访问";
            }
            return "网络连接正常";
        }
        
        @Override
        public String toString() {
            return "MQTT连接诊断结果:\n" +
                "网络连接: " + (isNetworkConnected ? "✓ 已连接" : "✗ 未连接") + "\n" +
                "网络类型: " + networkType + "\n" +
                "本地IP: " + localIpAddress + "\n" +
                "DNS解析: " + (isDnsResolvable ? "✓ 成功" : "✗ 失败") + "\n" +
                "解析IP: " + resolvedIpAddress + "\n" +
                "主机可达: " + (isHostReachable ? "✓ 是" : "✗ 否") + "\n" +
                "端口开放: " + (isPortOpen ? "✓ 是" : "✗ 否") + "\n" +
                "网络段: " + networkSegmentAnalysis + "\n" +
                "主要问题: " + getPrimaryIssue();
        }
    }
}
