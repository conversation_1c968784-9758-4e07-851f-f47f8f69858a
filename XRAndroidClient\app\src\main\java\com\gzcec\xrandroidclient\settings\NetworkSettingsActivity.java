package com.gzcec.xrandroidclient.settings;

import android.app.AlertDialog;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gzcec.xrandroidclient.R;
import com.gzcec.xrandroidclient.config.NetworkConfig;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 网络设置界面
 * 允许用户配置MQTT服务器连接参数
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2.0
 */
public class NetworkSettingsActivity extends AppCompatActivity {
    
    private static final String TAG = "NetworkSettings";
    
    // ==================== UI组件 ====================
    
    private EditText etMqttHost;
    private EditText etMqttPort;
    private Switch swAutoDiscovery;
    private TextView tvCurrentConfig;
    private TextView tvNetworkInfo;
    private Button btnSave;
    private Button btnTest;
    private Button btnScan;
    private Button btnReset;
    private ProgressBar progressBar;
    private RecyclerView rvDiscoveredServers;
    
    // ==================== 核心组件 ====================
    
    private NetworkConfig networkConfig;
    private ExecutorService executorService;
    private Handler mainHandler;
    
    // ==================== 生命周期 ====================
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_network_settings);
        
        initComponents();
        initViews();
        loadCurrentConfig();
        updateNetworkInfo();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null) {
            executorService.shutdown();
        }
    }
    
    // ==================== 初始化方法 ====================
    
    /**
     * 初始化组件
     */
    private void initComponents() {
        networkConfig = new NetworkConfig(this);
        executorService = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        // 查找视图组件
        etMqttHost = findViewById(R.id.et_mqtt_host);
        etMqttPort = findViewById(R.id.et_mqtt_port);
        swAutoDiscovery = findViewById(R.id.sw_auto_discovery);
        tvCurrentConfig = findViewById(R.id.tv_current_config);
        tvNetworkInfo = findViewById(R.id.tv_network_info);
        btnSave = findViewById(R.id.btn_save);
        btnTest = findViewById(R.id.btn_test);
        btnScan = findViewById(R.id.btn_scan);
        btnReset = findViewById(R.id.btn_reset);
        progressBar = findViewById(R.id.progress_bar);
        rvDiscoveredServers = findViewById(R.id.rv_discovered_servers);
        
        // 设置点击监听器
        btnSave.setOnClickListener(this::onSaveClicked);
        btnTest.setOnClickListener(this::onTestClicked);
        btnScan.setOnClickListener(this::onScanClicked);
        btnReset.setOnClickListener(this::onResetClicked);
        
        // 设置自动发现开关监听器
        swAutoDiscovery.setOnCheckedChangeListener((buttonView, isChecked) -> {
            networkConfig.setAutoDiscoveryEnabled(isChecked);
            updateUIState();
        });
        
        // 设置RecyclerView
        rvDiscoveredServers.setLayoutManager(new LinearLayoutManager(this));
        
        // 初始UI状态
        updateUIState();
    }
    
    /**
     * 加载当前配置
     */
    private void loadCurrentConfig() {
        etMqttHost.setText(networkConfig.getMqttHost());
        etMqttPort.setText(String.valueOf(networkConfig.getMqttPort()));
        swAutoDiscovery.setChecked(networkConfig.isAutoDiscoveryEnabled());
        
        updateCurrentConfigDisplay();
    }
    
    /**
     * 更新网络信息显示
     */
    private void updateNetworkInfo() {
        String networkInfo = "设备IP: " + networkConfig.getLocalIpAddress() + "\n" +
                           networkConfig.getWifiInfo() + "\n" +
                           "网络状态: " + (networkConfig.isNetworkConnected() ? "已连接" : "未连接");
        tvNetworkInfo.setText(networkInfo);
    }
    
    /**
     * 更新当前配置显示
     */
    private void updateCurrentConfigDisplay() {
        String config = "当前配置: " + networkConfig.getMqttUrl() + "\n" +
                       "自动发现: " + (networkConfig.isAutoDiscoveryEnabled() ? "启用" : "禁用");
        tvCurrentConfig.setText(config);
    }
    
    /**
     * 更新UI状态
     */
    private void updateUIState() {
        boolean autoDiscovery = swAutoDiscovery.isChecked();
        etMqttHost.setEnabled(!autoDiscovery);
        etMqttPort.setEnabled(!autoDiscovery);
    }
    
    // ==================== 事件处理 ====================
    
    /**
     * 保存按钮点击事件
     */
    private void onSaveClicked(View view) {
        String host = etMqttHost.getText().toString().trim();
        String portStr = etMqttPort.getText().toString().trim();
        
        // 验证输入
        if (TextUtils.isEmpty(host)) {
            etMqttHost.setError("请输入MQTT服务器地址");
            return;
        }
        
        if (!NetworkConfig.isValidIpAddress(host)) {
            etMqttHost.setError("请输入有效的IP地址");
            return;
        }
        
        int port;
        try {
            port = Integer.parseInt(portStr);
            if (!NetworkConfig.isValidPort(port)) {
                etMqttPort.setError("端口号必须在1-65535之间");
                return;
            }
        } catch (NumberFormatException e) {
            etMqttPort.setError("请输入有效的端口号");
            return;
        }
        
        // 保存配置
        networkConfig.setMqttHost(host);
        networkConfig.setMqttPort(port);

        updateCurrentConfigDisplay();
        Toast.makeText(this, "配置已保存", Toast.LENGTH_SHORT).show();

        Log.i(TAG, "网络配置已保存: " + host + ":" + port);
    }
    
    /**
     * 测试连接按钮点击事件
     */
    private void onTestClicked(View view) {
        String host = etMqttHost.getText().toString().trim();
        String portStr = etMqttPort.getText().toString().trim();
        
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(portStr)) {
            Toast.makeText(this, "请先输入服务器地址和端口", Toast.LENGTH_SHORT).show();
            return;
        }
        
        int port;
        try {
            port = Integer.parseInt(portStr);
        } catch (NumberFormatException e) {
            Toast.makeText(this, "端口号格式错误", Toast.LENGTH_SHORT).show();
            return;
        }
        
        testConnection(host, port);
    }
    
    /**
     * 扫描按钮点击事件
     */
    private void onScanClicked(View view) {
        scanForServers();
    }
    
    /**
     * 重置按钮点击事件
     */
    private void onResetClicked(View view) {
        new AlertDialog.Builder(this)
            .setTitle("重置配置")
            .setMessage("确定要重置所有网络配置吗？")
            .setPositiveButton("确定", (dialog, which) -> {
                networkConfig.resetConfig();
                loadCurrentConfig();
                updateNetworkInfo();
                Toast.makeText(this, "配置已重置", Toast.LENGTH_SHORT).show();
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    // ==================== 网络操作 ====================
    
    /**
     * 测试连接
     */
    private void testConnection(String host, int port) {
        progressBar.setVisibility(View.VISIBLE);
        btnTest.setEnabled(false);
        
        executorService.execute(() -> {
            boolean success = testMqttConnection(host, port);
            
            mainHandler.post(() -> {
                progressBar.setVisibility(View.GONE);
                btnTest.setEnabled(true);
                
                if (success) {
                    Toast.makeText(this, "连接测试成功！", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "连接测试失败", Toast.LENGTH_SHORT).show();
                }
            });
        });
    }
    
    /**
     * 扫描服务器
     */
    private void scanForServers() {
        progressBar.setVisibility(View.VISIBLE);
        btnScan.setEnabled(false);
        
        executorService.execute(() -> {
            List<String> possibleAddresses = networkConfig.getPossibleBrokerAddresses();
            
            mainHandler.post(() -> {
                progressBar.setVisibility(View.GONE);
                btnScan.setEnabled(true);
                
                // 显示扫描结果
                showScanResults(possibleAddresses);
            });
        });
    }
    
    /**
     * 显示扫描结果
     */
    private void showScanResults(List<String> addresses) {
        StringBuilder sb = new StringBuilder("发现的可能地址:\n\n");
        for (String address : addresses) {
            sb.append("• ").append(address).append("\n");
        }
        
        new AlertDialog.Builder(this)
            .setTitle("扫描结果")
            .setMessage(sb.toString())
            .setPositiveButton("确定", null)
            .show();
    }
    
    /**
     * 测试MQTT连接
     */
    private boolean testMqttConnection(String host, int port) {
        try {
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress(host, port), 5000);
            socket.close();
            Log.i(TAG, "连接测试成功: " + host + ":" + port);
            return true;
        } catch (Exception e) {
            Log.w(TAG, "连接测试失败: " + host + ":" + port + " - " + e.getMessage());
            return false;
        }
    }


}
