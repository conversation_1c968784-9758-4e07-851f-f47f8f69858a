package com.gzcec.xrandroidclient.device;

import com.google.gson.annotations.SerializedName;

public enum DeviceType {
    @SerializedName("0")
    SERVER,         // 服务器
    @SerializedName("1")
    GAMESVC,        // 游戏服务端
    @SerializedName("2")
    HMD,            // 头显
    @SerializedName("3")
    MotionPlatform, // 动感平台
    @SerializedName("4")
    Fan,            // 可调速风扇
    @SerializedName("5")
    Hotness,        // 热感
    @SerializedName("6")
    DoorManual,     // 可推拉门(安装有角度传感器)
    @SerializedName("7")
    DoorCmdControl, // 指令控制门
    @SerializedName("8")
    WaterSpray      // 喷水
}