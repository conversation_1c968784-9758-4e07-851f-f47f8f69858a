﻿using Jskj.AppLog;
using Jskj.Win32Api;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;
using System.Threading;
using System.Windows;
using XRSvc.DataPack; // Added namespace for GameInfo

namespace XRSvc
{
    public class HBAPP : System.Windows.Application, IDisposable
    {

        #region Field

        /// <summary>
        /// 参数类
        /// </summary>
        //private Setting Setting;

        private List<GameInfo> GameList;
        //private List<Device> DeviceList;
        //private List<HMDevice> HMDeviceList;


        //private XRServices _XrSvc;

        /// <summary>
        /// 本地信号量
        /// </summary>
        private static bool CreatedNew;

        /// <summary>
        /// 限制某一时间访问某一资源的线程数
        /// </summary>
        private static Semaphore SingleInstanceWatcher;

        /// <summary>
        /// 主窗口
        /// </summary>
        private MainWindow MainWin;

        /// <summary>
        /// 
        /// </summary>
        private System.Windows.Forms.NotifyIcon NotifyIcon;

        #endregion

        #region Property

        /// <summary>
        /// 与爱奇艺通讯的UDP服务
        /// </summary>
        //private XRServices XrSvc
        //{
        //    get
        //    {
        //        if (_XrSvc == null)
        //        {
        //            _XrSvc = new XRServices(this.Setting, this.GameList, this.HMDeviceList);
        //        }
        //        return _XrSvc;
        //    }
        //}

        #endregion

        #region Method

        /// <summary>
        /// 
        /// </summary>
        /// <param name="args"></param>
        public void RunMain(string[] args)
        {
            //检测是否以参数启动
            //if (args.Length == 0)
            //{
            //    Log.Write(Level.DEBUG, $"Cmd Line Args is Empty...");
            //    Environment.Exit(-2);//退出程序
            //    return;
            //}

            //只允许启动一个实例
            SingleInstanceWatcher = new Semaphore(0, 1, Assembly.GetExecutingAssembly().GetName().Name, out CreatedNew);
            if (!CreatedNew)
            {
                Environment.Exit(-2);//退出程序
                return;
            }

            //以管理员权限启动
            //if (!IsRunAdministrator())
            //{
            //    RunAsAdmin(string.Join(" ", args));
            //    Environment.Exit(-2);//退出程序
            //}

            Log.Write(Level.DEBUG, "Application Startup....");

            //注册全局异常
            RegisterUnHandleExcettion();

            //设置进程优先级别
            Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;

            ////读取配置信息
            //if (!ConfigHandler.ReadFromJson(out this.Setting))
            //{
            //    Log.Write(Level.ERROR, "Config file read Fail....");
            //    Environment.Exit(-2); //退出程序
            //    return;
            //}

            //读取游戏列表
            //if (!ConfigHandler.ReadGameListFromJson(out this.GameList))
            //{
            //    Log.Write(Level.ERROR, "GameList file read Fail....");
            //    Environment.Exit(-2); //退出程序
            //    return;
            //}



            ////读取设备列表
            //if (!ConfigHandler.ReadDeviceListFromJson(out this.DeviceList))
            //{
            //    Log.Write(Level.ERROR, "DeviceList file read Fail....");
            //    Environment.Exit(-2); //退出程序
            //    return;
            //}

            //读取头显列表
            //if (!ConfigHandler.ReadHMDDeviceListFromJson(out this.HMDeviceList))
            //{
            //    Log.Write(Level.ERROR, "HMDeviceList file read Fail....");
            //    Environment.Exit(-2); //退出程序
            //    return;
            //}

            ////设置日志输出等级
            //Log.SetLogLevel(Setting.LogLevel, true);

            ////加载资源，包括语言文件
            //if (!WpfHelper.LoadResource(this.Setting.Language))
            //{
            //    Log.Write(Level.ERROR, "LoadLanguage Failed");
            //    Environment.Exit(-2);//退出程序
            //    return;
            //}

            //this.XrSvc.Start();

            //创建任务栏图标
            CreateNotifyIcon();

            this.MainWin = new MainWindow();
            this.MainWin.Closing += MainWin_Closing;


            //启动WPF应用程序
            this.Run(this.MainWin);

            
        }

        #endregion

        #region 启动权限相关

        /// <summary>
        /// 返回程序是否有管理员权限启动
        /// </summary>
        /// <returns></returns>
        public static bool IsRunAdministrator()
        {
            System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
            System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
            return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
        }

        /// <summary>
        /// 
        /// </summary>
        public static void RunAsAdmin(string args)
        {
            //创建启动对象
            System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
            startInfo.UseShellExecute = true;
            startInfo.WorkingDirectory = Environment.CurrentDirectory;
            startInfo.FileName = Assembly.GetExecutingAssembly().Location;
            startInfo.Arguments = args;

            //设置启动动作,确保以管理员身份运行
            startInfo.Verb = "runas";
            try
            {
                System.Diagnostics.Process.Start(startInfo);
            }
            catch
            {
                return;
            }
        }

        #endregion

        #region 全局异常处理

        /// <summary>
        /// 注册全局异常
        /// </summary>
        public void RegisterUnHandleExcettion()
        {
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        /// <summary>
        /// UI线程抛出全局异常事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                e.Handled = true;
                Log.Write(Level.ERROR, e.Exception);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
            }
        }

        /// <summary>
        /// 非UI线程抛出全局异常事件处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception exception)
                {
                    Log.Write(Level.ERROR, exception);
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
            }
        }

        #endregion

        #region Handle NotifyIcon Part

        /// <summary>
        /// 
        /// </summary>
        private void CreateNotifyIcon()
        {
            // 托盘
            this.NotifyIcon = new System.Windows.Forms.NotifyIcon
            {
                Text = "Jskj.GameEngine" + System.Reflection.Assembly.GetExecutingAssembly().GetName().Version,
                Icon = System.Drawing.Icon.ExtractAssociatedIcon(System.Windows.Forms.Application.ExecutablePath),
                Visible = true
            };


            // 注册【退出程序】的鼠标点击事件
            System.Windows.Forms.ToolStripMenuItem QuitItem = new System.Windows.Forms.ToolStripMenuItem("退出");
            QuitItem.Click += new EventHandler(QuitAppMenuItem_Click);

            //关联托盘控件
            System.Windows.Forms.ContextMenuStrip ContextMenu = new System.Windows.Forms.ContextMenuStrip();
            ContextMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] { QuitItem });

            //关联菜单，并注册正在打开的事件
            this.NotifyIcon.ContextMenuStrip = ContextMenu;
            this.NotifyIcon.ContextMenuStrip.Opening += ContextMenuStrip_Opening;

            //鼠标双击托盘图标的事件
            this.NotifyIcon.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler((o, e) =>
            {
                if (e.Button == System.Windows.Forms.MouseButtons.Left)
                {
                    Application.Current.Dispatcher.BeginInvoke(new Action(delegate
                    {
                        Application.Current.MainWindow.ShowInTaskbar = false;
                        Application.Current.MainWindow.Show();
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        //Application.Current.MainWindow.Activate();
                    }));
                }
            });
        }


        /// <summary>
        /// 托盘菜单【退出】的点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void QuitAppMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Application.Current.Dispatcher.BeginInvoke(new Action(delegate
                {
                    System.Windows.Application.Current.Shutdown();
                }));
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return;
            }
        }

        /// <summary>
        /// 处理菜单正在打开的事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ContextMenuStrip_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                POINT p;
                WinHelper.GetCursorPos(out p);
                System.Drawing.Point menuPosition = new System.Drawing.Point();
                menuPosition.X = p.X;
                menuPosition.Y = p.Y - 30 - NotifyIcon.ContextMenuStrip.Size.Height;
                NotifyIcon.ContextMenuStrip.Show(menuPosition);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return;
            }
        }

        #endregion

        #region Handle Window Event

        /// <summary>
        /// 当主窗口关闭时
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MainWin_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                this.NotifyIcon?.Dispose();//没在IDisposable接口处理，是因为时机太晚了，来不及清除托盘的图标
                this._XrSvc?.Shutdown();

            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return;
            }
        }

        #endregion

        #region IDisposable 成员

        ~HBAPP()
        {
            Dispose(false);
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // 清理托管资源
                }

                //Log.Write(Level.DEBUG, "Dispose");
                System.Diagnostics.Debug.WriteLine("HBAPP Dispose");

                //this._XrSvc?.Shutdown();
            }
            catch
            {
                return;
            }
        }

        #endregion

    }
}
