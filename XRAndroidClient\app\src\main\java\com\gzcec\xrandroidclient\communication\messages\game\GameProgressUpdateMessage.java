package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;

import java.util.HashMap;
import java.util.Map;

/**
 * 游戏进度更新消息
 */
public class GameProgressUpdateMessage extends BaseMessage {
    private String gameId;
    private String deviceId;
    private int progress;
    private String status;
    private Map<String, Object> progressData;

    public GameProgressUpdateMessage() {
        setType(MessageType.GAME_PROGRESS_UPDATE);
        this.progressData = new HashMap<>();
    }

    // Getter和Setter方法
    public String getGameId() { return gameId; }
    public void setGameId(String gameId) { this.gameId = gameId; }
    
    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    
    public int getProgress() { return progress; }
    public void setProgress(int progress) { this.progress = progress; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public Map<String, Object> getProgressData() { return progressData; }
    public void setProgressData(Map<String, Object> progressData) { this.progressData = progressData; }
}
