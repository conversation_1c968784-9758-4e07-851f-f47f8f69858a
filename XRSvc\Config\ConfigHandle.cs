﻿using Jskj.AppLog;
using System;
using System.Collections.Generic;
using System.IO;
using XRSvc.Utils;
using XRSvc.DataPack;

namespace Jskj.XRSvc.Config
{
    public class ConfigHandle
    {

        #region Field

        /// <summary>
        /// 应用程序根目录
        /// </summary>
        public static readonly string AppDir = AppDomain.CurrentDomain.BaseDirectory;

        /// <summary>
        /// 配置文件路径
        /// </summary>
        private static readonly string ConfigFilePath = $"{new DirectoryInfo(AppDir).FullName}\\Config\\Config.json";

        #endregion

        #region Method Public

        /// <summary>
        /// 保存到Json文件
        /// </summary>
        /// <param name="setting">配置信息</param>
        /// <returns>保存是否成功</returns>
        public static bool SaveToJson(Setting setting)
        {
            try
            {
                if (setting == null)
                {
                    return false;
                }

                //如果文件目录不存在，创建它
                Directory.CreateDirectory(new FileInfo(ConfigFilePath).DirectoryName);

                //写入到主配置文件
                JsonFileHelper.WriteToJsonFile(ConfigFilePath, setting);

                return true;
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return false;
            }
        }

        /// <summary>
        /// 从Json文件读取配置
        /// </summary>
        /// <param name="setting">输出的配置信息</param>
        /// <returns>读取是否成功</returns>
        public static bool ReadFromJson(out Setting setting)
        {
            setting = default;
            try
            {
                //配置文件不存在就创建一个默认的
                if (!File.Exists(ConfigFilePath))
                {
                    CreateSettingFile();
                }

                //读取主配置文件
                setting = JsonFileHelper.ReadFromJsonFile<Setting>(ConfigFilePath);

                return setting != null;
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return false;
            }
        }

        #endregion

        #region Method Private

        /// <summary>
        /// 创建默认配置文件
        /// </summary>
        private static void CreateSettingFile()
        {
            Setting setting = new Setting();
            setting.InitDefault();
            SaveToJson(setting);
        }

        #endregion

    }
}
