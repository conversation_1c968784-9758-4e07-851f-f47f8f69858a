package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.communication.data.DeviceStartResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 游戏启动响应消息
 */
public class GameStartResponseMessage extends BaseMessage {
    private String requestId;
    private boolean success;
    private String errorMessage;
    private List<DeviceStartResult> results;

    public GameStartResponseMessage() {
        setType(MessageType.GAME_START_RESPONSE);
        this.results = new ArrayList<>();
    }

    // Getter和Setter方法
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public List<DeviceStartResult> getResults() { return results; }
    public void setResults(List<DeviceStartResult> results) { this.results = results; }
}
