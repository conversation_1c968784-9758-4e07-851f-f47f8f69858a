package com.gzcec.xrandroidclient.communication.messages.device;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.device.DeviceInfo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备状态更新消息
 */
public class DeviceStatusUpdateMessage extends BaseMessage {
    private List<DeviceInfo> devices;

    public DeviceStatusUpdateMessage() {
        setType(MessageType.DEVICE_STATUS_UPDATE);
        this.devices = new ArrayList<>();
        // timestamp字段已在BaseMessage中定义，无需重复定义
    }

    // Getter和Setter方法
    public List<DeviceInfo> getDevices() { return devices; }
    public void setDevices(List<DeviceInfo> devices) { this.devices = devices; }

    // timestamp的getter和setter方法已在BaseMessage中定义，无需重复定义
}
