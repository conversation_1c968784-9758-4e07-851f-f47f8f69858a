using System;
using System.IO;
using Newtonsoft.Json;

namespace XRSvc.Utils
{
    public static class JsonFileHelper
    {
        /// <summary>
        /// 将数据写入到JSON文件
        /// </summary>
        public static void WriteToJsonFile<T>(string filePath, T data)
        {
            var json = JsonConvert.SerializeObject(data, Formatting.Indented);
            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// 从JSON文件读取数据
        /// </summary>
        public static T ReadFromJsonFile<T>(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件未找到: {filePath}");
            var json = File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<T>(json);
        }

        /// <summary>
        /// 尝试从JSON文件读取数据，如果文件不存在返回默认值
        /// </summary>
        public static T ReadFromJsonFileOrDefault<T>(string filePath, T defaultValue = default(T))
        {
            try
            {
                if (!File.Exists(filePath))
                    return defaultValue;
                var json = File.ReadAllText(filePath);
                var result = JsonConvert.DeserializeObject<T>(json);
                return result != null ? result : defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
