package com.gzcec.xrandroidclient.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;
import java.io.Closeable;
import java.lang.ref.WeakReference;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 资源管理器
 * 统一管理应用程序资源的生命周期，防止内存泄漏
 */
public class ResourceManager {
    private static final String TAG = "ResourceManager";
    
    // 单例实例
    private static volatile ResourceManager instance;
    private static final Object lock = new Object();
    
    // 资源跟踪
    private final Set<WeakReference<ManagedResource>> managedResources;
    private final ConcurrentHashMap<String, ResourceGroup> resourceGroups;
    private final AtomicInteger totalResources = new AtomicInteger(0);
    private final AtomicInteger activeResources = new AtomicInteger(0);
    
    // 清理任务
    private final ScheduledExecutorService cleanupScheduler;
    private final long CLEANUP_INTERVAL = 30000; // 30秒清理一次
    
    // 内存监控
    private final AtomicLong lastMemoryCheck = new AtomicLong(0);
    private final long MEMORY_CHECK_INTERVAL = 10000; // 10秒检查一次内存
    
    /**
     * 托管资源接口
     */
    public interface ManagedResource extends Closeable {
        String getResourceId();
        String getResourceType();
        long getCreatedTime();
        long getLastAccessTime();
        boolean isActive();
        void updateLastAccessTime();
    }
    
    /**
     * 资源组
     */
    public static class ResourceGroup {
        private final String groupName;
        private final Set<WeakReference<ManagedResource>> resources;
        private final AtomicInteger resourceCount = new AtomicInteger(0);
        private final long createdTime;
        
        public ResourceGroup(String groupName) {
            this.groupName = groupName;
            this.resources = ConcurrentHashMap.newKeySet();
            this.createdTime = System.currentTimeMillis();
        }
        
        public void addResource(ManagedResource resource) {
            resources.add(new WeakReference<>(resource));
            resourceCount.incrementAndGet();
        }
        
        public void removeResource(ManagedResource resource) {
            resources.removeIf(ref -> {
                ManagedResource r = ref.get();
                return r == null || r.equals(resource);
            });
            resourceCount.decrementAndGet();
        }
        
        public int getActiveResourceCount() {
            return (int) resources.stream()
                .map(WeakReference::get)
                .filter(r -> r != null && r.isActive())
                .count();
        }
        
        public void cleanup() {
            resources.removeIf(ref -> ref.get() == null);
        }
        
        public String getGroupName() { return groupName; }
        public long getCreatedTime() { return createdTime; }
        public int getTotalResourceCount() { return resourceCount.get(); }
    }
    
    /**
     * 抽象托管资源基类
     */
    public abstract static class AbstractManagedResource implements ManagedResource {
        protected final String resourceId;
        protected final String resourceType;
        protected final long createdTime;
        protected final AtomicLong lastAccessTime;
        protected volatile boolean active = true;
        
        protected AbstractManagedResource(String resourceType) {
            this.resourceId = generateResourceId();
            this.resourceType = resourceType;
            this.createdTime = System.currentTimeMillis();
            this.lastAccessTime = new AtomicLong(createdTime);
        }
        
        @Override
        public String getResourceId() { return resourceId; }
        
        @Override
        public String getResourceType() { return resourceType; }
        
        @Override
        public long getCreatedTime() { return createdTime; }
        
        @Override
        public long getLastAccessTime() { return lastAccessTime.get(); }
        
        @Override
        public boolean isActive() { return active; }
        
        @Override
        public void updateLastAccessTime() {
            lastAccessTime.set(System.currentTimeMillis());
        }
        
        protected void markInactive() {
            active = false;
        }
        
        private static String generateResourceId() {
            return "RES_" + System.currentTimeMillis() + "_" + 
                   Thread.currentThread().getId() + "_" + 
                   (int)(Math.random() * 1000);
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            AbstractManagedResource that = (AbstractManagedResource) obj;
            return resourceId.equals(that.resourceId);
        }
        
        @Override
        public int hashCode() {
            return resourceId.hashCode();
        }
    }
    
    /**
     * 私有构造函数
     */
    private ResourceManager() {
        this.managedResources = ConcurrentHashMap.newKeySet();
        this.resourceGroups = new ConcurrentHashMap<>();
        
        this.cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ResourceCleanup");
            t.setDaemon(true);
            return t;
        });
        
        // 启动定期清理任务
        startCleanupTask();
        
        Log.i(TAG, "资源管理器已初始化");
    }
    
    /**
     * 获取单例实例
     */
    public static ResourceManager getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new ResourceManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册资源
     */
    public void registerResource(ManagedResource resource) {
        registerResource(resource, "default");
    }
    
    /**
     * 注册资源到指定组
     */
    public void registerResource(ManagedResource resource, String groupName) {
        if (resource == null) {
            Log.w(TAG, "尝试注册空资源");
            return;
        }
        
        // 添加到全局资源集合
        managedResources.add(new WeakReference<>(resource));
        totalResources.incrementAndGet();
        activeResources.incrementAndGet();
        
        // 添加到资源组
        ResourceGroup group = resourceGroups.computeIfAbsent(groupName, ResourceGroup::new);
        group.addResource(resource);
        
        Log.d(TAG, String.format("资源已注册 - ID: %s, 类型: %s, 组: %s", 
            resource.getResourceId(), resource.getResourceType(), groupName));
    }
    
    /**
     * 注销资源
     */
    public void unregisterResource(ManagedResource resource) {
        if (resource == null) return;
        
        // 从全局资源集合移除
        managedResources.removeIf(ref -> {
            ManagedResource r = ref.get();
            return r == null || r.equals(resource);
        });
        
        // 从资源组移除
        for (ResourceGroup group : resourceGroups.values()) {
            group.removeResource(resource);
        }
        
        activeResources.decrementAndGet();
        
        Log.d(TAG, String.format("资源已注销 - ID: %s, 类型: %s", 
            resource.getResourceId(), resource.getResourceType()));
    }
    
    /**
     * 关闭资源
     */
    public void closeResource(ManagedResource resource) {
        if (resource == null) return;
        
        try {
            resource.close();
            unregisterResource(resource);
            Log.d(TAG, "资源已关闭: " + resource.getResourceId());
        } catch (Exception e) {
            Log.e(TAG, "关闭资源失败: " + resource.getResourceId(), e);
        }
    }
    
    /**
     * 关闭资源组
     */
    public void closeResourceGroup(String groupName) {
        ResourceGroup group = resourceGroups.get(groupName);
        if (group == null) {
            Log.w(TAG, "资源组不存在: " + groupName);
            return;
        }
        
        int closedCount = 0;
        for (WeakReference<ManagedResource> ref : group.resources) {
            ManagedResource resource = ref.get();
            if (resource != null && resource.isActive()) {
                try {
                    resource.close();
                    closedCount++;
                } catch (Exception e) {
                    Log.e(TAG, "关闭资源失败: " + resource.getResourceId(), e);
                }
            }
        }
        
        resourceGroups.remove(groupName);
        Log.i(TAG, String.format("资源组已关闭 - 组: %s, 关闭资源数: %d", groupName, closedCount));
    }
    
    /**
     * 启动清理任务
     */
    private void startCleanupTask() {
        cleanupScheduler.scheduleWithFixedDelay(() -> {
            try {
                performCleanup();
                checkMemoryUsage();
            } catch (Exception e) {
                Log.e(TAG, "清理任务执行失败", e);
            }
        }, CLEANUP_INTERVAL, CLEANUP_INTERVAL, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 执行清理
     */
    private void performCleanup() {
        long startTime = System.currentTimeMillis();
        int cleanedCount = 0;
        
        // 清理已被GC的弱引用
        cleanedCount += managedResources.removeIf(ref -> ref.get() == null) ? 1 : 0;
        
        // 清理资源组
        for (ResourceGroup group : resourceGroups.values()) {
            group.cleanup();
        }
        
        // 清理长时间未使用的资源
        long cutoffTime = System.currentTimeMillis() - 300000; // 5分钟
        for (WeakReference<ManagedResource> ref : managedResources) {
            ManagedResource resource = ref.get();
            if (resource != null && resource.getLastAccessTime() < cutoffTime) {
                try {
                    Log.d(TAG, "清理长时间未使用的资源: " + resource.getResourceId());
                    resource.close();
                    cleanedCount++;
                } catch (Exception e) {
                    Log.e(TAG, "清理资源失败: " + resource.getResourceId(), e);
                }
            }
        }
        
        long duration = System.currentTimeMillis() - startTime;
        if (cleanedCount > 0) {
            Log.i(TAG, String.format("清理完成 - 清理资源数: %d, 耗时: %dms", cleanedCount, duration));
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastMemoryCheck.get() < MEMORY_CHECK_INTERVAL) {
            return;
        }
        
        lastMemoryCheck.set(currentTime);
        
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        
        Log.d(TAG, String.format("内存使用情况 - 已用: %.1fMB/%.1fMB (%.1f%%), 活跃资源: %d", 
            usedMemory / 1024.0 / 1024.0, maxMemory / 1024.0 / 1024.0, 
            memoryUsagePercent, activeResources.get()));
        
        // 如果内存使用率过高，触发强制清理
        if (memoryUsagePercent > 80) {
            Log.w(TAG, "内存使用率过高，触发强制清理");
            forceCleanup();
        }
    }
    
    /**
     * 强制清理
     */
    public void forceCleanup() {
        Log.i(TAG, "开始强制清理资源");
        
        // 建议GC
        System.gc();
        
        // 执行清理
        performCleanup();
        
        Log.i(TAG, "强制清理完成");
    }
    
    /**
     * 获取资源统计信息
     */
    public ResourceStatistics getStatistics() {
        int activeCount = 0;
        for (WeakReference<ManagedResource> ref : managedResources) {
            ManagedResource resource = ref.get();
            if (resource != null && resource.isActive()) {
                activeCount++;
            }
        }
        
        return new ResourceStatistics(
            totalResources.get(),
            activeCount,
            resourceGroups.size(),
            getCurrentMemoryUsage()
        );
    }
    
    /**
     * 获取当前内存使用情况
     */
    private MemoryInfo getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        return new MemoryInfo(usedMemory, totalMemory, maxMemory);
    }
    
    /**
     * 关闭资源管理器
     */
    public void shutdown() {
        Log.i(TAG, "开始关闭资源管理器");
        
        // 关闭所有资源
        for (WeakReference<ManagedResource> ref : managedResources) {
            ManagedResource resource = ref.get();
            if (resource != null) {
                try {
                    resource.close();
                } catch (Exception e) {
                    Log.e(TAG, "关闭资源失败: " + resource.getResourceId(), e);
                }
            }
        }
        
        // 关闭清理调度器
        try {
            cleanupScheduler.shutdown();
            if (!cleanupScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupScheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupScheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        managedResources.clear();
        resourceGroups.clear();
        
        Log.i(TAG, "资源管理器已关闭");
    }
    
    /**
     * 内存信息
     */
    public static class MemoryInfo {
        public final long usedMemory;
        public final long totalMemory;
        public final long maxMemory;
        
        MemoryInfo(long used, long total, long max) {
            this.usedMemory = used;
            this.totalMemory = total;
            this.maxMemory = max;
        }
        
        public double getUsagePercent() {
            return (double) usedMemory / maxMemory * 100;
        }
    }
    
    /**
     * 资源统计信息
     */
    public static class ResourceStatistics {
        public final int totalResources;
        public final int activeResources;
        public final int resourceGroups;
        public final MemoryInfo memoryInfo;
        
        ResourceStatistics(int total, int active, int groups, MemoryInfo memory) {
            this.totalResources = total;
            this.activeResources = active;
            this.resourceGroups = groups;
            this.memoryInfo = memory;
        }
        
        @Override
        public String toString() {
            return String.format("ResourceStats{total=%d, active=%d, groups=%d, memory=%.1f%%}", 
                totalResources, activeResources, resourceGroups, memoryInfo.getUsagePercent());
        }
    }
}

/**
 * Android系统监控器
 * 监控应用性能、内存使用、网络状态等
 */
class AndroidSystemMonitor {
    private static final String TAG = "AndroidSystemMonitor";

    // 单例实例
    private static volatile AndroidSystemMonitor instance;
    private static final Object lock = new Object();

    // 网络状态跟踪
    private boolean lastNetworkState = true;
    private String lastNetworkType = "";

    // 监控配置
    private static final int MONITOR_INTERVAL = 5000; // 5秒
    private static final int HISTORY_SIZE = 50;       // 保留50个历史记录

    // 监控组件
    private final Context context;
    private final Handler handler;
    private final Runnable monitorRunnable;
    private final List<PerformanceSnapshot> performanceHistory;
    private final List<SystemMonitorListener> listeners;

    // 监控状态
    private volatile boolean isMonitoring = false;

    /**
     * 性能快照
     */
    public static class PerformanceSnapshot {
        public final long timestamp;
        public final long usedMemory;
        public final long totalMemory;
        public final long availableMemory;
        public final float memoryUsagePercent;
        public final long heapUsed;
        public final long heapMax;
        public final int threadCount;
        public final boolean isNetworkAvailable;
        public final String networkType;
        public final long uptimeMillis;

        public PerformanceSnapshot(long timestamp, long usedMemory, long totalMemory,
                                 long availableMemory, float memoryUsagePercent,
                                 long heapUsed, long heapMax, int threadCount,
                                 boolean isNetworkAvailable, String networkType, long uptimeMillis) {
            this.timestamp = timestamp;
            this.usedMemory = usedMemory;
            this.totalMemory = totalMemory;
            this.availableMemory = availableMemory;
            this.memoryUsagePercent = memoryUsagePercent;
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.threadCount = threadCount;
            this.isNetworkAvailable = isNetworkAvailable;
            this.networkType = networkType;
            this.uptimeMillis = uptimeMillis;
        }

        @Override
        public String toString() {
            return String.format("Performance{memory=%.1f%%, heap=%dMB/%dMB, threads=%d, network=%s}",
                memoryUsagePercent, heapUsed / 1024 / 1024, heapMax / 1024 / 1024,
                threadCount, networkType);
        }
    }

    /**
     * 系统监控监听器
     */
    public interface SystemMonitorListener {
        void onPerformanceUpdate(PerformanceSnapshot snapshot);
        void onMemoryWarning(PerformanceSnapshot snapshot);
        void onNetworkStateChanged(boolean isAvailable, String networkType);
    }

    /**
     * 私有构造函数
     */
    private AndroidSystemMonitor(Context context) {
        this.context = context.getApplicationContext();
        this.handler = new Handler(Looper.getMainLooper());
        this.performanceHistory = new ArrayList<>();
        this.listeners = new CopyOnWriteArrayList<>();

        this.monitorRunnable = new Runnable() {
            @Override
            public void run() {
                if (isMonitoring) {
                    collectPerformanceData();
                    handler.postDelayed(this, MONITOR_INTERVAL);
                }
            }
        };

        Log.i(TAG, "Android系统监控器已初始化");
    }

    /**
     * 获取单例实例
     */
    public static AndroidSystemMonitor getInstance(Context context) {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new AndroidSystemMonitor(context);
                }
            }
        }
        return instance;
    }

    /**
     * 开始监控
     */
    public void startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "系统监控已在运行");
            return;
        }

        isMonitoring = true;
        handler.post(monitorRunnable);
        Log.i(TAG, "Android系统监控已启动");
    }

    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }

        isMonitoring = false;
        handler.removeCallbacks(monitorRunnable);
        Log.i(TAG, "Android系统监控已停止");
    }

    /**
     * 注册监听器
     */
    public void registerListener(SystemMonitorListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * 注销监听器
     */
    public void unregisterListener(SystemMonitorListener listener) {
        listeners.remove(listener);
    }

    /**
     * 获取当前性能快照
     */
    public PerformanceSnapshot getCurrentPerformance() {
        return collectPerformanceData();
    }

    /**
     * 获取性能历史
     */
    public List<PerformanceSnapshot> getPerformanceHistory() {
        synchronized (performanceHistory) {
            return new ArrayList<>(performanceHistory);
        }
    }

    /**
     * 收集性能数据
     */
    private PerformanceSnapshot collectPerformanceData() {
        try {
            long timestamp = System.currentTimeMillis();

            // 内存信息
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);

            long totalMemory = memoryInfo.totalMem;
            long availableMemory = memoryInfo.availMem;
            long usedMemory = totalMemory - availableMemory;
            float memoryUsagePercent = (float) usedMemory / totalMemory * 100;

            // 堆内存信息
            Runtime runtime = Runtime.getRuntime();
            long heapUsed = runtime.totalMemory() - runtime.freeMemory();
            long heapMax = runtime.maxMemory();

            // 线程数
            int threadCount = Thread.activeCount();

            // 网络状态
            ConnectivityManager connectivityManager = (ConnectivityManager)
                context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            boolean isNetworkAvailable = networkInfo != null && networkInfo.isConnected();
            String networkType = networkInfo != null ? networkInfo.getTypeName() : "None";

            // 系统运行时间
            long uptimeMillis = SystemClock.elapsedRealtime();

            PerformanceSnapshot snapshot = new PerformanceSnapshot(
                timestamp, usedMemory, totalMemory, availableMemory, memoryUsagePercent,
                heapUsed, heapMax, threadCount, isNetworkAvailable, networkType, uptimeMillis
            );

            // 添加到历史记录
            synchronized (performanceHistory) {
                performanceHistory.add(snapshot);
                if (performanceHistory.size() > HISTORY_SIZE) {
                    performanceHistory.remove(0);
                }
            }

            // 检查告警条件
            checkAlerts(snapshot);

            // 通知监听器
            for (SystemMonitorListener listener : listeners) {
                try {
                    listener.onPerformanceUpdate(snapshot);
                } catch (Exception e) {
                    Log.e(TAG, "通知性能监听器失败", e);
                }
            }

            return snapshot;

        } catch (Exception e) {
            Log.e(TAG, "收集性能数据失败", e);
            return null;
        }
    }

    /**
     * 检查告警条件
     */
    private void checkAlerts(PerformanceSnapshot snapshot) {
        // 内存使用率告警
        if (snapshot.memoryUsagePercent > 90) {
            Log.w(TAG, "内存使用率过高: " + snapshot.memoryUsagePercent + "%");
            for (SystemMonitorListener listener : listeners) {
                try {
                    listener.onMemoryWarning(snapshot);
                } catch (Exception e) {
                    Log.e(TAG, "通知内存告警监听器失败", e);
                }
            }
        }

        // 网络状态变化检查
        if (snapshot.isNetworkAvailable != lastNetworkState ||
            !snapshot.networkType.equals(lastNetworkType)) {

            lastNetworkState = snapshot.isNetworkAvailable;
            lastNetworkType = snapshot.networkType;

            for (SystemMonitorListener listener : listeners) {
                try {
                    listener.onNetworkStateChanged(snapshot.isNetworkAvailable, snapshot.networkType);
                } catch (Exception e) {
                    Log.e(TAG, "通知网络状态监听器失败", e);
                }
            }
        }
    }

    /**
     * 获取系统信息摘要
     */
    public String getSystemSummary() {
        PerformanceSnapshot current = getCurrentPerformance();
        if (current == null) {
            return "系统信息获取失败";
        }

        return String.format(
            "系统摘要:\n" +
            "内存使用: %.1f%% (%dMB/%dMB)\n" +
            "堆内存: %dMB/%dMB\n" +
            "活跃线程: %d\n" +
            "网络状态: %s (%s)\n" +
            "运行时间: %s",
            current.memoryUsagePercent,
            current.usedMemory / 1024 / 1024,
            current.totalMemory / 1024 / 1024,
            current.heapUsed / 1024 / 1024,
            current.heapMax / 1024 / 1024,
            current.threadCount,
            current.isNetworkAvailable ? "已连接" : "未连接",
            current.networkType,
            formatUptime(current.uptimeMillis)
        );
    }

    /**
     * 格式化运行时间
     */
    private String formatUptime(long uptimeMillis) {
        long seconds = uptimeMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else {
            return String.format("%d分钟", minutes);
        }
    }
}
