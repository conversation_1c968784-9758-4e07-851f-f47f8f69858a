package com.gzcec.xrandroidclient.communication.messages.device;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;

import java.util.List;

/**
 * 设备状态请求消息
 */
public class DeviceStatusRequestMessage extends BaseMessage {
    private String requestId;
    private List<Integer> deviceIds; // 指定设备ID列表，为空则请求所有设备
    private boolean onlineOnly; // 是否只请求在线设备状态

    public DeviceStatusRequestMessage() {
        // BaseMessage构造函数会自动设置timestamp
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public List<Integer> getDeviceIds() {
        return deviceIds;
    }
    
    public void setDeviceIds(List<Integer> deviceIds) {
        this.deviceIds = deviceIds;
    }
    
    public boolean isOnlineOnly() {
        return onlineOnly;
    }
    
    public void setOnlineOnly(boolean onlineOnly) {
        this.onlineOnly = onlineOnly;
    }
}
