﻿using System;
using Newtonsoft.Json;

namespace XRSvc.DataPack
{
    /// <summary>
    /// 设备信息数据类
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int ID;

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string Name;

        /// <summary>
        /// 设备IP地址
        /// </summary>
        public string IpAddress;

        /// <summary>
        /// 设备类型
        /// </summary>
        public DeviceType DeviceType;

        /// <summary>
        /// 设备在线状态
        /// </summary>
        public bool IsOnline;

        /// <summary>
        /// 设备是否启用
        /// </summary>
        public bool IsEnabled;

        /// <summary>
        /// 电池电量 (0-100)
        /// </summary>
        public double BatteryLevel;

        /// <summary>
        /// 是否在游戏中
        /// </summary>
        public bool IsInGame;

        /// <summary>
        /// 当前游戏包名
        /// </summary>
        public string CurrentGamePackage;

        /// <summary>
        /// 游戏状态描述
        /// </summary>
        public string GameStatus;

        /// <summary>
        /// 游戏是否正在启动中
        /// </summary>
        public bool IsGameStarting;

        /// <summary>
        /// 设备是否被选中 (仅用于UI显示)
        /// </summary>
        public bool IsSelected;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated;

        /// <summary>
        /// 最后连接时间
        /// </summary>
        public DateTime LastConnected;
    }
}
