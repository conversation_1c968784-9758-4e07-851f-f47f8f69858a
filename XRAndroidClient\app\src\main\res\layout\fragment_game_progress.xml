<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">

    <!-- 顶部服务器Tab（单独一行） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginBottom="8dp"
        android:padding="8dp"
        android:orientation="horizontal"
        android:background="@drawable/column_bg_rounded">
        <LinearLayout
        android:id="@+id/server_tab_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical" />
    </LinearLayout>

    <!-- 选择游戏（单独一行） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/column_bg_rounded">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="选择游戏:"
            android:textColor="@color/btn_text_color_selector"
            android:textSize="16sp"/>
        <RadioGroup
            android:id="@+id/rg_game_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"/>
    </LinearLayout>

    <!-- 新增：服务器状态、启动按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/column_bg_rounded">
        <TextView
            android:id="@+id/tv_server_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="服务器未启动"
            android:textColor="#FF0000"
            android:textSize="16sp"
            android:layout_marginEnd="16dp"/>
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_server_action"
            android:layout_width="wrap_content"
            android:text="启动服务器"
            android:drawableStart="@android:drawable/ic_media_play"
            />
    </LinearLayout>

    <!-- 新增：设备操作按钮 -->
    <LinearLayout
        android:id="@+id/layout_device_ops"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:padding="8dp"
        android:background="@drawable/column_bg_rounded">
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_sort_by_id"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="按设备编号排序"
            android:drawableStart="@android:drawable/ic_menu_sort_by_size"
            android:layout_marginEnd="8dp" />
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_sort_by_battery"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="按剩余电量排序"
            android:drawableStart="@android:drawable/ic_menu_manage"
            android:layout_marginEnd="8dp" />
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_select_top12"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="选择排前12个设备"
            android:drawableStart="@android:drawable/ic_menu_add"
            android:layout_marginEnd="8dp" />
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_clear_selection"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="取消所有勾选" />
    </LinearLayout>

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_devices"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false"
        android:layout_marginBottom="8dp"
        android:background="@drawable/column_bg_rounded" />

    <!-- 底部批量操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/column_bg_rounded"
        android:padding="8dp"
        android:layout_marginBottom="10dp">
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_start_game"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/btn_start_game"
            android:drawableStart="@android:drawable/ic_media_play"
            android:layout_marginEnd="8dp" />
        <Button
            style="@style/UnifiedButton"
            android:id="@+id/btn_end_game"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/btn_end_game"
            android:drawableStart="@android:drawable/ic_menu_close_clear_cancel"
            />
    </LinearLayout>
</LinearLayout>