plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.gzcec.xrandroidclient'
    compileSdk 36

    defaultConfig {
        applicationId "com.gzcec.xrandroidclient"
        minSdk 25
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation libs.appcompat
    implementation libs.material

    // JSON处理
    implementation 'com.google.code.gson:gson:2.10.1'

    // MQTT客户端
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}