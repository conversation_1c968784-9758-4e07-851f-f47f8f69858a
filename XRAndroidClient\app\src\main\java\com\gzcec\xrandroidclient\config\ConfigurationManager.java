package com.gzcec.xrandroidclient.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Android端分层配置管理器
 * 支持SharedPreferences、配置验证、热更新等功能
 */
public class ConfigurationManager {
    private static final String TAG = "ConfigurationManager";
    
    // 单例实例
    private static volatile ConfigurationManager instance;
    private static final Object lock = new Object();
    
    // 配置存储
    private final Context context;
    private final SharedPreferences defaultPrefs;
    private final SharedPreferences userPrefs;
    private final SharedPreferences runtimePrefs;
    private final Gson gson;
    
    // 配置监听器
    private final List<ConfigurationChangeListener> listeners;
    private final Map<String, ConfigValidator> validators;
    private final Map<String, Object> cache;
    
    // 配置文件名
    private static final String DEFAULT_PREFS = "default_config";
    private static final String USER_PREFS = "user_config";
    private static final String RUNTIME_PREFS = "runtime_config";
    
    /**
     * 配置层级枚举
     */
    public enum ConfigLayer {
        DEFAULT(0),     // 默认配置
        USER(1),        // 用户配置
        RUNTIME(2);     // 运行时配置
        
        private final int priority;
        
        ConfigLayer(int priority) {
            this.priority = priority;
        }
        
        public int getPriority() {
            return priority;
        }
    }
    
    /**
     * 配置变更监听器接口
     */
    public interface ConfigurationChangeListener {
        void onConfigurationChanged(String key, Object oldValue, Object newValue);
    }
    
    /**
     * 配置验证器接口
     */
    public interface ConfigValidator {
        boolean validate(Object value);
        String getErrorMessage();
    }
    
    /**
     * 私有构造函数
     */
    private ConfigurationManager(Context context) {
        this.context = context.getApplicationContext();
        this.gson = new Gson();
        
        // 初始化SharedPreferences
        this.defaultPrefs = this.context.getSharedPreferences(DEFAULT_PREFS, Context.MODE_PRIVATE);
        this.userPrefs = this.context.getSharedPreferences(USER_PREFS, Context.MODE_PRIVATE);
        this.runtimePrefs = this.context.getSharedPreferences(RUNTIME_PREFS, Context.MODE_PRIVATE);
        
        // 初始化集合
        this.listeners = new CopyOnWriteArrayList<>();
        this.validators = new ConcurrentHashMap<>();
        this.cache = new ConcurrentHashMap<>();
        
        // 注册内置验证器
        registerBuiltinValidators();
        
        // 初始化默认配置
        initializeDefaultConfiguration();
        
        Log.i(TAG, "配置管理器已初始化");
    }
    
    /**
     * 获取单例实例
     */
    public static ConfigurationManager getInstance(Context context) {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new ConfigurationManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取配置值
     */
    public <T> T getValue(String key, T defaultValue, Class<T> type) {
        try {
            // 检查缓存
            Object cachedValue = cache.get(key);
            if (cachedValue != null) {
                return convertValue(cachedValue, type, defaultValue);
            }
            
            // 按优先级查找配置值
            Object value = null;
            
            // 运行时配置 (最高优先级)
            if (runtimePrefs.contains(key)) {
                value = getValueFromPrefs(runtimePrefs, key, type);
            }
            // 用户配置
            else if (userPrefs.contains(key)) {
                value = getValueFromPrefs(userPrefs, key, type);
            }
            // 默认配置 (最低优先级)
            else if (defaultPrefs.contains(key)) {
                value = getValueFromPrefs(defaultPrefs, key, type);
            }
            
            if (value == null) {
                value = defaultValue;
            }
            
            // 缓存结果
            cache.put(key, value);
            
            return convertValue(value, type, defaultValue);
            
        } catch (Exception e) {
            Log.e(TAG, "获取配置值失败: " + key, e);
            return defaultValue;
        }
    }
    
    /**
     * 设置配置值
     */
    public boolean setValue(String key, Object value, ConfigLayer layer) {
        try {
            // 验证配置值
            if (!validateValue(key, value)) {
                Log.w(TAG, "配置值验证失败: " + key);
                return false;
            }
            
            // 获取旧值
            Object oldValue = getValue(key, null, Object.class);
            
            // 选择对应的SharedPreferences
            SharedPreferences prefs = getPrefsForLayer(layer);
            SharedPreferences.Editor editor = prefs.edit();
            
            // 根据值类型存储
            if (value instanceof String) {
                editor.putString(key, (String) value);
            } else if (value instanceof Integer) {
                editor.putInt(key, (Integer) value);
            } else if (value instanceof Long) {
                editor.putLong(key, (Long) value);
            } else if (value instanceof Float) {
                editor.putFloat(key, (Float) value);
            } else if (value instanceof Boolean) {
                editor.putBoolean(key, (Boolean) value);
            } else {
                // 复杂对象使用JSON序列化
                String json = gson.toJson(value);
                editor.putString(key, json);
            }
            
            // 提交更改
            boolean success = editor.commit();
            
            if (success) {
                // 清除缓存
                cache.remove(key);
                
                // 通知监听器
                notifyConfigurationChanged(key, oldValue, value);
                
                Log.d(TAG, String.format("配置值已更新: %s = %s (层级: %s)", key, value, layer));
            }
            
            return success;
            
        } catch (Exception e) {
            Log.e(TAG, "设置配置值失败: " + key, e);
            return false;
        }
    }
    
    /**
     * 获取字符串配置
     */
    public String getString(String key, String defaultValue) {
        return getValue(key, defaultValue, String.class);
    }
    
    /**
     * 获取整数配置
     */
    public int getInt(String key, int defaultValue) {
        return getValue(key, defaultValue, Integer.class);
    }
    
    /**
     * 获取长整数配置
     */
    public long getLong(String key, long defaultValue) {
        return getValue(key, defaultValue, Long.class);
    }
    
    /**
     * 获取浮点数配置
     */
    public float getFloat(String key, float defaultValue) {
        return getValue(key, defaultValue, Float.class);
    }
    
    /**
     * 获取布尔配置
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        return getValue(key, defaultValue, Boolean.class);
    }
    
    /**
     * 获取对象配置
     */
    public <T> T getObject(String key, T defaultValue, Class<T> type) {
        try {
            String json = getString(key, null);
            if (json != null) {
                return gson.fromJson(json, type);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取对象配置失败: " + key, e);
        }
        return defaultValue;
    }
    
    /**
     * 设置字符串配置
     */
    public boolean setString(String key, String value, ConfigLayer layer) {
        return setValue(key, value, layer);
    }
    
    /**
     * 设置整数配置
     */
    public boolean setInt(String key, int value, ConfigLayer layer) {
        return setValue(key, value, layer);
    }
    
    /**
     * 设置布尔配置
     */
    public boolean setBoolean(String key, boolean value, ConfigLayer layer) {
        return setValue(key, value, layer);
    }
    
    /**
     * 设置对象配置
     */
    public boolean setObject(String key, Object value, ConfigLayer layer) {
        return setValue(key, value, layer);
    }
    
    /**
     * 注册配置变更监听器
     */
    public void registerListener(ConfigurationChangeListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 注销配置变更监听器
     */
    public void unregisterListener(ConfigurationChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 注册配置验证器
     */
    public void registerValidator(String key, ConfigValidator validator) {
        validators.put(key, validator);
    }
    
    /**
     * 清除配置缓存
     */
    public void clearCache() {
        cache.clear();
        Log.d(TAG, "配置缓存已清除");
    }
    
    /**
     * 重置配置层
     */
    public boolean resetLayer(ConfigLayer layer) {
        try {
            SharedPreferences prefs = getPrefsForLayer(layer);
            boolean success = prefs.edit().clear().commit();
            
            if (success) {
                clearCache();
                Log.i(TAG, "配置层已重置: " + layer);
            }
            
            return success;
        } catch (Exception e) {
            Log.e(TAG, "重置配置层失败: " + layer, e);
            return false;
        }
    }
    
    /**
     * 获取所有配置
     */
    public Map<String, Object> getAllConfigurations() {
        Map<String, Object> result = new HashMap<>();
        
        // 按优先级合并配置
        addPrefsToMap(result, defaultPrefs);
        addPrefsToMap(result, userPrefs);
        addPrefsToMap(result, runtimePrefs);
        
        return result;
    }
    
    /**
     * 从SharedPreferences获取值
     */
    private Object getValueFromPrefs(SharedPreferences prefs, String key, Class<?> type) {
        if (type == String.class) {
            return prefs.getString(key, null);
        } else if (type == Integer.class || type == int.class) {
            return prefs.getInt(key, 0);
        } else if (type == Long.class || type == long.class) {
            return prefs.getLong(key, 0L);
        } else if (type == Float.class || type == float.class) {
            return prefs.getFloat(key, 0f);
        } else if (type == Boolean.class || type == boolean.class) {
            return prefs.getBoolean(key, false);
        } else {
            // 复杂对象从JSON反序列化
            String json = prefs.getString(key, null);
            if (json != null) {
                try {
                    return gson.fromJson(json, type);
                } catch (Exception e) {
                    Log.e(TAG, "JSON反序列化失败: " + key, e);
                }
            }
        }
        return null;
    }
    
    /**
     * 根据层级获取SharedPreferences
     */
    private SharedPreferences getPrefsForLayer(ConfigLayer layer) {
        switch (layer) {
            case DEFAULT:
                return defaultPrefs;
            case USER:
                return userPrefs;
            case RUNTIME:
                return runtimePrefs;
            default:
                return runtimePrefs;
        }
    }
    
    /**
     * 转换值类型
     */
    @SuppressWarnings("unchecked")
    private <T> T convertValue(Object value, Class<T> type, T defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        
        try {
            if (type.isInstance(value)) {
                return (T) value;
            }
            
            // 类型转换
            if (type == String.class) {
                return (T) value.toString();
            } else if (type == Integer.class || type == int.class) {
                return (T) Integer.valueOf(value.toString());
            } else if (type == Long.class || type == long.class) {
                return (T) Long.valueOf(value.toString());
            } else if (type == Float.class || type == float.class) {
                return (T) Float.valueOf(value.toString());
            } else if (type == Boolean.class || type == boolean.class) {
                return (T) Boolean.valueOf(value.toString());
            }
            
            return defaultValue;
        } catch (Exception e) {
            Log.e(TAG, "类型转换失败", e);
            return defaultValue;
        }
    }
    
    /**
     * 验证配置值
     */
    private boolean validateValue(String key, Object value) {
        ConfigValidator validator = validators.get(key);
        if (validator != null) {
            boolean isValid = validator.validate(value);
            if (!isValid) {
                Log.w(TAG, "配置验证失败: " + key + " - " + validator.getErrorMessage());
            }
            return isValid;
        }
        return true;
    }
    
    /**
     * 通知配置变更
     */
    private void notifyConfigurationChanged(String key, Object oldValue, Object newValue) {
        for (ConfigurationChangeListener listener : listeners) {
            try {
                listener.onConfigurationChanged(key, oldValue, newValue);
            } catch (Exception e) {
                Log.e(TAG, "通知配置变更监听器失败", e);
            }
        }
    }
    
    /**
     * 将SharedPreferences添加到Map
     */
    private void addPrefsToMap(Map<String, Object> map, SharedPreferences prefs) {
        for (Map.Entry<String, ?> entry : prefs.getAll().entrySet()) {
            map.put(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfiguration() {
        // 如果默认配置为空，设置默认值
        if (defaultPrefs.getAll().isEmpty()) {
            SharedPreferences.Editor editor = defaultPrefs.edit();
            
            // 网络配置
            editor.putString("mqtt.host", "*************");
            editor.putInt("mqtt.port", 1883);
            editor.putInt("mqtt.timeout", 30000);
            editor.putInt("mqtt.keepalive", 60);
            editor.putBoolean("mqtt.auto_discovery", true);
            
            // 日志配置
            editor.putString("log.level", "INFO");
            editor.putInt("log.max_files", 10);
            editor.putLong("log.max_size", 10485760L); // 10MB
            
            // UI配置
            editor.putString("ui.language", "zh-CN");
            editor.putString("ui.theme", "dark");
            editor.putBoolean("ui.auto_refresh", true);
            editor.putInt("ui.refresh_interval", 5000);
            
            // 设备配置
            editor.putInt("device.heartbeat", 30000);
            editor.putInt("device.timeout", 60000);
            
            editor.apply();
            Log.i(TAG, "默认配置已初始化");
        }
    }
    
    /**
     * 注册内置验证器
     */
    private void registerBuiltinValidators() {
        // MQTT端口验证器
        registerValidator("mqtt.port", new ConfigValidator() {
            @Override
            public boolean validate(Object value) {
                try {
                    int port = Integer.parseInt(value.toString());
                    return port >= 1 && port <= 65535;
                } catch (Exception e) {
                    return false;
                }
            }
            
            @Override
            public String getErrorMessage() {
                return "端口号必须在1-65535之间";
            }
        });
        
        // 日志级别验证器
        registerValidator("log.level", new ConfigValidator() {
            private final String[] validLevels = {"DEBUG", "INFO", "WARNING", "ERROR"};
            
            @Override
            public boolean validate(Object value) {
                String level = value.toString().toUpperCase();
                return Arrays.asList(validLevels).contains(level);
            }
            
            @Override
            public String getErrorMessage() {
                return "日志级别必须是: DEBUG, INFO, WARNING, ERROR";
            }
        });
        
        // 刷新间隔验证器
        registerValidator("ui.refresh_interval", new ConfigValidator() {
            @Override
            public boolean validate(Object value) {
                try {
                    int interval = Integer.parseInt(value.toString());
                    return interval >= 1000 && interval <= 60000;
                } catch (Exception e) {
                    return false;
                }
            }
            
            @Override
            public String getErrorMessage() {
                return "刷新间隔必须在1000-60000毫秒之间";
            }
        });
    }
}
