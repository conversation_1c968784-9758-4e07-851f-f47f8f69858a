﻿using Jskj.XRSystem.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using XRSvc.CustomControl;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 游戏列表, key=GameID
        /// </summary>
        private readonly Dictionary<int, GameInfo> GameList;

        public ObservableCollection<GameInfo> GameListValues { get; set; }

        public MainWindow()
        {
            InitializeComponent();
        }

        public MainWindow(List<GameInfo> gameList)
        {
            this.GameList = gameList.ToDictionary(g => g.ID, g => g);
            this.GameListValues = new ObservableCollection<GameInfo>(gameList);
            DataContext = this;
            InitializeComponent();
        }
    }
}

