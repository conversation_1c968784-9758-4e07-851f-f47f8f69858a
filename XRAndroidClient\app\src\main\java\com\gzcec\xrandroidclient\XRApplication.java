package com.gzcec.xrandroidclient;

import android.app.Application;
import android.util.Log;

import com.gzcec.xrandroidclient.communication.MqttCommunicationManager;
import com.gzcec.xrandroidclient.utils.LogManager;
import com.gzcec.xrandroidclient.utils.XRLog;

/**
 * 应用程序入口类
 * 负责全局初始化工作
 */
public class XRApplication extends Application {
    private static final String TAG = "XRApplication";
    
    @Override
    public void onCreate() {
        super.onCreate();
        long startTime = System.currentTimeMillis();

        Log.i(TAG, "XR应用启动");

        // 初始化日志管理器
        initializeLogManager();

        // 记录应用启动信息
        XRLog.i(TAG, "🚀 XR应用启动");
        XRLog.memory(TAG);

        // 初始化MQTT通信管理器
        initializeMqttCommunicationManager();

        XRLog.time(TAG, "应用启动", startTime);
        XRLog.i(TAG, "✅ XR应用启动完成");
    }

    /**
     * 初始化日志管理器
     */
    private void initializeLogManager() {
        try {
            LogManager.getInstance().initialize(this);
            Log.i(TAG, "日志管理器初始化成功");

            // 启用调试模式
            XRLog.setDebugMode(true);

        } catch (Exception e) {
            Log.e(TAG, "日志管理器初始化失败", e);
        }
    }

    /**
     * 初始化MQTT通信管理器
     */
    private void initializeMqttCommunicationManager() {
        try {
            MqttCommunicationManager.getInstance().initialize(this);
            Log.i(TAG, "MQTT通信管理器初始化成功");
        } catch (Exception e) {
            Log.e(TAG, "MQTT通信管理器初始化失败", e);
        }
    }
    
    @Override
    public void onTerminate() {
        super.onTerminate();
        Log.i(TAG, "XR应用终止");
        
        // 销毁MQTT通信管理器
        try {
            MqttCommunicationManager.getInstance().cleanup();
            Log.i(TAG, "MQTT通信管理器已销毁");
        } catch (Exception e) {
            Log.e(TAG, "销毁MQTT通信管理器失败", e);
        }

        // 销毁日志管理器
        try {
            LogManager.getInstance().shutdown();
            Log.i(TAG, "日志管理器已销毁");
        } catch (Exception e) {
            Log.e(TAG, "销毁日志管理器失败", e);
        }
    }
}
