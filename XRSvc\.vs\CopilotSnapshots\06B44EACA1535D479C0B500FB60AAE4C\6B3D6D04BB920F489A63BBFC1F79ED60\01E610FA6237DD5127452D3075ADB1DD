﻿<Window x:Class="XRSvc.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:XRSvc.CustomControl"
        mc:Ignorable="d"
        Title="XR播放系统 - 店铺端" Height="730" Width="1045" WindowState="Normal" WindowStartupLocation="CenterScreen" ResizeMode="NoResize" AllowsTransparency="True" WindowStyle="None" Background="{x:Null}">
    
    <!--资源,只用于设计，运行时使用程序入口统一的资源-->
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/style.xaml"/>
                <ResourceDictionary Source="/Resources/zh-cn.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid x:Name="Grid_Main" Visibility="Visible">
        <!--标题栏-->
        <Grid x:Name="TitleBar" VerticalAlignment="Top" Background="#2D2D30">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,0,0" Height="35">

                <!--最小化按钮-->
                <Grid x:Name="Btn_WinMin" Margin="0,0,0,0" Width="35" Height="35" Style="{DynamicResource TitleBarStyle}">
                    <Path Data="M 0,0 L 12,0 L 12,1 L 0,1 z" Fill="White" HorizontalAlignment="Center" VerticalAlignment="Center" SnapsToDevicePixels="True"/>
                </Grid>

                <!--窗口最大化和向下还原按钮-->
                <Grid IsEnabled="False">
                    <!--窗口最大化按钮-->
                    <Grid x:Name="Btn_WinMax" Margin="5,0,0,0" Width="35" Height="35" Style="{DynamicResource TitleBarStyle}">
                        <Rectangle Width="12" Height="12" StrokeThickness="0.7" Stroke="White" SnapsToDevicePixels="True" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Grid>

                    <!--向下还原按钮-->
                    <Grid x:Name="Btn_WinRecover" Visibility="Collapsed" Margin="5,0,0,0" Width="35" Height="35" Style="{DynamicResource TitleBarStyle}">
                        <Rectangle Width="9" Height="9" StrokeThickness="0.7" Stroke="White" SnapsToDevicePixels="True" HorizontalAlignment="Left" VerticalAlignment="top" Margin="14,15,0,0"/>
                        <Rectangle Width="9" Height="9" StrokeThickness="0.7" Stroke="White" SnapsToDevicePixels="True" HorizontalAlignment="Left" VerticalAlignment="top" Margin="12,17,0,0" Fill="{Binding ElementName=TitleBar, Path=Background}"/>
                    </Grid>
                </Grid>

                <!--关闭按钮-->
                <Grid x:Name="Btn_WinClose" Width="35" Height="35" Margin="5,0,0,0" Style="{DynamicResource TitleBarStyle}">
                    <Grid Width="12" Height="12" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Line X1="0" Y1="0" X2="11" Y2="11" Style="{DynamicResource LineStyle}"/>
                        <Line X1="11" Y1="0" X2="0" Y2="11" Style="{DynamicResource LineStyle}"/>
                    </Grid>
                </Grid>
            </StackPanel>
        </Grid>

        <!--窗口主要内容-->
        <Grid Margin="0,35,0,0" Background="#F0F0F0">
            <TabControl>
                <!--服务器: 游戏-->
                <TabItem Header="游戏" Width="50">

                    <!--TabControl: 游戏服务器-->
                    <Grid x:Name="Grid_Content" >

                        <TabControl Margin="5,5,5,0" >

                            <!--服务器1-->
                            <TabItem Header="服务器1">
                                <StackPanel Orientation="Vertical" >

                                    <!--游戏列表-->
                                    <GroupBox Header="游戏列表" Height="60" Margin="10,5,10,0" Padding="5">
                                        <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" ItemWidth="100">
                                            <local:GameStyle/>
                                            <local:GameStyle/>
                                            <local:GameStyle/>
                                        </WrapPanel>
                                    </GroupBox>

                                    <!--游戏服务器-->
                                    <GroupBox Header="游戏服务器" Height="60" Margin="10,5,10,0" Padding="10,0,0,0">
                                        <local:GameServerStyle/>
                                    </GroupBox>

                                    <!--设备列表-->
                                    <TabControl Height="430" Background="#F9F9F9" VerticalAlignment="Top" Margin="10,10,10,0">
                                        <TabItem Header="设备列表1">
                                            <ScrollViewer>
                                                <WrapPanel Orientation="Horizontal" >
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>

                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>

                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                </WrapPanel>
                                            </ScrollViewer>
                                        </TabItem>
                                        <TabItem Header="设备列表2">
                                            <ScrollViewer>
                                                <WrapPanel Orientation="Horizontal" >
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>

                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>

                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                    <local:XRClientStyle Margin="10,10,0,0"/>
                                                </WrapPanel>
                                            </ScrollViewer>
                                        </TabItem>
                                    </TabControl>

                                    <StackPanel Orientation="Horizontal" Margin="10,20,10,0">
                                        <Button Content="启动游戏" Width="110" Height="35" Background="#FDFDFD"/>
                                        <Button Content="开始游戏" Width="110" Height="35" Margin="30,0,0,0" Background="#FDFDFD"/>
                                        <Button Content="结束游戏" Width="110" Height="35" Margin="30,0,0,0" Background="#FDFDFD"/>
                                    </StackPanel>

                                </StackPanel>
                            </TabItem>


                        </TabControl>

                    </Grid>
                </TabItem>

                <!--TabItem: 设备-->
                <TabItem Header="设备" Width="50">
                    <Grid Background="#F9F9F9">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <GroupBox Header="设备管理服务器" Margin="10,10,10,0" Padding="5" Grid.Row="0">
                            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" ItemWidth="150">
                                <local:DeviceServerStyle/>
                                <local:DeviceServerStyle/>
                            </WrapPanel>
                        </GroupBox>
                        <GroupBox Header="设备列表" Margin="10,10,10,10" Padding="5" Grid.Row="1">  
                           <ScrollViewer VerticalScrollBarVisibility="Auto">  
                               <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Top" ItemWidth="166" ItemHeight="60">
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>

                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                    <local:DeviceStyle/>
                                </WrapPanel>  
                           </ScrollViewer>  
                        </GroupBox>
                    </Grid>
                </TabItem>

                <!--TabItem: 日志-->
                <TabItem Header="日志" Width="50">
                    <Grid Background="#F9F9F9">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="10">
                            <TextBlock Text="这里是日志内容示例。可以显示多行日志信息，内容较多时可通过滚动条查看全部内容。&#x0a;日志1: 系统启动完成。&#x0a;日志2: 用户登录成功。&#x0a;日志3: 游戏服务器已连接。&#x0a;日志4: 设备状态正常。&#x0a;日志5: 其他信息……"
                                       TextWrapping="Wrap"
                                       FontSize="14"
                                       Foreground="Black"/>
                        </ScrollViewer>
                    </Grid>
                </TabItem>

                <!--TabItem: 设置-->
                <TabItem Header="设置">
                    <Grid Background="#F9F9F9">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="126*"/>
                            <ColumnDefinition Width="913*"/>
                        </Grid.ColumnDefinitions>
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>

    </Grid>
</Window>
