package com.gzcec.xrandroidclient.communication.messages.device;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.device.DeviceInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备列表响应消息
 */
public class DeviceListResponseMessage extends BaseMessage {
    private String requestId;
    private List<DeviceInfo> devices;
    private int totalCount;
    private int onlineCount;

    public DeviceListResponseMessage() {
        setType(MessageType.DEVICE_LIST_RESPONSE);
        this.devices = new ArrayList<>();
    }

    // Getter和Setter方法
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }
    
    public List<DeviceInfo> getDevices() { return devices; }
    public void setDevices(List<DeviceInfo> devices) { this.devices = devices; }
    
    public int getTotalCount() { return totalCount; }
    public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
    
    public int getOnlineCount() { return onlineCount; }
    public void setOnlineCount(int onlineCount) { this.onlineCount = onlineCount; }
}
