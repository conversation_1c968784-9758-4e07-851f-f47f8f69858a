# XR系统 Android客户端开发文档

## 项目概述

XR系统Android客户端是一个用于管理和控制XR设备的移动应用程序，通过MQTT协议与PC服务端进行通信，实现设备状态监控、游戏启动控制等功能。

## 技术架构

### 核心技术栈
- **开发语言**: Java
- **最低SDK版本**: API 21 (Android 5.0)
- **目标SDK版本**: API 34 (Android 14)
- **通信协议**: MQTT
- **数据格式**: JSON
- **UI框架**: Android原生UI + RecyclerView

### 主要依赖库
```gradle
dependencies {
    // Android核心库
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    
    // JSON处理
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // MQTT客户端
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'
    
    // 测试库
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
```

## 项目结构

```
XRAndroidClient/
├── app/
│   ├── src/main/java/com/gzcec/xrandroidclient/
│   │   ├── MainActivity.java                    # 主活动
│   │   ├── DeviceIdManager.java                # 设备ID管理器
│   │   ├── communication/                      # 通信模块
│   │   │   ├── BaseMessage.java               # 消息基类
│   │   │   ├── MessageType.java               # 消息类型枚举
│   │   │   ├── MqttGameClient.java            # MQTT客户端
│   │   │   ├── DeviceStatusInfo.java          # 设备状态信息
│   │   │   ├── GameInfo.java                  # 游戏信息
│   │   │   ├── DeviceStartResult.java         # 设备启动结果
│   │   │   ├── *RequestMessage.java           # 各种请求消息
│   │   │   ├── *ResponseMessage.java          # 各种响应消息
│   │   │   └── *UpdateMessage.java            # 各种更新消息
│   │   ├── device/                            # 设备管理模块
│   │   │   ├── DeviceInfo.java               # 设备信息类
│   │   │   └── DeviceAdapter.java            # 设备列表适配器
│   │   ├── gameprogress/                      # 游戏进度模块
│   │   │   ├── GameProgressFragment.java     # 游戏进度界面
│   │   │   └── GameProgressDeviceAdapter.java # 游戏设备适配器
│   │   └── gameselection/                     # 游戏选择模块
│   │       ├── GameSelectionFragment.java    # 游戏选择界面
│   │       └── GameAdapter.java              # 游戏列表适配器
│   ├── src/main/res/                          # 资源文件
│   └── src/main/AndroidManifest.xml          # 应用清单
├── build.gradle                               # 应用级构建配置
├── proguard-rules.pro                        # 代码混淆规则
└── docs/                                      # 文档目录
    └── Android开发文档.md                     # 本文档
```

## 核心模块详解

### 1. 通信模块 (communication)

#### 1.1 消息系统架构

**BaseMessage.java** - 所有消息的基类
```java
public class BaseMessage {
    private String messageId;      // 消息ID
    private MessageType type;      // 消息类型
    private Date timestamp;        // 时间戳
    private String senderId;       // 发送者ID
    private String receiverId;     // 接收者ID
}
```

**MessageType.java** - 消息类型枚举
```java
public enum MessageType {
    DEVICE_LIST_REQUEST,        // 设备列表请求
    DEVICE_LIST_RESPONSE,       // 设备列表响应
    DEVICE_STATUS_UPDATE,       // 设备状态更新
    DEVICE_SELECTION_CHANGED,   // 设备选择变更
    GAME_START_REQUEST,         // 游戏启动请求
    GAME_START_RESPONSE,        // 游戏启动响应
    GAME_STOP_REQUEST,          // 游戏停止请求
    GAME_STOP_RESPONSE,         // 游戏停止响应
    GAME_PROGRESS_UPDATE,       // 游戏进度更新
    HEARTBEAT,                  // 心跳
    ERROR,                      // 错误
    ACKNOWLEDGMENT,             // 确认
    CONNECTION_STATUS           // 连接状态
}
```

#### 1.2 核心数据类

**DeviceStatusInfo.java** - 设备状态信息
```java
public class DeviceStatusInfo {
    private String deviceId;           // 设备ID
    private String serialNumber;       // 序列号
    private String name;              // 设备名称
    private String ipAddress;         // IP地址
    private boolean isOnline;         // 是否在线
    private int batteryLevel;         // 电池电量
    private boolean isInGame;         // 是否在游戏中
    private String currentGamePackage; // 当前游戏包名
    private String deviceType;        // 设备类型
    private Date lastUpdated;         // 最后更新时间
    private boolean isSelected;       // 是否被选中
}
```

**GameInfo.java** - 游戏信息
```java
public class GameInfo {
    private int id;                   // 游戏ID
    private String name;              // 游戏名称
    private String packageName;       // 包名
    private String version;           // 版本
    private String description;       // 描述
    private String iconPath;          // 图标路径
    private int duration;             // 游戏时长
    private boolean isAvailable;      // 是否可用
}
```

#### 1.3 MQTT客户端

**MqttGameClient.java** - MQTT通信客户端
```java
public class MqttGameClient {
    // 核心方法
    public void connect(String brokerUrl, String clientId)  // 连接MQTT服务器
    public void disconnect()                                // 断开连接
    public void subscribeToTopics()                        // 订阅主题
    public void publishMessage(String topic, String payload) // 发布消息
    
    // 消息处理方法
    private void handleDeviceListResponse(String payload)   // 处理设备列表响应
    private void handleGameStartResponse(String payload)    // 处理游戏启动响应
    private void handleDeviceStatusUpdate(String payload)  // 处理设备状态更新
}
```

### 2. 设备管理模块 (device)

#### 2.1 设备信息类
**DeviceInfo.java** - 本地设备信息管理
```java
public class DeviceInfo {
    private String id;                // 设备ID
    private String name;              // 设备名称
    private String serialNumber;      // 序列号
    private String ipAddress;         // IP地址
    private boolean isOnline;         // 在线状态
    private int batteryLevel;         // 电池电量
    private boolean isSelected;       // 选中状态
}
```

#### 2.2 设备适配器
**DeviceAdapter.java** - RecyclerView适配器，用于显示设备列表

### 3. 游戏进度模块 (gameprogress)

#### 3.1 游戏进度界面
**GameProgressFragment.java** - 游戏进度管理界面
```java
public class GameProgressFragment extends Fragment {
    // 核心功能
    private void startGame()                    // 启动游戏
    private void stopGame()                     // 停止游戏
    private void updateDeviceList()             // 更新设备列表
    private void updateGameProgress()           // 更新游戏进度
    
    // MQTT回调方法
    public void onGameStartResponse(GameStartResponseMessage response)
    public void onGameStopResponse(GameStopResponseMessage response)
    public void onGameProgressUpdate(GameProgressUpdateMessage update)
    public void onDeviceListReceived(DeviceListResponseMessage response)
    public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update)
}
```

### 4. 游戏选择模块 (gameselection)

#### 4.1 游戏选择界面
**GameSelectionFragment.java** - 游戏选择界面
- 显示可用游戏列表
- 支持游戏搜索和筛选
- 游戏详情展示

## 通信协议

### MQTT主题结构
```
xr/devices/list/request          # 设备列表请求
xr/devices/list/response         # 设备列表响应
xr/devices/status/update         # 设备状态更新
xr/devices/selection/changed     # 设备选择变更
xr/games/start/request           # 游戏启动请求
xr/games/start/response          # 游戏启动响应
xr/games/stop/request            # 游戏停止请求
xr/games/stop/response           # 游戏停止响应
xr/games/progress/update         # 游戏进度更新
xr/system/heartbeat              # 系统心跳
xr/system/connection/status      # 连接状态
```

### 消息格式示例

#### 设备列表请求
```json
{
    "messageId": "uuid-string",
    "type": "DEVICE_LIST_REQUEST",
    "timestamp": "2024-01-01T12:00:00Z",
    "senderId": "android-client",
    "onlineOnly": true,
    "deviceTypeFilter": "VR"
}
```

#### 游戏启动请求
```json
{
    "messageId": "uuid-string",
    "type": "GAME_START_REQUEST",
    "timestamp": "2024-01-01T12:00:00Z",
    "senderId": "android-client",
    "selectedGame": {
        "id": 1,
        "name": "VR游戏示例",
        "packageName": "com.example.vrgame"
    },
    "selectedDeviceIds": ["device1", "device2"],
    "gameParameters": {
        "difficulty": "normal",
        "duration": 1800
    }
}
```

## 开发指南

### 1. 环境搭建
1. 安装Android Studio (推荐最新稳定版)
2. 配置Android SDK (API 21-34)
3. 克隆项目代码
4. 同步Gradle依赖

### 2. 编译运行
```bash
# 清理项目
./gradlew clean

# 编译Debug版本
./gradlew assembleDebug

# 安装到设备
./gradlew installDebug

# 运行测试
./gradlew test
```

### 3. 代码规范
- 遵循Java代码规范
- 使用驼峰命名法
- 添加必要的注释
- 每个public类必须在独立的.java文件中

### 4. 调试技巧
- 使用Android Studio的调试器
- 查看Logcat日志
- 使用MQTT客户端工具测试通信
- 网络抓包分析MQTT消息

## 常见问题

### 1. MQTT连接问题
- 检查网络权限配置
- 确认MQTT服务器地址和端口
- 验证客户端ID唯一性

### 2. 消息序列化问题
- 确保Gson依赖正确添加
- 检查消息类的字段命名
- 验证JSON格式正确性

### 3. UI更新问题
- 确保在主线程更新UI
- 使用Handler或runOnUiThread
- 检查RecyclerView适配器通知

## 版本历史

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础MQTT通信功能
- 设备列表管理
- 游戏启动控制

### v1.1.0 (当前版本)
- 重构通信模块架构
- 修复Java文件结构问题
- 优化消息类型定义
- 完善错误处理机制
- 更新依赖库版本

## API接口文档

### 1. MqttGameClient API

#### 连接管理
```java
// 连接到MQTT服务器
public void connect(String brokerUrl, String clientId) throws Exception

// 断开连接
public void disconnect() throws Exception

// 检查连接状态
public boolean isConnected()

// 设置连接回调
public void setConnectionCallback(MqttConnectionCallback callback)
```

#### 消息发布
```java
// 发布设备列表请求
public void requestDeviceList(boolean onlineOnly, String deviceTypeFilter)

// 发布游戏启动请求
public void requestGameStart(GameInfo game, List<String> deviceIds, Map<String, Object> parameters)

// 发布游戏停止请求
public void requestGameStop(String gameId)

// 发布设备选择变更
public void notifyDeviceSelectionChanged(List<String> selectedDeviceIds)

// 发布心跳消息
public void sendHeartbeat(String status, Map<String, Object> info)
```

#### 消息订阅回调
```java
// 设置消息回调接口
public void setMessageCallback(MqttMessageCallback callback)

// 消息回调接口定义
public interface MqttMessageCallback {
    void onDeviceListReceived(DeviceListResponseMessage response);
    void onGameStartResponse(GameStartResponseMessage response);
    void onGameStopResponse(GameStopResponseMessage response);
    void onGameProgressUpdate(GameProgressUpdateMessage update);
    void onDeviceStatusUpdate(DeviceStatusUpdateMessage update);
    void onConnectionStatusChanged(ConnectionStatusMessage status);
    void onError(ErrorMessage error);
}
```

### 2. DeviceIdManager API

```java
// 获取设备唯一ID
public static String getDeviceId(Context context)

// 获取设备名称
public static String getDeviceName(Context context)

// 获取设备型号
public static String getDeviceModel()

// 获取应用版本
public static String getAppVersion(Context context)
```

### 3. GameProgressFragment API

```java
// 初始化界面
public void initializeUI()

// 更新设备列表
public void updateDeviceList(List<DeviceStatusInfo> devices)

// 更新游戏进度
public void updateGameProgress(GameProgressUpdateMessage progress)

// 显示错误信息
public void showError(String message)

// 显示成功信息
public void showSuccess(String message)
```

## 配置文件

### 1. AndroidManifest.xml 配置

```xml
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 设备信息权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/Theme.XRAndroidClient">

        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

    </application>
</manifest>
```

### 2. build.gradle 配置

```gradle
android {
    namespace 'com.gzcec.xrandroidclient'
    compileSdk 34

    defaultConfig {
        applicationId "com.gzcec.xrandroidclient"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
            applicationIdSuffix ".debug"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
```

## 测试指南

### 1. 单元测试

#### 测试MQTT客户端
```java
@Test
public void testMqttConnection() {
    MqttGameClient client = new MqttGameClient();
    // 测试连接逻辑
    assertTrue(client.connect("tcp://localhost:1883", "test-client"));
    assertTrue(client.isConnected());
    client.disconnect();
    assertFalse(client.isConnected());
}

@Test
public void testMessageSerialization() {
    GameStartRequestMessage request = new GameStartRequestMessage();
    request.setSelectedGame(new GameInfo());

    Gson gson = new Gson();
    String json = gson.toJson(request);
    GameStartRequestMessage deserialized = gson.fromJson(json, GameStartRequestMessage.class);

    assertEquals(request.getType(), deserialized.getType());
}
```

#### 测试设备管理
```java
@Test
public void testDeviceIdGeneration() {
    Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
    String deviceId = DeviceIdManager.getDeviceId(context);

    assertNotNull(deviceId);
    assertFalse(deviceId.isEmpty());

    // 多次调用应返回相同ID
    String deviceId2 = DeviceIdManager.getDeviceId(context);
    assertEquals(deviceId, deviceId2);
}
```

### 2. 集成测试

#### 测试MQTT通信流程
```java
@Test
public void testGameStartFlow() {
    // 1. 连接MQTT服务器
    MqttGameClient client = new MqttGameClient();
    client.connect("tcp://test-broker:1883", "test-client");

    // 2. 设置回调
    client.setMessageCallback(new MqttMessageCallback() {
        @Override
        public void onGameStartResponse(GameStartResponseMessage response) {
            assertTrue(response.isSuccess());
        }
    });

    // 3. 发送游戏启动请求
    GameInfo game = new GameInfo();
    game.setId(1);
    game.setName("测试游戏");

    List<String> deviceIds = Arrays.asList("device1", "device2");
    client.requestGameStart(game, deviceIds, new HashMap<>());

    // 4. 等待响应
    Thread.sleep(5000);
}
```

### 3. UI测试

#### 测试游戏进度界面
```java
@Test
public void testGameProgressFragment() {
    ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class);

    // 切换到游戏进度页面
    onView(withId(R.id.navigation_game_progress)).perform(click());

    // 验证界面元素
    onView(withId(R.id.device_list_recycler_view)).check(matches(isDisplayed()));
    onView(withId(R.id.start_game_button)).check(matches(isDisplayed()));
    onView(withId(R.id.stop_game_button)).check(matches(isDisplayed()));
}
```

## 性能优化

### 1. 内存优化
- 及时释放MQTT连接资源
- 使用弱引用避免内存泄漏
- 合理管理Bitmap资源
- 避免在循环中创建大量对象

### 2. 网络优化
- 实现MQTT消息队列机制
- 添加网络状态监听
- 实现断线重连逻辑
- 压缩大型消息内容

### 3. UI优化
- 使用ViewHolder模式优化RecyclerView
- 实现图片懒加载
- 避免在主线程执行耗时操作
- 使用异步任务处理网络请求

## 安全考虑

### 1. 网络安全
- 使用SSL/TLS加密MQTT连接
- 实现消息签名验证
- 添加访问令牌机制
- 防止中间人攻击

### 2. 数据安全
- 敏感数据本地加密存储
- 避免在日志中输出敏感信息
- 实现数据完整性校验
- 定期清理临时数据

## 部署指南

### 1. 发布准备
```bash
# 生成签名密钥
keytool -genkey -v -keystore release-key.keystore -alias xr-android -keyalg RSA -keysize 2048 -validity 10000

# 配置签名信息
# 在app/build.gradle中添加signingConfigs

# 构建发布版本
./gradlew assembleRelease
```

### 2. 版本管理
- 遵循语义化版本规范
- 维护版本更新日志
- 实现应用内更新机制
- 支持灰度发布策略

## 故障排除

### 1. 常见编译错误
- **找不到符号错误**: 检查import语句和类路径
- **重复类定义**: 确保每个public类在独立文件中
- **依赖冲突**: 使用gradle dependency命令检查依赖树

### 2. 运行时错误
- **MQTT连接失败**: 检查网络权限和服务器配置
- **JSON解析错误**: 验证消息格式和字段映射
- **UI更新异常**: 确保在主线程更新UI组件

### 3. 性能问题
- **内存泄漏**: 使用LeakCanary检测内存问题
- **ANR问题**: 避免在主线程执行耗时操作
- **网络超时**: 合理设置超时时间和重试机制

## 联系方式

如有问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 项目地址: https://github.com/gzcec/xr-system
- 文档地址: https://docs.gzcec.com/xr-system
