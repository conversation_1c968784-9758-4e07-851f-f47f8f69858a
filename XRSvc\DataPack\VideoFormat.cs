﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XRSvc.DataPack
{
    /// <summary>
    /// 影片格式
    /// </summary>
    public enum VideoFormat
    {
        TYPE_2D = 0,        //  2D视频
        TYPE_3D_LR = 1,     //  3D视频 左右
        TYPE_360 = 2,       //  360视频
        TYPE_3D360_TB = 3,  //  3D360 上下
        TYPE_3D360_BT = 4,  //  3D360 下上
        TYPE_3D360_LR = 5,  //  3D360 左右
        TYPE_3D360_RL = 6,  //  3D360 右左
        TYPE_3D_TB = 7,     //  180视频 上下
        TYPE_3D_BT = 8,     //  3D180 下上
        TYPE_3D_RL = 9,     //  3D180 右左
        TYPE_180 = 10,      //  180视频
        TYPE_3D180_TB = 11, //  3D180 上下
        TYPE_3D180_BT = 12, //  3D180 下上
        TYPE_3D180_LR = 13, //  3D180 左右
        TYPE_3D180_RL = 14, //  3D180 右左
        TYPE_UNKNOWN = 15,  //  未知类型
    }
}
