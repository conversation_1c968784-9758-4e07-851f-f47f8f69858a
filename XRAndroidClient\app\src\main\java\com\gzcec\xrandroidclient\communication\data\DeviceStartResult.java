package com.gzcec.xrandroidclient.communication.data;

import java.util.Date;

/**
 * 设备启动结果数据类
 */
public class DeviceStartResult {
    private String deviceId;
    private String serialNumber;
    private boolean success;
    private String errorMessage;
    private Date startTime;

    // Getter和Setter方法
    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    
    public String getSerialNumber() { return serialNumber; }
    public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public Date getStartTime() { return startTime; }
    public void setStartTime(Date startTime) { this.startTime = startTime; }
}
