﻿<UserControl x:Class="XRSvc.CustomControl.DeviceServerStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="30" Width="140">
    <Grid x:Name="GamePanel" FocusVisualStyle="{x:Null}" Height="30">
        <StackPanel Orientation="Horizontal">
            <RadioButton x:Name="RadioButton_GameName"
                         Content="{Binding Name}"
                         Tag="{Binding Tag}"
                         IsChecked="{Binding IsSelected, Mode=TwoWay}"
                         HorizontalAlignment="Left"
                         VerticalAlignment="Center"
                         FontSize="12"
                         Margin="5"
                         Click="RadioButton_GameName_Click"/>
            <TextBlock x:Name="TextBlock_ConnectState"
                       Text="{Binding StateText}"
                       Foreground="{Binding StateColor}"
                       VerticalAlignment="Center"
                       Margin="5,0,0,0" />
        </StackPanel>
    </Grid>
</UserControl>
