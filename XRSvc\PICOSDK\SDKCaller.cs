﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Jskj.AppLog;
using PICOSDK;
using System.Threading.Tasks; // Added for Task.Run

/// <summary>
/// PICO SDK调用器，提供对PICO XR设备SDK的封装
/// </summary>
public class SDKCaller
{
    /// <summary>
    /// SDK回调委托
    /// </summary>
    private PXREAOpenApi.SDKCallBack sdkcb;

    /// <summary>
    /// SDK是否已初始化
    /// </summary>
    private bool _isInitialized = false;

    /// <summary>
    /// 构造函数，初始化SDK回调
    /// </summary>
    public SDKCaller()
    {
        sdkcb = new PXREAOpenApi.SDKCallBack(Callback);
    }

    /// <summary>
    /// 初始化SDK
    /// </summary>
    /// <returns>初始化结果，0表示成功</returns>
    public int initSDK()
    {
        try
        {
            Log.Write(Level.INFO, "开始初始化PICO SDK...");
            
            // 检查SDK DLL是否存在
            string dllPath = @"C:\Program Files (x86)\PICO Business Suite\SDK\clientdll\64\PXREAClientSDK.dll";
            if (!System.IO.File.Exists(dllPath))
            {
                Log.Write(Level.ERROR, $"PICO SDK DLL不存在: {dllPath}");
                return -1;
            }

            int result = PXREAOpenApi.Init(IntPtr.Zero, sdkcb, (uint)PXREAOpenApiCallbackType.PXREAFullMask);
            
            if (result == 0)
            {
                _isInitialized = true;
                Log.Write(Level.INFO, "PICO SDK初始化成功");
            }
            else
            {
                Log.Write(Level.ERROR, $"PICO SDK初始化失败，错误码: {result}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"PICO SDK初始化异常: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 反初始化SDK
    /// </summary>
    public void deinitSDK()
    {
        try
        {
            if (_isInitialized)
            {
                PXREAOpenApi.Deinit();
                _isInitialized = false;
                Log.Write(Level.INFO, "PICO SDK反初始化完成");
            }
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"PICO SDK反初始化异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查SDK是否已初始化
    /// </summary>
    /// <returns>是否已初始化</returns>
    public bool IsInitialized()
    {
        return _isInitialized;
    }

    /// <summary>
    /// 获取设备电池信息
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int getBattery(string devID)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法获取电池信息");
            return -1;
        }

        try
        {
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            int result = PXREAOpenApi.GetBattery(szDevID);
            Marshal.FreeHGlobal(szDevID);
            
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"获取电池信息异常 - 设备: {devID}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 开始监控设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int startMonitor(string devID)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法开始监控");
            return -1;
        }

        try
        {
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            int result = PXREAOpenApi.StartMonitor(szDevID);
            Marshal.FreeHGlobal(szDevID);
            
            Log.Write(Level.INFO, $"开始监控设备 - 设备: {devID}, 结果: {result}");
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"开始监控设备异常 - 设备: {devID}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 停止监控设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int stopMonitor(string devID)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法停止监控");
            return -1;
        }

        try
        {
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            int result = PXREAOpenApi.StopMonitor(szDevID);
            Marshal.FreeHGlobal(szDevID);
            
            Log.Write(Level.INFO, $"停止监控设备 - 设备: {devID}, 结果: {result}");
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"停止监控设备异常 - 设备: {devID}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 启动应用
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <param name="packageName">应用包名</param>
    /// <returns>操作结果，0表示成功</returns>
    public int startPlayApp(string devID, string packageName)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法启动应用");
            return -1;
        }

        if (string.IsNullOrEmpty(devID))
        {
            Log.Write(Level.ERROR, "设备ID不能为空");
            return -1;
        }

        if (string.IsNullOrEmpty(packageName))
        {
            Log.Write(Level.ERROR, "包名不能为空");
            return -1;
        }

        try
        {
            Log.Write(Level.INFO, $"准备启动应用 - 设备: {devID}, 包名: {packageName}");
            
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            IntPtr szPackageName = (IntPtr)Marshal.StringToHGlobalAnsi(packageName);
            
            int result = PXREAOpenApi.StartPlayApp(szDevID, szPackageName);
            
            Marshal.FreeHGlobal(szDevID);
            Marshal.FreeHGlobal(szPackageName);
            
            if (result == 0)
            {
                Log.Write(Level.INFO, $"启动应用成功 - 设备: {devID}, 包名: {packageName}");
            }
            else
            {
                Log.Write(Level.ERROR, $"启动应用失败 - 设备: {devID}, 包名: {packageName}, 错误码: {result}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"启动应用异常 - 设备: {devID}, 包名: {packageName}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 停止应用
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <param name="packageName">应用包名</param>
    /// <returns>操作结果，0表示成功</returns>
    public int stopPlayApp(string devID, string packageName)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法停止应用");
            return -1;
        }

        if (string.IsNullOrEmpty(devID))
        {
            Log.Write(Level.ERROR, "设备ID不能为空");
            return -1;
        }

        if (string.IsNullOrEmpty(packageName))
        {
            Log.Write(Level.ERROR, "包名不能为空");
            return -1;
        }

        try
        {
            Log.Write(Level.INFO, $"准备停止应用 - 设备: {devID}, 包名: {packageName}");
            
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            IntPtr szPackageName = (IntPtr)Marshal.StringToHGlobalAnsi(packageName);
            int result = PXREAOpenApi.StopPlayApp(szDevID, szPackageName);
            Marshal.FreeHGlobal(szDevID);
            Marshal.FreeHGlobal(szPackageName);
            
            if (result == 0)
            {
                Log.Write(Level.INFO, $"停止应用成功 - 设备: {devID}, 包名: {packageName}");
            }
            else
            {
                Log.Write(Level.ERROR, $"停止应用失败 - 设备: {devID}, 包名: {packageName}, 错误码: {result}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"停止应用异常 - 设备: {devID}, 包名: {packageName}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 锁定设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int lockDevice(string devID)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法锁定设备");
            return -1;
        }

        if (string.IsNullOrEmpty(devID))
        {
            Log.Write(Level.ERROR, "设备ID不能为空");
            return -1;
        }

        try
        {
            Log.Write(Level.INFO, $"准备锁定设备 - 设备: {devID}");
            
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            int result = PXREAOpenApi.LockDevice(szDevID);
            Marshal.FreeHGlobal(szDevID);
            
            if (result == 0)
            {
                Log.Write(Level.INFO, $"锁定设备成功 - 设备: {devID}");
            }
            else
            {
                Log.Write(Level.ERROR, $"锁定设备失败 - 设备: {devID}, 错误码: {result}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"锁定设备异常 - 设备: {devID}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 解锁设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int unlockDevice(string devID)
    {
        if (!_isInitialized)
        {
            Log.Write(Level.ERROR, "SDK未初始化，无法解锁设备");
            return -1;
        }

        if (string.IsNullOrEmpty(devID))
        {
            Log.Write(Level.ERROR, "设备ID不能为空");
            return -1;
        }

        try
        {
            Log.Write(Level.INFO, $"准备解锁设备 - 设备: {devID}");
            
            IntPtr szDevID = (IntPtr)Marshal.StringToHGlobalAnsi(devID);
            int result = PXREAOpenApi.UnlockDevice(szDevID);
            Marshal.FreeHGlobal(szDevID);
            
            if (result == 0)
            {
                Log.Write(Level.INFO, $"解锁设备成功 - 设备: {devID}");
            }
            else
            {
                Log.Write(Level.ERROR, $"解锁设备失败 - 设备: {devID}, 错误码: {result}");
            }
            
            return result;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"解锁设备异常 - 设备: {devID}, 错误: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 设备发现事件
    /// </summary>
    public event EventHandler<PICOSDK.DeviceEventArgs> DeviceFound;
    /// <summary>
    /// 设备丢失事件
    /// </summary>
    public event EventHandler<PICOSDK.DeviceEventArgs> DeviceLost;
    /// <summary>
    /// 电池电量变化事件
    /// </summary>
    public event EventHandler<PICOSDK.DeviceEventArgs> BatteryChanged;
    /// <summary>
    /// 监控帧数据事件
    /// </summary>
    public event EventHandler<PICOSDK.DeviceEventArgs> FrameReceived;
    /// <summary>
    /// 设备锁定事件
    /// </summary>
    public event EventHandler<PICOSDK.DeviceEventArgs> DeviceLocked;
    /// <summary>
    /// 设备解锁事件
    /// </summary>
    public event EventHandler<PICOSDK.DeviceEventArgs> DeviceUnlocked;

    /// <summary>
    /// SDK回调处理函数
    /// </summary>
    /// <param name="context">上下文</param>
    /// <param name="type">回调类型</param>
    /// <param name="status">状态</param>
    /// <param name="userData">用户数据</param>
    public void Callback(IntPtr context, PXREAOpenApiCallbackType type, int status, IntPtr userData)
    {
        // 对于电池回调不打印日志，减少冗余输出
        if (type != PXREAOpenApiCallbackType.PXREADeviceBattery)
        {
            Log.Write(Level.INFO, $"PICO SDK回调 - 类型: {type}, 状态: {status}");
        }
        
        try
        {
            switch(type)
            {
                case PXREAOpenApiCallbackType.PXREADeviceFind:
                    string foundDevID = Marshal.PtrToStringAnsi(userData);
                    DeviceFound?.Invoke(this, new PICOSDK.DeviceEventArgs { DevID = foundDevID });
                    Log.Write(Level.INFO, $"发现设备: {foundDevID}");
                    
                    // 发现设备后自动锁定设备
                    Task.Run(() => AutoLockDevice(foundDevID));
                    break;
                    
                case PXREAOpenApiCallbackType.PXREADeviceMissing:
                    string lostDevID = Marshal.PtrToStringAnsi(userData);
                    DeviceLost?.Invoke(this, new PICOSDK.DeviceEventArgs { DevID = lostDevID });
                    Log.Write(Level.INFO, $"设备丢失: {lostDevID}");
                    break;
                    
                case PXREAOpenApiCallbackType.PXREADeviceBattery:
                    PXREADevBattery battery = (PXREADevBattery)Marshal.PtrToStructure(userData, typeof(PXREADevBattery));
                    BatteryChanged?.Invoke(this, new PICOSDK.DeviceEventArgs { DevID = battery.devID, Battery = battery.battery });
                    break;
                    
                case PXREAOpenApiCallbackType.PXREADeviceMonitor:
                    PXREAFrameBlob frame = (PXREAFrameBlob)Marshal.PtrToStructure(userData, typeof(PXREAFrameBlob));
                    FrameReceived?.Invoke(this, new PICOSDK.DeviceEventArgs { DevID = frame.devID, Frame = frame });
                    Log.Write(Level.INFO, $"监控帧数据 - 设备: {frame.devID}, 大小: {frame.size}, 格式: {frame.pixFormat}");
                    break;
                    
                case PXREAOpenApiCallbackType.PXREADeviceLock:
                    string lockedDevID = Marshal.PtrToStringAnsi(userData);
                    DeviceLocked?.Invoke(this, new PICOSDK.DeviceEventArgs { DevID = lockedDevID });
                    Log.Write(Level.INFO, $"设备已锁定: {lockedDevID}");
                    break;
                    
                case PXREAOpenApiCallbackType.PXREADeviceUnlock:
                    string unlockedDevID = Marshal.PtrToStringAnsi(userData);
                    DeviceUnlocked?.Invoke(this, new PICOSDK.DeviceEventArgs { DevID = unlockedDevID });
                    Log.Write(Level.INFO, $"设备已解锁: {unlockedDevID}");
                    break;
                    
                case PXREAOpenApiCallbackType.PXREACurrentApplication:
                    string appInfo = Marshal.PtrToStringAnsi(userData);
                    Log.Write(Level.INFO, $"当前应用信息: {appInfo}");
                    break;
                    
                default:
                    Log.Write(Level.INFO, $"未处理的回调类型: {type}");
                    break;
            }
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"SDK回调处理异常 - 类型: {type}, 错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 自动锁定设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    private void AutoLockDevice(string devID)
    {
        try
        {
            // 延迟1秒后锁定设备，确保设备完全连接
            System.Threading.Thread.Sleep(1000);
            
            Log.Write(Level.INFO, $"自动锁定设备: {devID}");
            int result = lockDevice(devID);
            
            if (result == 0)
            {
                Log.Write(Level.INFO, $"自动锁定设备成功: {devID}");
            }
            else
            {
                Log.Write(Level.ERROR, $"自动锁定设备失败: {devID}, 错误码: {result}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"自动锁定设备异常: {devID}, 错误: {ex.Message}");
        }
    }
}
