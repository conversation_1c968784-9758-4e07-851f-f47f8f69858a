using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Jskj.AppLog;

namespace XRSvc.Utils
{
    /// <summary>
    /// 统一的UI线程调度器，提供高效的线程切换和批量处理机制
    /// </summary>
    public class UIThreadScheduler
    {
        private static readonly Lazy<UIThreadScheduler> _instance = new Lazy<UIThreadScheduler>(() => new UIThreadScheduler());
        public static UIThreadScheduler Instance => _instance.Value;

        private readonly Dispatcher _dispatcher;
        private readonly ConcurrentQueue<UIAction> _pendingActions;
        private readonly Timer _batchTimer;
        private readonly object _batchLock = new object();
        private volatile bool _isBatchProcessing = false;
        // 移除性能监控器依赖，简化实现

        // 批处理配置
        private const int BATCH_INTERVAL_MS = 16; // 约60FPS的更新频率
        private const int MAX_BATCH_SIZE = 50;    // 每批最大处理数量

        private UIThreadScheduler()
        {
            _dispatcher = Application.Current?.Dispatcher ?? Dispatcher.CurrentDispatcher;
            _pendingActions = new ConcurrentQueue<UIAction>();

            // 创建批处理定时器
            _batchTimer = new Timer(ProcessBatch, null, BATCH_INTERVAL_MS, BATCH_INTERVAL_MS);

            Log.Write(Level.INFO, "UI线程调度器已初始化");
        }

        /// <summary>
        /// 检查当前是否在UI线程
        /// </summary>
        public bool IsOnUIThread => _dispatcher.CheckAccess();

        /// <summary>
        /// 在UI线程上执行操作（同步）
        /// </summary>
        public void InvokeOnUIThread(Action action, DispatcherPriority priority = DispatcherPriority.Normal)
        {
            if (action == null) return;

            if (IsOnUIThread)
            {
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"UI线程操作执行失败: {ex.Message}");
                }
            }
            else
            {
                _dispatcher.Invoke(action, priority);
            }
        }

        /// <summary>
        /// 在UI线程上异步执行操作
        /// </summary>
        public async Task InvokeOnUIThreadAsync(Action action, DispatcherPriority priority = DispatcherPriority.Normal)
        {
            if (action == null) return;

            if (IsOnUIThread)
            {
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"UI线程异步操作执行失败: {ex.Message}");
                }
            }
            else
            {
                await _dispatcher.InvokeAsync(action, priority);
            }
        }

        /// <summary>
        /// 在UI线程上异步执行操作并返回结果
        /// </summary>
        public async Task<T> InvokeOnUIThreadAsync<T>(Func<T> func, DispatcherPriority priority = DispatcherPriority.Normal)
        {
            if (func == null) return default(T);

            if (IsOnUIThread)
            {
                try
                {
                    return func();
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"UI线程异步操作执行失败: {ex.Message}");
                    return default(T);
                }
            }
            else
            {
                return await _dispatcher.InvokeAsync(func, priority);
            }
        }

        /// <summary>
        /// 批量执行UI操作（推荐用于频繁的UI更新）
        /// </summary>
        public void BatchInvokeOnUIThread(Action action, string actionId = null, DispatcherPriority priority = DispatcherPriority.Normal)
        {
            if (action == null) return;

            var uiAction = new UIAction
            {
                Action = action,
                ActionId = actionId ?? Guid.NewGuid().ToString(),
                Priority = priority,
                Timestamp = DateTime.Now
            };

            _pendingActions.Enqueue(uiAction);
        }

        /// <summary>
        /// 立即处理所有待处理的批量操作
        /// </summary>
        public async Task FlushBatchAsync()
        {
            if (_isBatchProcessing) return;

            await Task.Run(() => ProcessBatch(null));
        }

        /// <summary>
        /// 处理批量操作
        /// </summary>
        private void ProcessBatch(object state)
        {
            if (_isBatchProcessing || _pendingActions.IsEmpty) return;

            lock (_batchLock)
            {
                if (_isBatchProcessing) return;
                _isBatchProcessing = true;
            }

            try
            {
                var actions = new List<UIAction>();
                var processedIds = new HashSet<string>();

                // 收集待处理的操作，去重相同ID的操作
                int count = 0;
                while (count < MAX_BATCH_SIZE && _pendingActions.TryDequeue(out UIAction action))
                {
                    if (string.IsNullOrEmpty(action.ActionId) || processedIds.Add(action.ActionId))
                    {
                        actions.Add(action);
                        count++;
                    }
                }

                if (actions.Count > 0)
                {
                    // 按优先级分组执行
                    var groupedActions = new Dictionary<DispatcherPriority, List<Action>>();
                    
                    foreach (var action in actions)
                    {
                        if (!groupedActions.ContainsKey(action.Priority))
                        {
                            groupedActions[action.Priority] = new List<Action>();
                        }
                        groupedActions[action.Priority].Add(action.Action);
                    }

                    // 按优先级顺序执行
                    foreach (var group in groupedActions)
                    {
                        var priority = group.Key;
                        var actionList = group.Value;

                        if (IsOnUIThread)
                        {
                            ExecuteActions(actionList);
                        }
                        else
                        {
                            _dispatcher.InvokeAsync(() => ExecuteActions(actionList), priority);
                        }
                    }

                    Log.Write(Level.DEBUG, $"批量处理了 {actions.Count} 个UI操作");
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"批量处理UI操作失败: {ex.Message}");
            }
            finally
            {
                _isBatchProcessing = false;
            }
        }

        /// <summary>
        /// 执行操作列表
        /// </summary>
        private void ExecuteActions(List<Action> actions)
        {
            foreach (var action in actions)
            {
                try
                {
                    action();
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"批量UI操作执行失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _batchTimer?.Dispose();
            
            // 处理剩余的操作
            ProcessBatch(null);
            
            Log.Write(Level.INFO, "UI线程调度器已释放");
        }
    }

    /// <summary>
    /// UI操作包装类
    /// </summary>
    internal class UIAction
    {
        public Action Action { get; set; }
        public string ActionId { get; set; }
        public DispatcherPriority Priority { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
