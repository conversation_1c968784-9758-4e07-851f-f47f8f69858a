package com.gzcec.xrandroidclient.communication.messages.device;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;

/**
 * 设备列表请求消息
 */
public class DeviceListRequestMessage extends BaseMessage {
    private String requestId;
    private boolean onlineOnly;
    private String deviceTypeFilter;

    public DeviceListRequestMessage() {
        setType(MessageType.DEVICE_LIST_REQUEST);
    }

    // Getter和Setter方法
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }

    public boolean isOnlineOnly() { return onlineOnly; }
    public void setOnlineOnly(boolean onlineOnly) { this.onlineOnly = onlineOnly; }

    public String getDeviceTypeFilter() { return deviceTypeFilter; }
    public void setDeviceTypeFilter(String deviceTypeFilter) { this.deviceTypeFilter = deviceTypeFilter; }
}
