package com.gzcec.xrandroidclient;

import java.util.ArrayList;
import java.util.List;
import com.gzcec.xrandroidclient.device.DeviceType;

/**
 * 设备ID管理类，统一管理所有设备ID和服务器分组
 */
public class DeviceIdManager {
    // 服务器前缀常量
    public static final int SERVER_80 = 80;
    public static final int SERVER_81 = 81;

    /**
     * 获取所有设备ID列表
     * @return 包含8001-8015和8101-8115的完整设备ID列表
     */
    public static List<Integer> getAllDeviceIds() {
        List<Integer> ids = new ArrayList<>();
        // 添加80服务器设备ID (8001-8015)
        for (int i = 1; i <= 15; i++) {
            ids.add(SERVER_80 * 100 + i); // 80 * 100 + i = 8000 + i
        }
        // 添加81服务器设备ID (8101-8115)
        for (int i = 1; i <= 15; i++) {
            ids.add(SERVER_81 * 100 + i); // 81 * 100 + i = 8100 + i
        }
        return ids;
    }

    /**
     * 根据服务器前缀获取设备ID列表
     * @param serverPrefix 服务器前缀 (80或81)
     * @return 对应服务器的设备ID列表
     */
    public static List<Integer> getDeviceIdsByServer(int serverPrefix) {
        List<Integer> ids = new ArrayList<>();
        int start = (serverPrefix == SERVER_80) ? 8001 : 8101;
        int end = (serverPrefix == SERVER_80) ? 8015 : 8115;
        
        for (int i = start; i <= end; i++) {
            ids.add(i);
        }
        return ids;
    }

    /**
     * 根据设备ID生成设备名称（几号机）
     * @param deviceId 设备ID，如8001
     * @return 设备名称，如"1号机"
     */
    public static String getDeviceName(int deviceId) {
        // 提取ID后两位作为设备编号
        int deviceNumber = deviceId % 100;
        return deviceNumber + "号机";
    }

    /**
     * 根据设备ID获取服务器前缀
     * @param deviceId 设备ID
     * @return 服务器前缀 (80或81)
     */
    public static int getServerPrefix(int deviceId) {
        return deviceId / 100;
    }
}