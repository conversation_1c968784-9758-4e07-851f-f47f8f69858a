package com.gzcec.xrandroidclient.gameprogress;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;
import android.widget.ImageView;
import android.util.Log;

import com.gzcec.xrandroidclient.BatteryView;
import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.data.DeviceDataRepository;
import com.gzcec.xrandroidclient.R;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class GameProgressDeviceAdapter extends RecyclerView.Adapter<GameProgressDeviceAdapter.DeviceViewHolder> {
    private List<DeviceInfo> deviceList;
    private DeviceDataRepository deviceRepository;

    public GameProgressDeviceAdapter(List<DeviceInfo> deviceList) {
        this.deviceList = deviceList;
        this.deviceRepository = DeviceDataRepository.getInstance();
    }

    public interface OnSelectionChangedListener {
        void onSelectionChanged();
    }

    public interface OnGameControlListener {
        void onJoinGame(DeviceInfo device);
        void onExitGame(DeviceInfo device);
    }

    private OnSelectionChangedListener selectionChangedListener;
    private OnGameControlListener gameControlListener;

    public void setOnSelectionChangedListener(OnSelectionChangedListener listener) {
        this.selectionChangedListener = listener;
    }

    public void setOnGameControlListener(OnGameControlListener listener) {
        this.gameControlListener = listener;
    }
    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.gameprogress_device_card, parent, false);
        return new DeviceViewHolder(view);
    }
    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        DeviceInfo device = deviceList.get(position);
        holder.checkboxSelect.setOnCheckedChangeListener(null);
        holder.checkboxSelect.setChecked(device.isSelected());
        holder.checkboxSelect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // 先更新本地设备状态，确保立即生效
            device.setSelected(isChecked);

            // 立即通知监听器更新按钮文字
            if (selectionChangedListener != null) {
                selectionChangedListener.onSelectionChanged();
            }

            // 然后更新数据仓库中的设备选择状态（异步）
            deviceRepository.updateDeviceSelection(device.getId(), isChecked);
        });
        holder.checkboxSelect.setText(device.getName()); // 用CheckBox显示设备名
        holder.tvDeviceId.setText("(" + device.getId() + ")"); // 显示设备ID而不是序列号
        holder.tvStatus.setText(device.isOnline() ? holder.itemView.getContext().getString(R.string.label_online) : holder.itemView.getContext().getString(R.string.label_status));
        holder.tvStatus.setTextColor(device.isOnline() ? 0xFF00FF00 : 0xFFFF5252); // 在线绿色

        // 设备离线时显示电量为0%
        String batteryText = device.isOnline() ? device.getBattery() : "0%";
        holder.tvBattery.setText(batteryText);
        holder.tvBattery.setTextColor(device.isOnline() ? 0xFF00FF00 : 0xFFFF5252); // 电量绿色

        // 根据设备状态显示游戏状态文字
        String gameStatusText;
        int gameStatusColor;

        if (!device.isOnline()) {
            gameStatusText = "未连接游戏服务器";
            gameStatusColor = 0xFF808080; // 灰色
        } else if (device.isGameStarting()) {
            gameStatusText = "游戏启动中";
            gameStatusColor = 0xFFFFA500; // 橙色
        } else if (device.isInGame()) {
            gameStatusText = "游戏已启动";
            gameStatusColor = 0xFF00FF00; // 绿色
        } else {
            gameStatusText = "游戏未启动";
            gameStatusColor = 0xFFFF5252; // 红色
        }

        holder.tvGameStatus.setText(gameStatusText);
        holder.tvGameStatus.setTextColor(gameStatusColor);

        // 电量格数（0-4格，百分比分档）
        int level = 0;
        double battery = device.isOnline() ? device.getBatteryLevel() : 0; // 离线设备电量为0
        if (battery > 0 && battery <= 25) level = 1;
        else if (battery > 25 && battery <= 50) level = 2;
        else if (battery > 50 && battery <= 75) level = 3;
        else if (battery > 75) level = 4;
        Log.d("BatteryDebug", "position=" + position + ", battery=" + battery + "%, level=" + level + ", online=" + device.isOnline());
        holder.imgBattery.setLevel(level);
        // 在线点颜色
        if (device.isOnline()) {
            holder.ivHigh.setImageResource(R.drawable.ic_green_dot); // 在线绿色点
        } else {
            holder.ivHigh.setImageResource(R.drawable.ic_red_dot);
        }
        // 游戏状态图标
        holder.iv_game_status.setImageResource(device.isInGame() ? R.drawable.ic_green_dot : R.drawable.ic_red_dot);
        // 服务器状态图标
        holder.iv_server_status.setImageResource(device.isOnline() ? R.drawable.ic_green_dot : R.drawable.ic_red_dot);

        // 设置游戏控制按钮点击事件
        holder.btnJoinGame.setOnClickListener(v -> {
            if (gameControlListener != null) {
                Log.d("GameProgressAdapter", "点击加入游戏: " + device.getName() + " (ID: " + device.getId() + ")");
                gameControlListener.onJoinGame(device);
            }
        });

        holder.btnExitGame.setOnClickListener(v -> {
            if (gameControlListener != null) {
                Log.d("GameProgressAdapter", "点击退出游戏: " + device.getName() + " (ID: " + device.getId() + ")");
                gameControlListener.onExitGame(device);
            }
        });

        // 根据设备状态更新按钮可用性
        boolean isOnline = device.isOnline();
        boolean isInGame = device.isInGame();
        boolean isGameStarting = device.isGameStarting();
        boolean isGameStopping = device.isGameStopping();

        holder.btnJoinGame.setEnabled(isOnline && !isInGame && !isGameStarting && !isGameStopping);
        holder.btnExitGame.setEnabled(isOnline && isInGame && !isGameStopping);

        // 更新按钮文字和样式
        if (isGameStarting) {
            holder.btnJoinGame.setText("启动中...");
        } else if (isInGame) {
            holder.btnJoinGame.setText("游戏中");
        } else {
            holder.btnJoinGame.setText("加入游戏");
        }

        if (isGameStopping) {
            holder.btnExitGame.setText("停止中...");
        } else if (isInGame) {
            holder.btnExitGame.setText("退出游戏");
        } else {
            holder.btnExitGame.setText("未在游戏");
        }
    }
    @Override
    public int getItemCount() {
        return deviceList.size();
    }
    static class DeviceViewHolder extends RecyclerView.ViewHolder {
        CheckBox checkboxSelect;
        TextView tvDeviceId;
        TextView tvStatus, tvBattery, tvGameStatus;
        BatteryView imgBattery;
        ImageView ivHigh, iv_game_status, iv_server_status;
    Button btnJoinGame, btnExitGame;
        DeviceViewHolder(@NonNull View itemView) {
            super(itemView);
            checkboxSelect = itemView.findViewById(R.id.cb_device);
            tvDeviceId = itemView.findViewById(R.id.tv_device_id);
            tvStatus = itemView.findViewById(R.id.tv_high); // 正确绑定在线/离线
            tvBattery = itemView.findViewById(R.id.tv_battery_percent);
            tvGameStatus = itemView.findViewById(R.id.tv_game_status);
            imgBattery = (BatteryView) itemView.findViewById(R.id.battery_view);
            ivHigh = itemView.findViewById(R.id.iv_high);
            iv_game_status = itemView.findViewById(R.id.iv_game_status);
            iv_server_status = itemView.findViewById(R.id.iv_server_status);
            btnJoinGame = itemView.findViewById(R.id.btn_join);
            btnExitGame = itemView.findViewById(R.id.btn_exit);
        }
    }
}