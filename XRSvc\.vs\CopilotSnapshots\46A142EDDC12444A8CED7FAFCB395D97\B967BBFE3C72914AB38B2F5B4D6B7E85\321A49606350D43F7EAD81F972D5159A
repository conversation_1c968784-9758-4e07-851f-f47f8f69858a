﻿<UserControl x:Class="XRSvc.CustomControl.DeviceStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="140" d:DesignWidth="320">
    <UserControl.Resources>
        <!-- 卡片阴影效果 -->
        <DropShadowEffect x:Key="CardShadow" Color="Black" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
        
        <!-- 在线状态指示器样式 -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="10"/>
            <Setter Property="Height" Value="10"/>
            <Setter Property="Fill" Value="#FF6B6B6B"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsOnline}" Value="True">
                    <Setter Property="Fill" Value="#FF4CAF50"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 电池进度条样式 -->
        <Style x:Key="ModernBatteryStyle" TargetType="ProgressBar">
            <Setter Property="Background" Value="#FFE0E0E0"/>
            <Setter Property="Foreground" Value="#FF4CAF50"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ProgressBar">
                        <Grid>
                            <Rectangle Fill="{TemplateBinding Background}" />
                            <Rectangle Fill="{TemplateBinding Foreground}" 
                                       HorizontalAlignment="Left" 
                                       Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Value, Converter={StaticResource ProgressBarWidthConverter}}" />
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <DataTrigger Binding="{Binding BatteryLevel}" Value="0">
                    <Setter Property="Foreground" Value="#FFF44336"/>
                </DataTrigger>
                <MultiDataTrigger>
                    <MultiDataTrigger.Conditions>
                        <Condition Binding="{Binding BatteryLevel}" Value="0"/>
                        <Condition Binding="{Binding IsOnline}" Value="True"/>
                    </MultiDataTrigger.Conditions>
                    <Setter Property="Foreground" Value="#FFFF9800"/>
                </MultiDataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 设备类型图标样式 -->
        <Style x:Key="DeviceIconStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="Foreground" Value="#FF666666"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 主标题样式 -->
        <Style x:Key="DeviceTitleStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#FF333333"/>
            <Setter Property="Margin" Value="0,0,0,2"/>
        </Style>

        <!-- 副标题样式 -->
        <Style x:Key="DeviceSubtitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Foreground" Value="#FF888888"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <!-- 状态文本样式 -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#FF666666"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 转换器实例 -->
        <local:OnlineStatusConverter x:Key="OnlineStatusConverter"/>
    </UserControl.Resources>

    <!-- 主容器 -->
    <Border Background="White" 
            BorderBrush="#FFE0E0E0" 
            BorderThickness="1" 
            CornerRadius="8" 
            Margin="6"
            Effect="{StaticResource CardShadow}">
        
        <Grid Margin="12">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 顶部：设备图标和状态 -->
            <Grid Grid.Row="0" Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 设备图标 -->
                <Border Grid.Column="0" 
                        Background="#FFF5F5F5" 
                        CornerRadius="6" 
                        Width="32" 
                        Height="32" 
                        Margin="0,0,12,0">
                    <TextBlock Text="&#xE770;" Style="{StaticResource DeviceIconStyle}"/>
                </Border>

                <!-- 设备信息 -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                    <TextBlock Text="{Binding ID}" Style="{StaticResource DeviceTitleStyle}"/>
                    <TextBlock Text="{Binding SerialNumber}" Style="{StaticResource DeviceSubtitleStyle}"/>
                </StackPanel>

                <!-- 在线状态指示器 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Style="{StaticResource StatusIndicatorStyle}"/>
                    <TextBlock Text="{Binding IsOnline, Converter={StaticResource OnlineStatusConverter}}" 
                               Style="{StaticResource StatusTextStyle}" 
                               Margin="6,0,0,0"/>
                </StackPanel>
            </Grid>

            <!-- 中间：设备类型和详细信息 -->
            <Grid Grid.Row="1" Margin="0,4">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 设备类型信息 -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="设备类型" FontSize="10" Foreground="#FF999999" Margin="0,0,0,2"/>
                    <TextBlock Text="{Binding DeviceType}" FontSize="12" Foreground="#FF555555"/>
                </StackPanel>

                <!-- 连接信息 -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                    <TextBlock Text="连接状态" FontSize="10" Foreground="#FF999999" Margin="0,0,0,2"/>
                    <TextBlock Text="{Binding ConnectionStatus}" FontSize="12" Foreground="#FF555555"/>
                </StackPanel>
            </Grid>

            <!-- 底部：电池状态 -->
            <Grid Grid.Row="2" Margin="0,8,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 电池图标 -->
                <TextBlock Grid.Column="0" 
                           Text="&#xE996;" 
                           FontFamily="Segoe MDL2 Assets" 
                           FontSize="14" 
                           Foreground="#FF666666" 
                           VerticalAlignment="Center"
                           Margin="0,0,8,0"/>

                <!-- 电池进度条 -->
                <ProgressBar Grid.Column="1" 
                             Height="6" 
                             Value="{Binding BatteryLevel}" 
                             Maximum="100" 
                             Style="{StaticResource ModernBatteryStyle}"
                             VerticalAlignment="Center"/>

                <!-- 电池百分比 -->
                <TextBlock Grid.Column="2" 
                           Text="{Binding BatteryLevel, StringFormat={}{0}%}" 
                           Style="{StaticResource StatusTextStyle}" 
                           Margin="8,0,0,0"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>