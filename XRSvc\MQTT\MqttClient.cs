using MQTTnet;
using MQTTnet.Client;
using System;
using System.Text;
using System.Threading.Tasks;
using Jskj.AppLog;
using MQTTnet.Server;
using MQTTnet.Protocol;
using System.Collections.Generic;
using System.Threading;
using System.Timers;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using XRSvc.Communication;
using XRSvc.Services;
using CommDeviceStopResult = XRSvc.Communication.DeviceStopResult;
using XRSvc.ViewModels;
using System.Linq;
using XRSvc.DataPack;
using System.Windows;
using XRSvc.DataSource;
using XRSvc.Utils;

namespace Jskj.XRSvc.MQTT
{
    /// <summary>
    /// 增强的MQTT客户端服务，支持游戏控制通信
    /// </summary>
    public class MqttClientService
    {
        #region Fields

        /// <summary>
        /// JSON序列化设置 - 使用小写开头的命名策略
        /// </summary>
        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            Formatting = Formatting.None,
            DateTimeZoneHandling = DateTimeZoneHandling.Local // 新增，确保带时区
        };

        private IMqttClient mqttClient;
        private MqttClientOptions options;
        private readonly GameLaunchService _gameLaunchService;
        private readonly DeviceViewModel _deviceViewModel;
        private readonly GameViewModel _gameViewModel;

        // 自动设备检测相关
        private System.Threading.Timer _devicePingTimer;
        private readonly int _pingIntervalMinutes = 1; // 每1分钟检测一次
        private readonly string _clientId;
        private bool _isConnected = false;
        private readonly UIThreadScheduler _uiScheduler;

        #endregion

        #region Events

        // 注意：GameStartRequested 和 DeviceListRequested 事件已移除
        // 现在直接在消息处理方法中处理请求，不再使用事件模式

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;

        #endregion

        public MqttClientService(string brokerHost = "127.0.0.1", int brokerPort = 1883,
            GameLaunchService gameLaunchService = null, DeviceViewModel deviceViewModel = null, GameViewModel gameViewModel = null)
        {
            _clientId = $"PCServer_{Environment.MachineName}_{DateTime.Now.Ticks}";
            _gameLaunchService = gameLaunchService;
            _deviceViewModel = deviceViewModel;
            _gameViewModel = gameViewModel;
            _uiScheduler = UIThreadScheduler.Instance;

            // 版本标识 - 确认运行的是优化UI线程版本
            Log.Write(Level.INFO, "=== MqttClientService 初始化 - 版本: 优化UI线程版本 v3.0 ===");

            mqttClient = new MqttFactory().CreateMqttClient();
            options = new MqttClientOptionsBuilder()
                .WithTcpServer(brokerHost, brokerPort)
                .WithClientId(_clientId)
                .WithCleanSession(true)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(30))
                .Build();
        }

        /// <summary>
        /// 连接到MQTT Broker并订阅相关主题
        /// </summary>
        public async Task ConnectAsync()
        {
            mqttClient.ConnectedAsync += async e =>
            {
                _isConnected = true;
                Log.Write(Level.INFO, $"MQTT客户端已连接: {_clientId}");

                // 订阅PC端需要的主题
                await SubscribeToTopicsAsync();

                // 发布连接状态
                await PublishConnectionStatusAsync(true);

                // 启动自动设备检测
                StartAutomaticDevicePing();

                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusEventArgs(true, "已连接到MQTT Broker"));
            };

            mqttClient.DisconnectedAsync += async e =>
            {
                _isConnected = false;
                Log.Write(Level.INFO, $"MQTT客户端已断开: {_clientId}");

                // 停止自动设备检测
                StopAutomaticDevicePing();

                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusEventArgs(false, "与MQTT Broker断开连接"));
                await Task.CompletedTask;
            };

            mqttClient.ApplicationMessageReceivedAsync += async e =>
            {
                Log.Write(Level.DEBUG, $"PC端收到MQTT消息: 主题={e.ApplicationMessage.Topic}");
                await HandleReceivedMessageAsync(e.ApplicationMessage);
            };

            await mqttClient.ConnectAsync(options);
        }

        /// <summary>
        /// 订阅PC端需要的主题
        /// </summary>
        private async Task SubscribeToTopicsAsync()
        {
            var topics = MqttTopics.GetPCSubscriptionTopics();
            foreach (var topic in topics)
            {
                await SubscribeAsync(topic);
            }
        }

        /// <summary>
        /// 处理接收到的MQTT消息
        /// </summary>
        private async Task HandleReceivedMessageAsync(MqttApplicationMessage message)
        {
            try
            {
                var topic = message.Topic;
                var payload = Encoding.UTF8.GetString(
                    message.PayloadSegment.Array,
                    message.PayloadSegment.Offset,
                    message.PayloadSegment.Count);

                Log.Write(Level.DEBUG, $"收到MQTT消息: 主题={topic}, 内容长度={payload.Length}");

                // 根据主题分发消息
                switch (topic)
                {
                    case MqttTopics.GAME_LIST_REQUEST:
                        await HandleGameListRequestAsync(payload);
                        break;

                    case MqttTopics.GAME_START_REQUEST:
                        await HandleGameStartRequestAsync(payload);
                        break;

                    case MqttTopics.GAME_STOP_REQUEST:
                        await HandleGameStopRequestAsync(payload);
                        break;

                    case MqttTopics.DEVICE_LIST_REQUEST:
                        await HandleDeviceListRequestAsync(payload);
                        break;

                    case MqttTopics.DEVICE_SELECTION_CHANGED:
                        await HandleDeviceSelectionChangedAsync(payload);
                        break;

                    case MqttTopics.HEARTBEAT:
                        await HandleHeartbeatAsync(payload);
                        break;

                    case MqttTopics.CONNECTION_STATUS:
                        await HandleConnectionStatusAsync(payload);
                        break;

                    case MqttTopics.DEVICE_STATUS_UPDATE:
                        await HandleDeviceStatusUpdateAsync(payload);
                        break;

                    case MqttTopics.DEVICE_CONTROL_REQUEST:
                        await HandleDeviceControlRequestAsync(payload);
                        break;

                    default:
                        Log.Write(Level.INFO, $"未处理的MQTT主题: {topic}");
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理MQTT消息失败: {ex.Message}");
            }
        }

        public async Task SubscribeAsync(string topic)
        {
            var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                .WithTopicFilter(f => { f.WithTopic(topic); })
                .Build();
            await mqttClient.SubscribeAsync(subscribeOptions);
            Log.Write(Level.INFO, $"Subscribed to topic: {topic}");
        }

        public async Task PublishAsync(string topic, string payload)
        {
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(payload)
                .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.ExactlyOnce)
                .WithRetainFlag(false)
                .Build();
            await mqttClient.PublishAsync(message);

            // 对设备相关的大消息进行简化日志输出
            if (topic == "xr_system/device_management/status_update" ||
                topic == "xr_system/device_management/list_response")
            {
                Log.Write(Level.INFO, $"Published message: Topic={topic}, Payload长度={payload.Length}字符");
            }
            else
            {
                Log.Write(Level.INFO, $"Published message: Topic={topic}, Payload={payload}");
            }
        }

        /// <summary>
        /// 处理游戏启动请求
        /// </summary>
        private async Task HandleGameStartRequestAsync(string payload)
        {
            try
            {
                var request = JsonConvert.DeserializeObject<GameStartRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到游戏启动请求: 游戏={request.SelectedGame?.Name}, 设备数量={request.SelectedDeviceIds?.Count}");
                Log.Write(Level.INFO, "=== 使用简化的直接PICO SDK调用方式 ===");

                // 直接处理游戏启动，不依赖UI层
                var results = await LaunchGameDirectlyViaPicoSDK(request);

                // 发布响应
                bool success = results.All(r => r.Success);
                string errorMessage = success ? null : string.Join("; ", results.Where(r => !r.Success).Select(r => r.ErrorMessage));

                await PublishSimpleGameStartResponseAsync(request.MessageId, success, errorMessage);

            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理游戏启动请求失败: {ex.Message}");
                // 发送错误响应
                try
                {
                    var request = JsonConvert.DeserializeObject<GameStartRequestMessage>(payload);
                    await PublishGameStartResponseAsync(request?.MessageId ?? Guid.NewGuid().ToString(), false, $"处理请求时发生错误: {ex.Message}", null);
                }
                catch
                {
                    // 忽略响应发送失败
                }
            }
        }

        /// <summary>
        /// 处理设备列表请求
        /// </summary>
        private async Task HandleDeviceListRequestAsync(string payload)
        {
            try
            {
                Log.Write(Level.DEBUG, $"开始处理设备列表请求，消息内容: {payload}");
                var request = JsonConvert.DeserializeObject<DeviceListRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到设备列表请求，消息ID: {request?.MessageId}, 发送者: {request?.SenderId}");

                if (_deviceViewModel != null)
                {
                    // 使用Dispatcher在UI线程中访问DeviceViewModel
                    List<DeviceInfo> devices = null;
                    if (Application.Current?.Dispatcher != null)
                    {
                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            try
                            {
                                devices = _deviceViewModel.Source
                                    .Where(d => !request.OnlineOnly || d.IsOnline)
                                    .Select(d => new DeviceInfo
                                    {
                                        ID = d.ID,
                                        SerialNumber = d.SerialNumber,
                                        Name = d.Name,
                                        IpAddress = d.IP,
                                        IsOnline = d.IsOnline,
                                        BatteryLevel = (int)d.BatteryLevel,
                                        IsInGame = d.IsGameRunning,
                                        CurrentGamePackage = d.CurrentGamePackageName,
                                        DeviceType = d.Type,
                                        IsGameStarting = d.IsGameStarting,
                                        LastUpdated = DateTime.Now
                                    })
                                   .ToList();
                            }
                            catch (Exception ex)
                            {
                                Log.Write(Level.ERROR, $"在UI线程中获取设备列表失败: {ex.Message}");
                                devices = new List<DeviceInfo>();
                            }
                        });
                    }
                    else
                    {
                        Log.Write(Level.ERROR, "Application.Current.Dispatcher 不可用");
                        devices = new List<DeviceInfo>();
                    }

                    await PublishDeviceListResponseAsync(request.MessageId, devices ?? new List<DeviceInfo>());
                }
                else
                {
                    await PublishDeviceListResponseAsync(request.MessageId, new List<DeviceInfo>());
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理设备列表请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理游戏列表请求
        /// </summary>
        private async Task HandleGameListRequestAsync(string payload)
        {
            try
            {
                Log.Write(Level.DEBUG, $"开始处理游戏列表请求，消息内容: {payload}");
                var request = JsonConvert.DeserializeObject<GameListRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到游戏列表请求，消息ID: {request?.MessageId}, 发送者: {request?.SenderId}");

                if (_gameViewModel != null)
                {
                    // 使用优化的UI线程调度器
                    var games = await _uiScheduler.InvokeOnUIThreadAsync(() =>
                    {
                        try
                        {
                            var gameInfos = _gameViewModel.GetSourceInfos();
                            return gameInfos
                                .Where(g => request.IncludeDisabled || g.IsShow) // 根据请求过滤是否包含禁用游戏
                                .ToList();
                        }
                        catch (Exception ex)
                        {
                            Log.Write(Level.ERROR, $"在UI线程中获取游戏列表失败: {ex.Message}");
                            return new List<GameInfo>();
                        }
                    });

                    await PublishGameListResponseAsync(request.RequestId ?? request.MessageId, games ?? new List<GameInfo>(), true, null);
                }
                else
                {
                    await PublishGameListResponseAsync(request.RequestId ?? request.MessageId, new List<GameInfo>(), false, "游戏服务未初始化");
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理游戏列表请求失败: {ex.Message}");
                // 发送错误响应
                await PublishGameListResponseAsync("unknown", new List<GameInfo>(), false, ex.Message);
            }
        }

        /// <summary>
        /// 发布游戏启动响应
        /// </summary>
        private async Task PublishGameStartResponseAsync(string requestId, bool success, string errorMessage, GameLaunchResult result)
        {
            var response = new GameStartResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Success = success,
                ErrorMessage = errorMessage,
                Results = result?.SuccessDevices?.Concat(result.FailedDevices)?.Select(r => new DeviceStartResult
                {
                    DeviceId = r.DeviceId.ToString(),
                    SerialNumber = r.SerialNumber,
                    Success = r.Success,
                    ErrorMessage = r.ErrorMessage,
                    StartTime = r.LaunchTime
                }).ToList() ?? new List<DeviceStartResult>()
            };

            await PublishAsync(MqttTopics.GAME_START_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 发布游戏停止响应
        /// </summary>
        private async Task PublishGameStopResponseAsync(string requestId, bool success, string errorMessage, GameStopResult result)
        {
            var response = new GameStopResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Success = success,
                ErrorMessage = errorMessage,
                Results = result?.SuccessDevices?.Concat(result.FailedDevices)?.Select(r => new CommDeviceStopResult
                {
                    DeviceId = r.DeviceId.ToString(),
                    SerialNumber = r.SerialNumber,
                    Success = r.Success,
                    ErrorMessage = r.ErrorMessage,
                    StopTime = r.StopTime
                }).ToList() ?? new List<CommDeviceStopResult>()
            };

            await PublishAsync(MqttTopics.GAME_STOP_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 发布设备列表响应
        /// </summary>
        private async Task PublishDeviceListResponseAsync(string requestId, List<DeviceInfo> devices)
        {
            var response = new DeviceListResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Devices = devices,
                TotalCount = devices.Count,
                OnlineCount = devices.Count(d => d.IsOnline)
            };

            await PublishAsync(MqttTopics.DEVICE_LIST_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 发布游戏列表响应
        /// </summary>
        private async Task PublishGameListResponseAsync(string requestId, List<GameInfo> games, bool success, string errorMessage)
        {
            var response = new GameListResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Games = games,
                TotalCount = games.Count,
                Success = success,
                ErrorMessage = errorMessage,
                Type = "game_list_response" // 确保类型与Android端匹配
            };

            await PublishAsync(MqttTopics.GAME_LIST_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
            Log.Write(Level.INFO, $"游戏列表响应已发送: 游戏数量={games.Count}, 成功={success}, 请求ID={requestId}");
        }

        /// <summary>
        /// 发布连接状态
        /// </summary>
        private async Task PublishConnectionStatusAsync(bool isConnected)
        {
            var status = new ConnectionStatusMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _clientId,
                IsConnected = isConnected,
                Timestamp = DateTime.Now,
                ClientType = "PCServer"
            };

            await PublishAsync(MqttTopics.CONNECTION_STATUS, JsonConvert.SerializeObject(status, JsonSettings));
        }

        public async Task DisconnectAsync()
        {
            if (_isConnected)
            {
                await PublishConnectionStatusAsync(false);
            }
            await mqttClient.DisconnectAsync();
            Log.Write(Level.INFO, "MQTT Client disconnected.");
        }

        /// <summary>
        /// 处理游戏停止请求
        /// </summary>
        private async Task HandleGameStopRequestAsync(string payload)
        {
            try
            {
                var request = JsonConvert.DeserializeObject<GameStopRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到游戏停止请求: 游戏={request.SelectedGame?.Name}, 设备数量={request.SelectedDeviceIds?.Count}");
                Log.Write(Level.INFO, "=== 使用简化的直接PICO SDK停止方式 ===");

                // 直接处理游戏停止，不依赖UI层
                var results = await StopGameDirectlyViaPicoSDK(request);

                // 发布响应
                bool success = results.All(r => r.Success);
                string errorMessage = success ? null : string.Join("; ", results.Where(r => !r.Success).Select(r => r.ErrorMessage));

                await PublishSimpleGameStopResponseAsync(request.MessageId, success, errorMessage);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理游戏停止请求失败: {ex.Message}");
                // 发送错误响应
                try
                {
                    var request = JsonConvert.DeserializeObject<GameStopRequestMessage>(payload);
                    await PublishSimpleGameStopResponseAsync(request?.MessageId ?? Guid.NewGuid().ToString(), false, $"处理请求时发生错误: {ex.Message}");
                }
                catch
                {
                    // 忽略响应发送失败
                }
            }
        }

        /// <summary>
        /// 处理设备选择变更
        /// </summary>
        private async Task HandleDeviceSelectionChangedAsync(string payload)
        {
            Log.Write(Level.INFO, "收到设备选择变更");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 处理心跳消息
        /// </summary>
        private async Task HandleHeartbeatAsync(string payload)
        {
            var heartbeat = JsonConvert.DeserializeObject<HeartbeatMessage>(payload);
            Log.Write(Level.DEBUG, $"收到心跳: {heartbeat.SenderId}");

            // 回复心跳
            var response = new HeartbeatMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _clientId,
                ReceiverId = heartbeat.SenderId,
                Status = "OK",
                Info = new Dictionary<string, object>
                {
                    ["ServerTime"] = DateTime.Now,
                    ["ConnectedDevices"] = _deviceViewModel?.Source?.Count(d => d.IsOnline) ?? 0
                }
            };

            await PublishAsync(MqttTopics.HEARTBEAT, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 处理连接状态消息
        /// </summary>
        private async Task HandleConnectionStatusAsync(string payload)
        {
            try
            {
                Log.Write(Level.DEBUG, $"收到连接状态消息: {payload}");

                var connectionMessage = JsonConvert.DeserializeObject<ConnectionStatusMessage>(payload);
                if (connectionMessage != null)
                {
                    Log.Write(Level.INFO, $"客户端连接状态: {connectionMessage.SenderId}, 连接={connectionMessage.IsConnected}, 类型={connectionMessage.ClientType}");

                    // 如果是Android客户端首次连接，主动推送设备列表
                    if (connectionMessage.IsConnected &&
                        connectionMessage.ClientType == "AndroidClient" &&
                        _deviceViewModel?.Source != null)
                    {
                        Log.Write(Level.INFO, $"检测到Android客户端连接，主动推送设备列表给: {connectionMessage.SenderId}");
                        await PushDeviceListToClientAsync(connectionMessage.SenderId);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理连接状态消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 主动推送设备列表给指定客户端
        /// </summary>
        private async Task PushDeviceListToClientAsync(string clientId)
        {
            try
            {
                if (_deviceViewModel?.Source == null)
                {
                    Log.Write(Level.INFO, "设备数据源为空，无法推送设备列表");
                    return;
                }

                // 直接获取所有设备（包括离线设备）
                List<DeviceInfo> devices = _deviceViewModel.Source
                    .Select(d => new DeviceInfo
                    {
                        ID = d.ID,
                        SerialNumber = d.SerialNumber,
                        Name = d.Name,
                        IpAddress = d.IP,
                        IsOnline = d.IsOnline,
                        BatteryLevel = (int)d.BatteryLevel,
                        IsInGame = d.IsGameRunning,
                        CurrentGamePackage = d.CurrentGamePackageName,
                        DeviceType = d.Type,
                        IsGameStarting = d.IsGameStarting,
                        LastUpdated = DateTime.Now
                    })
                    .ToList();

                var response = new DeviceListResponseMessage
                {
                    MessageId = Guid.NewGuid().ToString(),
                    SenderId = _clientId,
                    ReceiverId = clientId,
                    Devices = devices,
                    TotalCount = devices.Count,
                    OnlineCount = devices.Count(d => d.IsOnline)
                };

                string jsonResponse = JsonConvert.SerializeObject(response, JsonSettings);
                await PublishAsync(MqttTopics.DEVICE_LIST_RESPONSE, jsonResponse);

                Log.Write(Level.INFO, $"主动推送设备列表完成: 总设备数={devices.Count}, 在线设备数={response.OnlineCount}, 目标客户端={clientId}");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"主动推送设备列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 广播设备状态更新
        /// </summary>
        public async Task BroadcastDeviceStatusUpdateAsync()
        {
            if (!_isConnected || _deviceViewModel == null) return;

            // 使用优化的UI线程调度器访问DeviceViewModel
            var devices = await _uiScheduler.InvokeOnUIThreadAsync(() =>
            {
                try
                {
                    return _deviceViewModel.Source.Select(d => new DeviceInfo
                    {
                        ID = d.ID,
                        SerialNumber = d.SerialNumber,
                        Name = d.Name,
                        IpAddress = d.IP,
                        IsOnline = d.IsOnline,
                        BatteryLevel = d.BatteryLevel,
                        IsInGame = d.IsGameRunning,
                        CurrentGamePackage = d.CurrentGamePackageName,
                        DeviceType = d.Type,
                        IsGameStarting = d.IsGameStarting,
                        LastUpdated = DateTime.Now
                    }).ToList();
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"在UI线程中获取设备状态失败: {ex.Message}");
                    return new List<DeviceInfo>();
                }
            });

            var update = new DeviceStatusUpdateMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _clientId,
                Devices = devices,
                Timestamp = DateTime.Now
            };

            await PublishAsync(MqttTopics.DEVICE_STATUS_UPDATE, JsonConvert.SerializeObject(update, JsonSettings));
        }

        /// <summary>
        /// 处理设备状态更新消息，只记录关键信息
        /// </summary>
        private async Task HandleDeviceStatusUpdateAsync(string payload)
        {
            try
            {
                // 反序列化payload，假设格式为{"devices":[...], ...}
                var json = Newtonsoft.Json.Linq.JObject.Parse(payload);
                var devices = json["devices"] as Newtonsoft.Json.Linq.JArray;
                int total = devices?.Count ?? 0;
                int online = 0;
                int offline = 0;
                List<string> changedDeviceIds = new List<string>();
                if (devices != null)
                {
                    foreach (var d in devices)
                    {
                        bool isOnline = d["isOnline"] != null && (bool)d["isOnline"];
                        if (isOnline) online++; else offline++;
                        // 可根据业务需求，记录变更设备ID或序列号
                        if (d["isSelected"] != null && (bool)d["isSelected"])
                        {
                            changedDeviceIds.Add(d["serialNumber"]?.ToString() ?? d["id"]?.ToString());
                        }
                    }
                }
                Log.Write(Level.INFO, $"[Broker] 设备状态批量上报: 总数={total}，在线={online}，离线={offline}，变更设备: {string.Join(",", changedDeviceIds)}");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理设备状态更新消息异常: {ex.Message}");
            }
            await Task.CompletedTask;
        }

        /// <summary>
        /// 获取连接状态
        /// </summary>
        public bool IsConnected => _isConnected;

        /// <summary>
        /// 直接启动游戏，避免线程切换问题
        /// </summary>
        /// <param name="game">游戏信息</param>
        /// <param name="devices">设备列表</param>
        /// <returns>启动结果列表</returns>
        private async Task<List<DeviceGameResult>> LaunchGameDirectlyAsync(GameSource game, List<DeviceSource> devices)
        {
            // 直接获取游戏属性，避免线程切换
            string gameName = game.Name;
            string gamePackageName = game.PackageName;

            Log.Write(Level.INFO, $"=== LaunchGameDirectlyAsync 开始执行，游戏: {gameName}, 设备数量: {devices.Count} ===");
            var results = new List<DeviceGameResult>();

            foreach (var device in devices)
            {
                var result = new DeviceGameResult
                {
                    DeviceId = device.ID,
                    SerialNumber = device.SerialNumber,
                    Success = false,
                    StartTime = DateTime.Now
                };

                try
                {
                    Log.Write(Level.INFO, $"设备 {device.SerialNumber} 开始启动游戏 {gameName}");

                    // 使用Task.Run在后台线程中调用PICO SDK
                    int sdkResult = await Task.Run(() =>
                    {
                        var deviceManager = PICOSDK.DeviceManager.Instance;
                        return deviceManager.StartPlayApp(device.SerialNumber, gamePackageName);
                    });

                    if (sdkResult == 0)
                    {
                        result.Success = true;
                        result.SuccessMessage = "游戏启动成功";
                        Log.Write(Level.INFO, $"设备 {device.SerialNumber} 启动游戏 {gameName} 成功");

                        // 使用安全的方法更新设备状态
                        UpdateDeviceGameStatusSafely(device.SerialNumber, true, gamePackageName);
                    }
                    else
                    {
                        Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 启动游戏 {gameName} 失败，错误码: {sdkResult}");
                        result.ErrorMessage = $"启动失败，错误码: {sdkResult}";
                    }
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 启动游戏异常: {ex.Message}");
                    result.ErrorMessage = ex.Message;
                }

                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 发布简化的游戏启动响应
        /// </summary>
        private async Task PublishSimpleGameStartResponseAsync(string requestId, bool success, string errorMessage)
        {
            var response = new
            {
                requestId = requestId,
                success = success,
                errorMessage = errorMessage,
                results = new object[0], // 空数组
                messageId = Guid.NewGuid().ToString(),
                senderId = _clientId,
                receiverId = (string)null,
                timestamp = DateTime.Now,
                type = (string)null
            };

            string payload = JsonConvert.SerializeObject(response, JsonSettings);
            await PublishAsync("xr_system/game_control/start_response", payload);
            Log.Write(Level.INFO, $"游戏启动响应已发送: 成功={success}, 请求ID={requestId}");
        }

        /// <summary>
        /// 通过PICO SDK直接启动游戏，完全绕过UI层
        /// </summary>
        /// <param name="request">游戏启动请求</param>
        /// <returns>启动结果列表</returns>
        private async Task<List<DeviceGameResult>> LaunchGameDirectlyViaPicoSDK(GameStartRequestMessage request)
        {
            Log.Write(Level.INFO, $"=== LaunchGameDirectlyViaPicoSDK 开始执行 ===");
            var results = new List<DeviceGameResult>();

            // 从请求中获取游戏包名
            string gamePackageName = request.SelectedGame.PackageName;
            string gameName = request.SelectedGame.Name;

            Log.Write(Level.INFO, $"游戏信息: 名称={gameName}, 包名={gamePackageName}");

            foreach (string deviceIdStr in request.SelectedDeviceIds)
            {
                var result = new DeviceGameResult
                {
                    DeviceId = int.Parse(deviceIdStr),
                    SerialNumber = $"设备{deviceIdStr}", // 临时使用，后面会通过DeviceManager获取真实序列号
                    Success = false,
                    StartTime = DateTime.Now
                };

                try
                {
                    // 通过DeviceManager获取设备的真实序列号
                    string serialNumber = GetDeviceSerialNumber(deviceIdStr);
                    if (string.IsNullOrEmpty(serialNumber))
                    {
                        result.ErrorMessage = $"未找到设备ID {deviceIdStr} 对应的序列号";
                        Log.Write(Level.ERROR, result.ErrorMessage);
                        results.Add(result);
                        continue;
                    }

                    result.SerialNumber = serialNumber;
                    Log.Write(Level.INFO, $"设备 {serialNumber} 开始启动游戏 {gameName}");

                    // 使用Task.Run在后台线程中调用PICO SDK
                    int sdkResult = await Task.Run(() =>
                    {
                        var deviceManager = PICOSDK.DeviceManager.Instance;
                        return deviceManager.StartPlayApp(serialNumber, gamePackageName);
                    });

                    if (sdkResult == 0)
                    {
                        result.Success = true;
                        result.SuccessMessage = "游戏启动成功";
                        Log.Write(Level.INFO, $"设备 {serialNumber} 启动游戏 {gameName} 成功");

                        // 游戏启动成功后，更新PC端数据源
                        UpdateDeviceGameStatusSafely(serialNumber, true, gamePackageName);
                    }
                    else
                    {
                        Log.Write(Level.ERROR, $"设备 {serialNumber} 启动游戏 {gameName} 失败，错误码: {sdkResult}");
                        result.ErrorMessage = $"启动失败，错误码: {sdkResult}";

                        // 游戏启动失败，确保设备状态为未运行
                        UpdateDeviceGameStatusSafely(serialNumber, false, null);
                    }
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"设备 {result.SerialNumber} 启动游戏异常: {ex.Message}");
                    result.ErrorMessage = ex.Message;

                    // 异常情况下，确保设备状态为未运行
                    UpdateDeviceGameStatusSafely(result.SerialNumber, false, null);
                }

                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 安全地更新设备游戏状态
        /// </summary>
        /// <param name="serialNumber">设备序列号</param>
        /// <param name="isGameRunning">游戏是否运行</param>
        /// <param name="gamePackageName">游戏包名</param>
        private void UpdateDeviceGameStatusSafely(string serialNumber, bool isGameRunning, string gamePackageName)
        {
            try
            {
                Log.Write(Level.DEBUG, $"开始更新设备状态: {serialNumber}, 游戏运行={isGameRunning}");

                if (_deviceViewModel != null)
                {
                    // 直接调用DeviceViewModel的方法，它内部已经处理了线程安全问题
                    _deviceViewModel.UpdateDeviceGameStatus(serialNumber, isGameRunning, gamePackageName);
                    Log.Write(Level.INFO, $"设备状态更新成功: {serialNumber}, 游戏运行={isGameRunning}");
                }
                else
                {
                    Log.Write(Level.INFO, "DeviceViewModel 为空，无法更新设备状态");
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"更新设备状态异常: {ex.Message}");
            }
        }



        /// <summary>
        /// 通过PICO SDK直接停止游戏，完全绕过UI层
        /// </summary>
        /// <param name="request">游戏停止请求</param>
        /// <returns>停止结果列表</returns>
        private async Task<List<DeviceGameResult>> StopGameDirectlyViaPicoSDK(GameStopRequestMessage request)
        {
            Log.Write(Level.INFO, $"=== StopGameDirectlyViaPicoSDK 开始执行 ===");
            var results = new List<DeviceGameResult>();

            // 从请求中获取游戏包名
            string gamePackageName = request.SelectedGame?.PackageName;
            string gameName = request.SelectedGame?.Name ?? "当前游戏";

            Log.Write(Level.INFO, $"停止游戏: 名称={gameName}, 包名={gamePackageName}, 设备数量: {request.SelectedDeviceIds.Count}");

            foreach (string deviceIdStr in request.SelectedDeviceIds)
            {
                var result = new DeviceGameResult
                {
                    DeviceId = int.Parse(deviceIdStr),
                    SerialNumber = $"设备{deviceIdStr}", // 临时使用，后面会通过DeviceManager获取真实序列号
                    Success = false,
                    StartTime = DateTime.Now
                };

                try
                {
                    // 通过DeviceManager获取设备的真实序列号
                    string serialNumber = GetDeviceSerialNumber(deviceIdStr);
                    if (string.IsNullOrEmpty(serialNumber))
                    {
                        result.ErrorMessage = $"未找到设备ID {deviceIdStr} 对应的序列号";
                        Log.Write(Level.ERROR, result.ErrorMessage);
                        results.Add(result);
                        continue;
                    }

                    result.SerialNumber = serialNumber;
                    Log.Write(Level.INFO, $"设备 {serialNumber} 开始停止游戏 {gameName}");

                    // 使用Task.Run在后台线程中调用PICO SDK
                    int sdkResult = await Task.Run(() =>
                    {
                        var deviceManager = PICOSDK.DeviceManager.Instance;
                        // StopPlayApp需要设备ID和包名两个参数
                        if (!string.IsNullOrEmpty(gamePackageName))
                        {
                            return deviceManager.StopPlayApp(serialNumber, gamePackageName);
                        }
                        else
                        {
                            // 如果没有包名，尝试获取设备当前运行的游戏包名
                            string currentPackage = GetCurrentGamePackageForDevice(serialNumber);
                            if (!string.IsNullOrEmpty(currentPackage))
                            {
                                Log.Write(Level.INFO, $"使用设备当前游戏包名: {currentPackage}");
                                return deviceManager.StopPlayApp(serialNumber, currentPackage);
                            }
                            else
                            {
                                Log.Write(Level.INFO, $"无法获取设备 {serialNumber} 的游戏包名，跳过停止操作");
                                return -1; // 返回错误码
                            }
                        }
                    });

                    if (sdkResult == 0)
                    {
                        result.Success = true;
                        result.SuccessMessage = "游戏停止成功";
                        Log.Write(Level.INFO, $"设备 {serialNumber} 停止游戏 {gameName} 成功");

                        // 游戏停止成功后，更新PC端数据源
                        UpdateDeviceGameStatusSafely(serialNumber, false, null);
                    }
                    else
                    {
                        Log.Write(Level.ERROR, $"设备 {serialNumber} 停止游戏 {gameName} 失败，错误码: {sdkResult}");
                        result.ErrorMessage = $"停止失败，错误码: {sdkResult}";
                    }
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"设备 {result.SerialNumber} 停止游戏异常: {ex.Message}");
                    result.ErrorMessage = ex.Message;
                }

                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// 发布简化的游戏停止响应
        /// </summary>
        private async Task PublishSimpleGameStopResponseAsync(string requestId, bool success, string errorMessage)
        {
            var response = new
            {
                requestId = requestId,
                success = success,
                errorMessage = errorMessage,
                results = new object[0], // 空数组
                messageId = Guid.NewGuid().ToString(),
                senderId = _clientId,
                receiverId = (string)null,
                timestamp = DateTime.Now,
                type = (string)null
            };

            string payload = JsonConvert.SerializeObject(response, JsonSettings);
            await PublishAsync("xr_system/game_control/stop_response", payload);
            Log.Write(Level.INFO, $"游戏停止响应已发送: 成功={success}, 请求ID={requestId}");
        }

        /// <summary>
        /// 获取设备当前运行的游戏包名
        /// </summary>
        /// <param name="serialNumber">设备序列号</param>
        /// <returns>游戏包名</returns>
        private string GetCurrentGamePackageForDevice(string serialNumber)
        {
            try
            {
                // 方法1: 直接访问DeviceViewModel（它内部已经处理了线程安全问题）
                if (_deviceViewModel?.Source != null)
                {
                    try
                    {
                        var deviceSource = _deviceViewModel.Source.FirstOrDefault(d => d.SerialNumber == serialNumber);
                        if (deviceSource != null && !string.IsNullOrEmpty(deviceSource.CurrentGamePackageName))
                        {
                            Log.Write(Level.DEBUG, $"通过DeviceViewModel找到设备 {serialNumber} 当前游戏包名: {deviceSource.CurrentGamePackageName}");
                            return deviceSource.CurrentGamePackageName;
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Write(Level.INFO, $"访问DeviceViewModel失败: {ex.Message}");
                    }
                }

                // 方法2: 通过DeviceManager查找设备信息
                var deviceManager = PICOSDK.DeviceManager.Instance;
                if (deviceManager.Devices != null && deviceManager.Devices.ContainsKey(serialNumber))
                {
                    var deviceInfo = deviceManager.Devices[serialNumber];
                    if (!string.IsNullOrEmpty(deviceInfo.CurrentGamePackage))
                    {
                        Log.Write(Level.DEBUG, $"通过DeviceManager找到设备 {serialNumber} 当前游戏包名: {deviceInfo.CurrentGamePackage}");
                        return deviceInfo.CurrentGamePackage;
                    }
                }

                Log.Write(Level.INFO, $"无法获取设备 {serialNumber} 的当前游戏包名");
                return null;
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"获取设备当前游戏包名失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取设备序列号
        /// </summary>
        /// <param name="deviceIdStr">设备ID字符串</param>
        /// <returns>设备序列号</returns>
        private string GetDeviceSerialNumber(string deviceIdStr)
        {
            try
            {
                // 通过DeviceViewModel查找设备序列号
                if (_deviceViewModel?.Source != null)
                {
                    string serialNumber = null;
                    if (Application.Current?.Dispatcher != null)
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                var device = _deviceViewModel.Source.FirstOrDefault(d => d.ID.ToString() == deviceIdStr);
                                if (device != null && !string.IsNullOrEmpty(device.SerialNumber))
                                {
                                    serialNumber = device.SerialNumber;
                                    Log.Write(Level.DEBUG, $"通过DeviceViewModel找到设备: ID={deviceIdStr}, 序列号={serialNumber}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Log.Write(Level.ERROR, $"在UI线程中查找设备序列号失败: {ex.Message}");
                            }
                        });
                    }

                    if (!string.IsNullOrEmpty(serialNumber))
                    {
                        return serialNumber;
                    }
                }

                Log.Write(Level.INFO, $"未找到设备ID {deviceIdStr} 对应的序列号");
                return null;
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"获取设备序列号失败: {ex.Message}");
                return null;
            }
        }

    #region 设备控制和Ping检测处理方法

    /// <summary>
    /// 处理设备控制请求
    /// </summary>
    private async Task HandleDeviceControlRequestAsync(string payload)
    {
        try
        {
            var request = JsonConvert.DeserializeObject<DeviceControlRequestMessage>(payload);
            Log.Write(Level.INFO, $"收到设备控制请求: 设备={request.DeviceId}, 动作={request.ControlAction}");

            var response = new DeviceControlResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                DeviceId = request.DeviceId,
                ControlAction = request.ControlAction,
                ExecutedAt = DateTime.Now
            };

            // 根据控制动作执行相应操作
            switch (request.ControlAction?.ToLower())
            {
                case "enable":
                    response.Success = await EnableDeviceAsync(request.DeviceId);
                    break;
                case "disable":
                    response.Success = await DisableDeviceAsync(request.DeviceId);
                    break;
                case "restart":
                    response.Success = await RestartDeviceAsync(request.DeviceId);
                    break;
                case "power_on":
                    response.Success = await PowerOnDeviceAsync(request.DeviceId);
                    break;
                case "power_off":
                    response.Success = await PowerOffDeviceAsync(request.DeviceId);
                    break;
                default:
                    response.Success = false;
                    response.ErrorMessage = $"不支持的控制动作: {request.ControlAction}";
                    break;
            }

            if (!response.Success && string.IsNullOrEmpty(response.ErrorMessage))
            {
                response.ErrorMessage = $"执行控制动作 {request.ControlAction} 失败";
            }

            // 发布响应
            var responsePayload = JsonConvert.SerializeObject(response, JsonSettings);
            await PublishAsync(MqttTopics.DEVICE_CONTROL_RESPONSE, responsePayload);

            Log.Write(Level.INFO, $"设备控制响应已发送: 设备={request.DeviceId}, 成功={response.Success}");
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"处理设备控制请求失败: {ex.Message}");
        }
    }

    #endregion

    #region 设备控制操作方法

    /// <summary>
    /// 启用设备
    /// </summary>
    private async Task<bool> EnableDeviceAsync(string deviceId)
    {
        try
        {
            if (int.TryParse(deviceId, out int id))
            {
                var device = _deviceViewModel?.Source?.FirstOrDefault(d => d.ID == id);
                if (device != null)
                {
                    device.IsEnabled = true;
                    Log.Write(Level.INFO, $"设备 {device.Name} 已启用");
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"启用设备 {deviceId} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 禁用设备
    /// </summary>
    private async Task<bool> DisableDeviceAsync(string deviceId)
    {
        try
        {
            if (int.TryParse(deviceId, out int id))
            {
                var device = _deviceViewModel?.Source?.FirstOrDefault(d => d.ID == id);
                if (device != null)
                {
                    device.IsEnabled = false;
                    Log.Write(Level.INFO, $"设备 {device.Name} 已禁用");
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"禁用设备 {deviceId} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 重启设备
    /// </summary>
    private async Task<bool> RestartDeviceAsync(string deviceId)
    {
        try
        {
            // 这里可以集成PICO SDK的重启功能
            Log.Write(Level.INFO, $"重启设备 {deviceId} (功能待实现)");
            return true;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"重启设备 {deviceId} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 开机设备
    /// </summary>
    private async Task<bool> PowerOnDeviceAsync(string deviceId)
    {
        try
        {
            // 这里可以集成PICO SDK的开机功能
            Log.Write(Level.INFO, $"开机设备 {deviceId} (功能待实现)");
            return true;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"开机设备 {deviceId} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 关机设备
    /// </summary>
    private async Task<bool> PowerOffDeviceAsync(string deviceId)
    {
        try
        {
            // 这里可以集成PICO SDK的关机功能
            Log.Write(Level.INFO, $"关机设备 {deviceId} (功能待实现)");
            return true;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"关机设备 {deviceId} 失败: {ex.Message}");
            return false;
        }
    }

    #endregion

    #region 自动设备检测功能

    /// <summary>
    /// 启动自动设备检测
    /// </summary>
    private void StartAutomaticDevicePing()
    {
        try
        {
            // 如果已经有定时器在运行，先停止
            StopAutomaticDevicePing();

            // 创建新的定时器，延迟30秒后开始第一次检测，然后每分钟检测一次
            _devicePingTimer = new System.Threading.Timer(async _ => await PerformAutomaticDevicePing(),
                                       null,
                                       TimeSpan.FromSeconds(30),
                                       TimeSpan.FromMinutes(_pingIntervalMinutes));

            Log.Write(Level.INFO, $"自动设备检测已启动，检测间隔: {_pingIntervalMinutes} 分钟");
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"启动自动设备检测失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 停止自动设备检测
    /// </summary>
    private void StopAutomaticDevicePing()
    {
        try
        {
            if (_devicePingTimer != null)
            {
                _devicePingTimer.Dispose();
                _devicePingTimer = null;
                Log.Write(Level.INFO, "自动设备检测已停止");
            }
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"停止自动设备检测失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行自动设备检测
    /// </summary>
    private async Task PerformAutomaticDevicePing()
    {
        try
        {
            if (!_isConnected || _deviceViewModel?.Source == null)
            {
                Log.Write(Level.DEBUG, "MQTT未连接或设备数据源不可用，跳过设备检测");
                return;
            }

            Log.Write(Level.INFO, "开始执行自动设备检测");

            // 创建Ping服务实例
            var pingService = new DevicePingService(_deviceViewModel.Source);

            // 检测所有设备
            var results = await pingService.PingAllDevicesAsync();

            // 更新设备在线状态
            pingService.UpdateDeviceOnlineStatus(results);

            // 创建响应消息并广播给Android端
            var response = new DevicePingResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                Results = results,
                CheckedAt = DateTime.Now
            };

            // 发布检测结果给Android端
            var responsePayload = JsonConvert.SerializeObject(response, JsonSettings);
            await PublishAsync(MqttTopics.DEVICE_PING_RESPONSE, responsePayload);

            int onlineCount = results.Count(r => r.IsOnline);
            Log.Write(Level.INFO, $"自动设备检测完成并已通知Android端: {onlineCount}/{results.Count} 台设备在线");

            // 触发设备状态更新广播
            await BroadcastDeviceStatusUpdateAsync();
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"执行自动设备检测失败: {ex.Message}");
        }
    }

    #endregion

    #region 资源释放

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            // 停止自动设备检测
            StopAutomaticDevicePing();

            // 断开MQTT连接
            if (_isConnected && mqttClient != null)
            {
                mqttClient.DisconnectAsync().Wait(5000);
            }

            // 释放MQTT客户端
            mqttClient?.Dispose();

            Log.Write(Level.INFO, "MQTT客户端资源已释放");
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"释放MQTT客户端资源失败: {ex.Message}");
        }
    }

    #endregion
    }

    /// <summary>
    /// 游戏启动请求事件参数
    /// </summary>
    public class GameStartRequestEventArgs : EventArgs
    {
        public GameStartRequestMessage Request { get; }

        public GameStartRequestEventArgs(GameStartRequestMessage request)
        {
            Request = request;
        }
    }

    /// <summary>
    /// 设备列表请求事件参数
    /// </summary>
    public class DeviceListRequestEventArgs : EventArgs
    {
        public DeviceListRequestMessage Request { get; }

        public DeviceListRequestEventArgs(DeviceListRequestMessage request)
        {
            Request = request;
        }
    }

    /// <summary>
    /// 设备游戏结果类
    /// </summary>
    public class DeviceGameResult
    {
        public int DeviceId { get; set; }
        public string SerialNumber { get; set; }
        public bool Success { get; set; }
        public string SuccessMessage { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// 连接状态事件参数
    /// </summary>
    public class ConnectionStatusEventArgs : EventArgs
    {
        public bool IsConnected { get; }
        public string Message { get; }

        public ConnectionStatusEventArgs(bool isConnected, string message)
        {
            IsConnected = isConnected;
            Message = message;
        }
    }
}