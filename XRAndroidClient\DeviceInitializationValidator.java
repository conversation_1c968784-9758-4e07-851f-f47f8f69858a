import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 设备初始化验证器
 * 用于验证Android端设备初始化逻辑的正确性
 */
public class DeviceInitializationValidator {
    
    public static void main(String[] args) {
        System.out.println("=== Android端设备初始化验证 ===\n");
        
        // 模拟DeviceInfo.createInitialDeviceList()的逻辑
        List<MockDeviceInfo> deviceList = createMockDeviceList();
        
        // 执行各项验证
        validateDeviceCount(deviceList);
        validateDeviceTypes(deviceList);
        validateDeviceIds(deviceList);
        validateDeviceStates(deviceList);
        validateFragmentFiltering(deviceList);
        
        System.out.println("=== 验证完成 ===");
    }
    
    private static List<MockDeviceInfo> createMockDeviceList() {
        List<MockDeviceInfo> devices = new java.util.ArrayList<>();
        
        // 创建30个头显设备
        // 80服务器设备 (8001-8015)
        for (int i = 1; i <= 15; i++) {
            MockDeviceInfo device = new MockDeviceInfo();
            device.id = 8000 + i;
            device.serialNumber = "HMD_80_" + String.format("%02d", i);
            device.name = i + "号机";
            device.ipAddress = "192.168.1." + (99 + i);
            device.deviceType = MockDeviceType.HMD;
            device.isOnline = false;
            device.isEnabled = false;
            device.batteryLevel = 0;
            devices.add(device);
        }
        
        // 81服务器设备 (8101-8115)
        for (int i = 1; i <= 15; i++) {
            MockDeviceInfo device = new MockDeviceInfo();
            device.id = 8100 + i;
            device.serialNumber = "HMD_81_" + String.format("%02d", i);
            device.name = i + "号机";
            device.ipAddress = "192.168.1." + (114 + i);
            device.deviceType = MockDeviceType.HMD;
            device.isOnline = false;
            device.isEnabled = false;
            device.batteryLevel = 0;
            devices.add(device);
        }
        
        // 创建6个外设设备 (与PC端配置保持一致)
        String[] peripheralNames = {"动感平台", "可调速风扇", "喷水设备", "热感设备", "可推拉门", "指令控制门"};
        String[] peripheralSerialNumbers = {"MOTION_PLATFORM_001", "FAN_001", "WATER_SPRAY_001", "HOTNESS_001", "DOOR_MANUAL_001", "DOOR_CMD_001"};
        MockDeviceType[] peripheralTypes = {
            MockDeviceType.MotionPlatform, MockDeviceType.Fan, MockDeviceType.WaterSpray,
            MockDeviceType.Hotness, MockDeviceType.DoorManual, MockDeviceType.DoorCmdControl
        };

        for (int i = 0; i < 6; i++) {
            MockDeviceInfo device = new MockDeviceInfo();
            device.id = 9001 + i;
            device.serialNumber = peripheralSerialNumbers[i];
            device.name = peripheralNames[i];
            device.ipAddress = "192.168.1." + (201 + i);
            device.deviceType = peripheralTypes[i];
            device.isOnline = false;
            device.isEnabled = false;
            device.batteryLevel = 100;
            devices.add(device);
        }
        
        return devices;
    }
    
    private static void validateDeviceCount(List<MockDeviceInfo> deviceList) {
        System.out.println("1. 设备数量验证:");
        System.out.println("   总设备数: " + deviceList.size() + " (预期: 36)");
        
        long hmdCount = deviceList.stream()
                .filter(device -> device.deviceType == MockDeviceType.HMD)
                .count();
        System.out.println("   头显设备数: " + hmdCount + " (预期: 30)");
        
        long peripheralCount = deviceList.stream()
                .filter(device -> device.deviceType != MockDeviceType.HMD 
                        && device.deviceType != MockDeviceType.SERVER 
                        && device.deviceType != MockDeviceType.GAMESVC)
                .count();
        System.out.println("   外设设备数: " + peripheralCount + " (预期: 6)");
        System.out.println();
    }
    
    private static void validateDeviceTypes(List<MockDeviceInfo> deviceList) {
        System.out.println("2. 设备类型验证:");
        Map<MockDeviceType, Integer> typeCount = new HashMap<>();
        
        for (MockDeviceInfo device : deviceList) {
            typeCount.put(device.deviceType, typeCount.getOrDefault(device.deviceType, 0) + 1);
        }
        
        for (Map.Entry<MockDeviceType, Integer> entry : typeCount.entrySet()) {
            System.out.println("   " + entry.getKey() + ": " + entry.getValue() + "个");
        }
        System.out.println();
    }
    
    private static void validateDeviceIds(List<MockDeviceInfo> deviceList) {
        System.out.println("3. 设备ID验证:");
        
        long server80Count = deviceList.stream()
                .filter(device -> device.id >= 8001 && device.id <= 8015)
                .count();
        System.out.println("   80服设备 (8001-8015): " + server80Count + "个");
        
        long server81Count = deviceList.stream()
                .filter(device -> device.id >= 8101 && device.id <= 8115)
                .count();
        System.out.println("   81服设备 (8101-8115): " + server81Count + "个");
        
        long peripheralIdCount = deviceList.stream()
                .filter(device -> device.id >= 9001 && device.id <= 9006)
                .count();
        System.out.println("   外设设备 (9001-9006): " + peripheralIdCount + "个");
        System.out.println();
    }
    
    private static void validateDeviceStates(List<MockDeviceInfo> deviceList) {
        System.out.println("4. 设备状态验证:");
        
        boolean allOffline = deviceList.stream().allMatch(device -> !device.isOnline);
        boolean allDisabled = deviceList.stream().allMatch(device -> !device.isEnabled);
        
        System.out.println("   所有设备离线: " + allOffline);
        System.out.println("   所有设备禁用: " + allDisabled);
        System.out.println();
    }
    
    private static void validateFragmentFiltering(List<MockDeviceInfo> deviceList) {
        System.out.println("5. Fragment筛选验证:");
        
        // GameProgressFragment筛选 (只显示HMD)
        long gameProgressCount = deviceList.stream()
                .filter(device -> device.deviceType == MockDeviceType.HMD)
                .count();
        System.out.println("   GameProgressFragment显示: " + gameProgressCount + "个设备");
        
        // DeviceStatusFragment筛选 (只显示HMD)
        long deviceStatusCount = deviceList.stream()
                .filter(device -> device.deviceType == MockDeviceType.HMD)
                .count();
        System.out.println("   DeviceStatusFragment显示: " + deviceStatusCount + "个设备");
        
        // HardwareCheckFragment筛选 (除HMD、SERVER、GAMESVC外)
        long hardwareCheckCount = deviceList.stream()
                .filter(device -> device.deviceType != MockDeviceType.HMD 
                        && device.deviceType != MockDeviceType.SERVER 
                        && device.deviceType != MockDeviceType.GAMESVC)
                .count();
        System.out.println("   HardwareCheckFragment显示: " + hardwareCheckCount + "个设备");
        System.out.println();
    }
    
    // 模拟类
    static class MockDeviceInfo {
        int id;
        String serialNumber;
        String name;
        String ipAddress;
        MockDeviceType deviceType;
        boolean isOnline;
        boolean isEnabled;
        double batteryLevel;
    }
    
    enum MockDeviceType {
        SERVER, GAMESVC, HMD, MotionPlatform, Fan, Hotness, DoorManual, DoorCmdControl, WaterSpray
    }
}
