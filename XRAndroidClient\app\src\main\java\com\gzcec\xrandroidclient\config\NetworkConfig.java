package com.gzcec.xrandroidclient.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 网络配置管理器
 * 负责管理MQTT Broker连接配置和网络发现
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2.0
 */
public class NetworkConfig {
    
    private static final String TAG = "NetworkConfig";
    
    // ==================== 配置常量 ====================
    
    /**
     * SharedPreferences文件名
     */
    private static final String PREFS_NAME = "network_config";
    
    /**
     * MQTT Broker主机地址键
     */
    private static final String KEY_MQTT_HOST = "mqtt_host";
    
    /**
     * MQTT Broker端口键
     */
    private static final String KEY_MQTT_PORT = "mqtt_port";
    
    /**
     * 自动发现启用键
     */
    private static final String KEY_AUTO_DISCOVERY = "auto_discovery";
    
    /**
     * 默认MQTT端口
     */
    public static final int DEFAULT_MQTT_PORT = 1883;
    
    /**
     * 常见的MQTT Broker IP地址列表
     */
    private static final String[] COMMON_BROKER_IPS = {
        "*************",   // 默认配置
        "*************",   // 常见路由器网段
        "**************",  // 当前Android设备网段
        "**********",      // 企业网络
        "************"     // 私有网络
    };
    
    // ==================== 成员变量 ====================
    
    private final Context context;
    private final SharedPreferences prefs;
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数
     * @param context Android上下文
     */
    public NetworkConfig(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    // ==================== 公共接口 ====================
    
    /**
     * 获取MQTT Broker主机地址
     * @return 主机地址
     */
    public String getMqttHost() {
        String savedHost = prefs.getString(KEY_MQTT_HOST, null);
        if (savedHost != null) {
            return savedHost;
        }
        
        // 如果没有保存的配置，尝试自动发现
        if (isAutoDiscoveryEnabled()) {
            String discoveredHost = discoverMqttBroker();
            if (discoveredHost != null) {
                setMqttHost(discoveredHost);
                return discoveredHost;
            }
        }
        
        // 返回基于当前网络的默认地址
        return getDefaultMqttHost();
    }
    
    /**
     * 设置MQTT Broker主机地址
     * @param host 主机地址
     */
    public void setMqttHost(String host) {
        prefs.edit().putString(KEY_MQTT_HOST, host).apply();
        Log.i(TAG, "MQTT主机地址已设置: " + host);
    }
    
    /**
     * 获取MQTT Broker端口
     * @return 端口号
     */
    public int getMqttPort() {
        return prefs.getInt(KEY_MQTT_PORT, DEFAULT_MQTT_PORT);
    }
    
    /**
     * 设置MQTT Broker端口
     * @param port 端口号
     */
    public void setMqttPort(int port) {
        prefs.edit().putInt(KEY_MQTT_PORT, port).apply();
        Log.i(TAG, "MQTT端口已设置: " + port);
    }

    /**
     * 强制使用用户配置
     * 清除推荐配置，确保使用用户手动设置的配置
     */
    public void forceUseUserConfig() {
        Log.i(TAG, "强制使用用户配置，清除推荐配置");
        // 这里可以添加一个标记，表示用户明确要求使用手动配置
        prefs.edit().putBoolean("force_user_config", true).apply();
    }

    /**
     * 检查是否强制使用用户配置
     */
    public boolean isForceUserConfig() {
        return prefs.getBoolean("force_user_config", false);
    }

    /**
     * 清除强制使用用户配置的标记
     */
    public void clearForceUserConfig() {
        prefs.edit().remove("force_user_config").apply();
    }
    
    /**
     * 获取完整的MQTT Broker URL
     * @return MQTT URL
     */
    public String getMqttUrl() {
        return "tcp://" + getMqttHost() + ":" + getMqttPort();
    }
    
    /**
     * 是否启用自动发现
     * @return true如果启用
     */
    public boolean isAutoDiscoveryEnabled() {
        return prefs.getBoolean(KEY_AUTO_DISCOVERY, true);
    }
    
    /**
     * 设置自动发现启用状态
     * @param enabled 是否启用
     */
    public void setAutoDiscoveryEnabled(boolean enabled) {
        prefs.edit().putBoolean(KEY_AUTO_DISCOVERY, enabled).apply();
        Log.i(TAG, "自动发现已" + (enabled ? "启用" : "禁用"));
    }
    
    /**
     * 获取当前设备的IP地址
     * @return IP地址
     */
    public String getLocalIpAddress() {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                for (InetAddress addr : addrs) {
                    if (!addr.isLoopbackAddress()) {
                        String sAddr = addr.getHostAddress();
                        // 过滤IPv4地址
                        if (sAddr != null && sAddr.indexOf(':') < 0) {
                            return sAddr;
                        }
                    }
                }
            }
        } catch (SocketException e) {
            Log.e(TAG, "获取本地IP地址失败", e);
        }
        return null;
    }
    
    /**
     * 获取WiFi网络信息
     * @return WiFi信息字符串
     */
    public String getWifiInfo() {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    String ssid = wifiInfo.getSSID();
                    int ipAddress = wifiInfo.getIpAddress();
                    String ip = String.format("%d.%d.%d.%d",
                        (ipAddress & 0xff),
                        (ipAddress >> 8 & 0xff),
                        (ipAddress >> 16 & 0xff),
                        (ipAddress >> 24 & 0xff));
                    return String.format("SSID: %s, IP: %s", ssid, ip);
                }
            }
        } catch (SecurityException e) {
            Log.w(TAG, "无法访问WiFi信息，权限不足: " + e.getMessage());
            return "WiFi信息不可用 (权限不足)";
        } catch (Exception e) {
            Log.e(TAG, "获取WiFi信息失败", e);
        }
        return "WiFi信息不可用";
    }
    
    /**
     * 检查网络连接状态
     * @return true如果网络已连接
     */
    public boolean isNetworkConnected() {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
        }
        return false;
    }
    
    /**
     * 获取可能的MQTT Broker地址列表
     * @return 地址列表
     */
    public List<String> getPossibleBrokerAddresses() {
        List<String> addresses = new ArrayList<>();
        
        // 添加当前网段的可能地址
        String localIp = getLocalIpAddress();
        if (localIp != null) {
            String networkPrefix = getNetworkPrefix(localIp);
            if (networkPrefix != null) {
                addresses.add(networkPrefix + ".100");
                addresses.add(networkPrefix + ".1");
                addresses.add(networkPrefix + ".10");
            }
        }
        
        // 添加常见地址
        for (String ip : COMMON_BROKER_IPS) {
            if (!addresses.contains(ip)) {
                addresses.add(ip);
            }
        }
        
        return addresses;
    }
    
    /**
     * 重置网络配置
     */
    public void resetConfig() {
        prefs.edit().clear().apply();
        Log.i(TAG, "网络配置已重置");
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 获取默认的MQTT主机地址
     * @return 默认主机地址
     */
    private String getDefaultMqttHost() {
        String localIp = getLocalIpAddress();
        if (localIp != null) {
            String networkPrefix = getNetworkPrefix(localIp);
            if (networkPrefix != null) {
                return networkPrefix + ".100";
            }
        }
        return COMMON_BROKER_IPS[0]; // 返回第一个常见地址
    }
    
    /**
     * 从IP地址获取网络前缀
     * @param ip IP地址
     * @return 网络前缀 (例如: 192.168.1)
     */
    private String getNetworkPrefix(String ip) {
        if (ip != null) {
            int lastDot = ip.lastIndexOf('.');
            if (lastDot > 0) {
                return ip.substring(0, lastDot);
            }
        }
        return null;
    }
    
    /**
     * 自动发现MQTT Broker (异步执行，避免阻塞主线程)
     * @return 发现的Broker地址，如果未发现返回null
     */
    private String discoverMqttBroker() {
        Log.i(TAG, "开始自动发现MQTT Broker...");

        List<String> possibleAddresses = getPossibleBrokerAddresses();

        // 限制测试数量，避免阻塞太久
        int maxTests = Math.min(possibleAddresses.size(), 3);
        for (int i = 0; i < maxTests; i++) {
            String address = possibleAddresses.get(i);
            if (testMqttConnection(address, DEFAULT_MQTT_PORT)) {
                Log.i(TAG, "发现MQTT Broker: " + address);
                return address;
            }
        }

        Log.w(TAG, "未发现可用的MQTT Broker");
        return null;
    }
    
    /**
     * 测试MQTT连接
     * @param host 主机地址
     * @param port 端口
     * @return true如果连接成功
     */
    private boolean testMqttConnection(String host, int port) {
        try {
            // 简单的Socket连接测试，使用较短的超时时间
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress(host, port), 1000); // 1秒超时
            socket.close();
            return true;
        } catch (Exception e) {
            Log.d(TAG, "测试连接失败: " + host + ":" + port);
            return false;
        }
    }
    
    // ==================== 静态工具方法 ====================
    
    /**
     * 验证IP地址格式
     * @param ip IP地址字符串
     * @return true如果格式正确
     */
    public static boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 验证端口号
     * @param port 端口号
     * @return true如果端口号有效
     */
    public static boolean isValidPort(int port) {
        return port > 0 && port <= 65535;
    }
}
