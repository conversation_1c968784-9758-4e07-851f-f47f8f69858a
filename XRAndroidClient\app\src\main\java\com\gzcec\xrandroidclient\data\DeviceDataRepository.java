package com.gzcec.xrandroidclient.data;

import android.util.Log;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.device.DeviceType;
import com.gzcec.xrandroidclient.DeviceIdManager;

/**
 * 设备数据仓库
 * 全局单例，负责设备数据的存储、更新和分发
 * 使用LiveData实现数据绑定和实时更新
 */
public class DeviceDataRepository {
    private static final String TAG = "DeviceDataRepository";
    private static DeviceDataRepository instance;
    
    // 完整设备列表的LiveData
    private final MutableLiveData<List<DeviceInfo>> allDevicesLiveData = new MutableLiveData<>();
    
    // 按类型筛选的设备列表LiveData
    private final MutableLiveData<List<DeviceInfo>> hmdDevicesLiveData = new MutableLiveData<>();
    
    // 按服务器筛选的设备列表LiveData
    private final MutableLiveData<List<DeviceInfo>> server80DevicesLiveData = new MutableLiveData<>();
    private final MutableLiveData<List<DeviceInfo>> server81DevicesLiveData = new MutableLiveData<>();

    // 硬件设备列表LiveData（除了HMD、SERVER、GAMESVC之外的设备）
    private final MutableLiveData<List<DeviceInfo>> hardwareDevicesLiveData = new MutableLiveData<>();
    
    // 在线设备统计LiveData
    private final MutableLiveData<DeviceStatistics> statisticsLiveData = new MutableLiveData<>();
    
    // 内部存储的设备列表
    private final List<DeviceInfo> deviceList = new CopyOnWriteArrayList<>();
    
    private DeviceDataRepository() {
        // 私有构造函数
        initializeDeviceList();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized DeviceDataRepository getInstance() {
        if (instance == null) {
            instance = new DeviceDataRepository();
        }
        return instance;
    }
    
    /**
     * 初始化设备列表
     */
    private void initializeDeviceList() {
        Log.d(TAG, "初始化设备数据仓库");
        
        // 使用DeviceInfo的初始化列表
        List<DeviceInfo> initialDevices = DeviceInfo.createInitialDeviceList();
        deviceList.clear();
        deviceList.addAll(initialDevices);
        
        // 更新所有LiveData
        updateAllLiveData();
        
        Log.d(TAG, "设备数据仓库初始化完成: " + deviceList.size() + " 台设备");
    }
    
    /**
     * 从PC端更新设备数据
     */
    public synchronized void updateDevicesFromServer(List<DeviceInfo> serverDevices) {
        if (serverDevices == null || serverDevices.isEmpty()) {
            Log.w(TAG, "服务器设备列表为空，跳过更新");
            return;
        }
        
        Log.d(TAG, "从PC端更新设备数据: " + serverDevices.size() + " 台设备");
        
        int updatedCount = 0;
        
        // 更新现有设备的状态
        for (DeviceInfo serverDevice : serverDevices) {
            boolean found = false;
            for (int i = 0; i < deviceList.size(); i++) {
                DeviceInfo localDevice = deviceList.get(i);
                if (localDevice.getId() == serverDevice.getId()) {
                    // 保存用户的选中状态
                    boolean wasSelected = localDevice.isSelected();
                    
                    // 更新设备信息
                    deviceList.set(i, serverDevice);
                    deviceList.get(i).setSelected(wasSelected);
                    
                    updatedCount++;
                    found = true;
                    
                    Log.d(TAG, "更新设备: ID=" + serverDevice.getId() + 
                        ", 在线=" + serverDevice.isOnline() + 
                        ", 电量=" + serverDevice.getBatteryLevel() + "%");
                    break;
                }
            }
            
            // 如果是新设备，添加到列表
            if (!found) {
                deviceList.add(serverDevice);
                updatedCount++;
                Log.d(TAG, "添加新设备: ID=" + serverDevice.getId());
            }
        }
        
        // 更新所有LiveData，触发UI更新
        updateAllLiveData();
        
        Log.d(TAG, "设备数据更新完成: " + updatedCount + " 台设备已更新");
    }
    
    /**
     * 更新设备选中状态
     */
    public synchronized void updateDeviceSelection(int deviceId, boolean selected) {
        for (DeviceInfo device : deviceList) {
            if (device.getId() == deviceId) {
                device.setSelected(selected);
                Log.d(TAG, "更新设备选中状态: ID=" + deviceId + ", 选中=" + selected);
                break;
            }
        }
        
        // 更新相关LiveData
        updateAllLiveData();
    }
    
    /**
     * 批量更新设备选中状态
     */
    public synchronized void updateDeviceSelections(List<Integer> deviceIds, boolean selected) {
        for (int deviceId : deviceIds) {
            for (DeviceInfo device : deviceList) {
                if (device.getId() == deviceId) {
                    device.setSelected(selected);
                    break;
                }
            }
        }
        
        Log.d(TAG, "批量更新设备选中状态: " + deviceIds.size() + " 台设备, 选中=" + selected);
        
        // 更新相关LiveData
        updateAllLiveData();
    }
    
    /**
     * 清空所有设备选中状态
     */
    public synchronized void clearAllSelections() {
        for (DeviceInfo device : deviceList) {
            device.setSelected(false);
        }
        
        Log.d(TAG, "清空所有设备选中状态");
        
        // 更新相关LiveData
        updateAllLiveData();
    }
    
    /**
     * 更新所有LiveData
     */
    private void updateAllLiveData() {
        // 更新完整设备列表
        allDevicesLiveData.postValue(new ArrayList<>(deviceList));
        
        // 更新HMD设备列表
        List<DeviceInfo> hmdDevices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HMD) {
                hmdDevices.add(device);
            }
        }
        hmdDevicesLiveData.postValue(hmdDevices);
        
        // 更新80服设备列表
        List<DeviceInfo> server80Devices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HMD) {
                int serverPrefix = DeviceIdManager.getServerPrefix(device.getId());
                if (serverPrefix == DeviceIdManager.SERVER_80) {
                    server80Devices.add(device);
                }
            }
        }
        server80DevicesLiveData.postValue(server80Devices);
        
        // 更新81服设备列表
        List<DeviceInfo> server81Devices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HMD) {
                int serverPrefix = DeviceIdManager.getServerPrefix(device.getId());
                if (serverPrefix == DeviceIdManager.SERVER_81) {
                    server81Devices.add(device);
                }
            }
        }
        server81DevicesLiveData.postValue(server81Devices);

        // 更新硬件设备列表
        List<DeviceInfo> hardwareDevices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (isHardwareDevice(device)) {
                hardwareDevices.add(device);
            }
        }
        hardwareDevicesLiveData.postValue(hardwareDevices);

        // 更新统计信息
        updateStatistics();
    }
    
    /**
     * 更新设备统计信息
     */
    private void updateStatistics() {
        int totalDevices = 0;
        int onlineDevices = 0;
        int selectedDevices = 0;
        int hmdDevices = 0;
        
        for (DeviceInfo device : deviceList) {
            totalDevices++;
            
            if (device.getDeviceType() == DeviceType.HMD) {
                hmdDevices++;
            }
            
            if (device.isOnline()) {
                onlineDevices++;
            }
            
            if (device.isSelected()) {
                selectedDevices++;
            }
        }
        
        DeviceStatistics statistics = new DeviceStatistics(
            totalDevices, onlineDevices, selectedDevices, hmdDevices);
        statisticsLiveData.postValue(statistics);
    }
    
    // ========== LiveData获取方法 ==========
    
    /**
     * 获取所有设备的LiveData
     */
    public LiveData<List<DeviceInfo>> getAllDevicesLiveData() {
        return allDevicesLiveData;
    }
    
    /**
     * 获取HMD设备的LiveData
     */
    public LiveData<List<DeviceInfo>> getHmdDevicesLiveData() {
        return hmdDevicesLiveData;
    }
    
    /**
     * 获取80服设备的LiveData
     */
    public LiveData<List<DeviceInfo>> getServer80DevicesLiveData() {
        return server80DevicesLiveData;
    }
    
    /**
     * 获取81服设备的LiveData
     */
    public LiveData<List<DeviceInfo>> getServer81DevicesLiveData() {
        return server81DevicesLiveData;
    }

    /**
     * 获取硬件设备的LiveData
     */
    public LiveData<List<DeviceInfo>> getHardwareDevicesLiveData() {
        return hardwareDevicesLiveData;
    }
    
    /**
     * 获取设备统计信息的LiveData
     */
    public LiveData<DeviceStatistics> getStatisticsLiveData() {
        return statisticsLiveData;
    }
    
    // ========== 同步获取方法（用于非UI线程） ==========
    
    /**
     * 同步获取所有设备列表的副本
     */
    public List<DeviceInfo> getAllDevices() {
        return new ArrayList<>(deviceList);
    }
    
    /**
     * 同步获取HMD设备列表
     */
    public List<DeviceInfo> getHmdDevices() {
        List<DeviceInfo> hmdDevices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HMD) {
                hmdDevices.add(device);
            }
        }
        return hmdDevices;
    }
    
    /**
     * 根据服务器获取设备列表
     */
    public List<DeviceInfo> getDevicesByServer(int serverPrefix) {
        List<DeviceInfo> serverDevices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HMD) {
                int deviceServerPrefix = DeviceIdManager.getServerPrefix(device.getId());
                if (deviceServerPrefix == serverPrefix) {
                    serverDevices.add(device);
                }
            }
        }
        return serverDevices;
    }
    
    /**
     * 获取硬件设备列表
     */
    public List<DeviceInfo> getHardwareDevices() {
        List<DeviceInfo> hardwareDevices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (isHardwareDevice(device)) {
                hardwareDevices.add(device);
            }
        }
        return hardwareDevices;
    }

    /**
     * 根据ID获取设备
     */
    public DeviceInfo getDeviceById(int deviceId) {
        for (DeviceInfo device : deviceList) {
            if (device.getId() == deviceId) {
                return device;
            }
        }
        return null;
    }

    /**
     * 判断是否为硬件设备
     * 硬件设备包括：动感平台、风扇、热感、门控、喷水等
     * 排除：HMD、服务器、游戏服务端
     */
    private boolean isHardwareDevice(DeviceInfo device) {
        DeviceType deviceType = device.getDeviceType();
        return deviceType != DeviceType.HMD &&
               deviceType != DeviceType.SERVER &&
               deviceType != DeviceType.GAMESVC;
    }
    
    /**
     * 设备统计信息类
     */
    public static class DeviceStatistics {
        private final int totalDevices;
        private final int onlineDevices;
        private final int selectedDevices;
        private final int hmdDevices;
        
        public DeviceStatistics(int totalDevices, int onlineDevices, int selectedDevices, int hmdDevices) {
            this.totalDevices = totalDevices;
            this.onlineDevices = onlineDevices;
            this.selectedDevices = selectedDevices;
            this.hmdDevices = hmdDevices;
        }
        
        public int getTotalDevices() { return totalDevices; }
        public int getOnlineDevices() { return onlineDevices; }
        public int getSelectedDevices() { return selectedDevices; }
        public int getHmdDevices() { return hmdDevices; }
        
        @Override
        public String toString() {
            return "DeviceStatistics{" +
                "总设备=" + totalDevices +
                ", 在线=" + onlineDevices +
                ", 已选=" + selectedDevices +
                ", HMD=" + hmdDevices +
                '}';
        }
    }
}
