package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.communication.data.DeviceStopResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 游戏停止响应消息
 */
public class GameStopResponseMessage extends BaseMessage {
    private String requestId;
    private boolean success;
    private String errorMessage;
    private List<DeviceStopResult> results;

    public GameStopResponseMessage() {
        setType(MessageType.GAME_STOP_RESPONSE);
        this.results = new ArrayList<>();
    }

    // Getter和Setter方法
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public List<DeviceStopResult> getResults() { return results; }
    public void setResults(List<DeviceStopResult> results) { this.results = results; }
}
