﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using XRSvc.DataPack;
using XRSvc.DataSource;
using PICOSDK;
using Jskj.AppLog;
using System.Windows;

namespace XRSvc.Services
{
    /// <summary>
    /// 游戏启动服务，负责管理游戏启动的完整流程
    /// </summary>
    public class GameLaunchService
    {
        #region Fields

        /// <summary>
        /// 设备管理器实例
        /// </summary>
        private readonly DeviceManager _deviceManager;

        #endregion

        #region Events

        /// <summary>
        /// 游戏启动进度事件
        /// </summary>
        public event EventHandler<GameLaunchProgressEventArgs> LaunchProgress;

        /// <summary>
        /// 游戏启动完成事件
        /// </summary>
        public event EventHandler<GameLaunchResultEventArgs> LaunchCompleted;

        #endregion

        #region Constructor

        /// <summary>
        /// 构造函数，初始化游戏启动服务
        /// </summary>
        public GameLaunchService()
        {
            _deviceManager = DeviceManager.Instance;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 启动游戏到指定设备
        /// </summary>
        /// <param name="selectedGame">选中的游戏</param>
        /// <param name="selectedDevices">选中的设备列表</param>
        /// <returns>启动任务</returns>
        public async Task<GameLaunchResult> LaunchGameAsync(GameSource selectedGame, IEnumerable<DeviceSource> selectedDevices)
        {
            var result = new GameLaunchResult
            {
                GameId = selectedGame.ID,
                GameName = selectedGame.Name,
                PackageName = selectedGame.PackageName,
                LaunchTime = DateTime.Now
            };

            try
            {
                Log.Write(Level.INFO, $"开始启动游戏 - 游戏: {selectedGame.Name}({selectedGame.PackageName}), 设备数量: {selectedDevices?.Count() ?? 0}");

                // 验证输入参数
                if (selectedGame == null)
                {
                    throw new ArgumentNullException(nameof(selectedGame), "选中的游戏不能为空");
                }

                if (string.IsNullOrEmpty(selectedGame.PackageName))
                {
                    throw new ArgumentException("游戏包名不能为空", nameof(selectedGame.PackageName));
                }

                var deviceList = selectedDevices?.ToList() ?? new List<DeviceSource>();
                if (!deviceList.Any())
                {
                    throw new ArgumentException("至少需要选择一个设备", nameof(selectedDevices));
                }

                // 过滤出在线的设备
                var onlineDevices = deviceList.Where(d => d.IsOnline).ToList();
                var offlineDevices = deviceList.Where(d => !d.IsOnline).ToList();

                if (!onlineDevices.Any())
                {
                    throw new InvalidOperationException("没有在线设备可以启动游戏");
                }

                Log.Write(Level.INFO, $"设备状态统计 - 在线: {onlineDevices.Count}, 离线: {offlineDevices.Count}");

                // 记录离线设备
                foreach (var device in offlineDevices)
                {
                    result.FailedDevices.Add(new DeviceLaunchResult
                    {
                        DeviceId = device.ID,
                        SerialNumber = device.SerialNumber,
                        Success = false,
                        ErrorMessage = "设备离线",
                        LaunchTime = DateTime.Now
                    });
                }

                // 启动游戏到在线设备
                var launchTasks = onlineDevices.Select(device => LaunchGameToDeviceAsync(selectedGame, device));
                var launchResults = await Task.WhenAll(launchTasks);

                // 汇总结果
                foreach (var deviceResult in launchResults)
                {
                    if (deviceResult.Success)
                    {
                        result.SuccessDevices.Add(deviceResult);
                    }
                    else
                    {
                        result.FailedDevices.Add(deviceResult);
                    }
                }

                result.Success = result.SuccessDevices.Any();
                result.TotalDevices = deviceList.Count;
                result.SuccessCount = result.SuccessDevices.Count;
                result.FailedCount = result.FailedDevices.Count;

                Log.Write(Level.INFO, $"游戏启动完成 - 成功: {result.SuccessCount}, 失败: {result.FailedCount}");

                // 触发完成事件
                LaunchCompleted?.Invoke(this, new GameLaunchResultEventArgs(result));

                return result;
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"游戏启动异常: {ex.Message}");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                LaunchCompleted?.Invoke(this, new GameLaunchResultEventArgs(result));
                return result;
            }
        }

        /// <summary>
        /// 停止指定设备上的游戏
        /// </summary>
        /// <param name="devices">要停止游戏的设备列表</param>
        /// <param name="packageName">要停止的游戏包名</param>
        /// <returns>停止任务</returns>
        public async Task<GameStopResult> StopGameAsync(IEnumerable<DeviceSource> devices, string packageName)
        {
            var result = new GameStopResult
            {
                StopTime = DateTime.Now
            };

            try
            {
                var deviceList = devices?.ToList() ?? new List<DeviceSource>();
                if (!deviceList.Any())
                {
                    throw new ArgumentException("至少需要选择一个设备", nameof(devices));
                }

                if (string.IsNullOrEmpty(packageName))
                {
                    throw new ArgumentException("游戏包名不能为空", nameof(packageName));
                }

                Log.Write(Level.INFO, $"开始停止游戏 - 设备数量: {deviceList.Count}, 包名: {packageName}");

                var stopTasks = deviceList.Select(device => StopGameOnDeviceAsync(device, packageName));
                var stopResults = await Task.WhenAll(stopTasks);

                foreach (var deviceResult in stopResults)
                {
                    if (deviceResult.Success)
                    {
                        result.SuccessDevices.Add(deviceResult);
                    }
                    else
                    {
                        result.FailedDevices.Add(deviceResult);
                    }
                }

                result.Success = result.SuccessDevices.Any();
                result.TotalDevices = deviceList.Count;
                result.SuccessCount = result.SuccessDevices.Count;
                result.FailedCount = result.FailedDevices.Count;

                Log.Write(Level.INFO, $"游戏停止完成 - 成功: {result.SuccessCount}, 失败: {result.FailedCount}");

                return result;
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"游戏停止异常: {ex.Message}");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 启动单台设备上的游戏（公共方法，供批量和单台调用）
        /// </summary>
        /// <param name="game">要启动的游戏</param>
        /// <param name="device">目标设备</param>
        /// <returns>是否启动成功</returns>
        public async Task<bool> LaunchGameForDeviceAsync(GameSource game, DeviceSource device)
        {
            var result = await LaunchGameAsync(game, new List<DeviceSource> { device });
            if (result.Success)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    device.IsGameRunning = true;
                    device.CurrentGamePackageName = game.PackageName;
                });
                return true;
            }
            return false;
        }

        /// <summary>
        /// 停止单台设备上的游戏（公共方法，供批量和单台调用）
        /// </summary>
        /// <param name="game">要停止的游戏</param>
        /// <param name="device">目标设备</param>
        /// <returns>是否停止成功</returns>
        public async Task<bool> StopGameForDeviceAsync(GameSource game, DeviceSource device)
        {
            var result = await StopGameAsync(new List<DeviceSource> { device }, game.PackageName);
            if (result.Success)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    device.IsGameRunning = false;
                    device.CurrentGamePackageName = null;
                    Log.Write(Level.INFO, $"[UI] 设备{device.SerialNumber}游戏未启动，时间: {DateTime.Now:HH:mm:ss.fff}");
                });
                return true;
            }
            return false;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// 启动游戏到指定设备
        /// </summary>
        /// <param name="game">要启动的游戏</param>
        /// <param name="device">目标设备</param>
        /// <returns>启动结果</returns>
        private async Task<DeviceLaunchResult> LaunchGameToDeviceAsync(GameSource game, DeviceSource device)
        {
            var result = new DeviceLaunchResult
            {
                DeviceId = device.ID,
                SerialNumber = device.SerialNumber,
                Success = false,
                LaunchTime = DateTime.Now
            };
            
            try
            {
                Log.Write(Level.INFO, $"设备 {device.SerialNumber} 开始启动游戏 {game.Name}");
                
                // 先锁定设备
                int lockResult = _deviceManager.LockDevice(device.SerialNumber);
                if (lockResult != 0)
                {
                    Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 锁定失败，错误码: {lockResult}");
                    result.ErrorMessage = $"设备锁定失败，错误码: {lockResult}";
                    // 分步进度通知（失败）
                    LaunchProgress?.Invoke(this, new GameLaunchProgressEventArgs
                    {
                        DeviceId = device.ID,
                        SerialNumber = device.SerialNumber,
                        GameName = game.Name,
                        Status = $"锁定失败: {lockResult}"
                    });
                    return result;
                }
                
                // 等待设备锁定完成
                await Task.Delay(1000);
                
                // 启动应用
                int sdkResult = _deviceManager.StartPlayApp(device.SerialNumber, game.PackageName);
                
                if (sdkResult == 0)
                {
                    result.Success = true;
                    result.SuccessMessage = "游戏启动成功";
                    Log.Write(Level.INFO, $"设备 {device.SerialNumber} 启动游戏 {game.Name} 成功");

                    // 更新设备状态（DeviceSource属性必须在UI线程访问）
                    Application.Current.Dispatcher.Invoke(() => {
                        device.IsGameRunning = true;
                        device.CurrentGamePackageName = game.PackageName;
                    });
                    Log.Write(Level.DEBUG, $"设备状态更新成功: {device.SerialNumber}");

                    // 分步进度通知（成功）
                    LaunchProgress?.Invoke(this, new GameLaunchProgressEventArgs
                    {
                        DeviceId = device.ID,
                        SerialNumber = device.SerialNumber,
                        GameName = game.Name,
                        Status = "启动成功"
                    });
                }
                else
                {
                    Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 启动游戏 {game.Name} 失败，错误码: {sdkResult}");
                    result.ErrorMessage = $"启动失败，错误码: {sdkResult}";

                    // 确保设备状态为未运行（DeviceSource属性必须在UI线程访问）
                    Application.Current.Dispatcher.Invoke(() => {
                        device.IsGameRunning = false;
                        device.CurrentGamePackageName = null;
                    });

                    // 分步进度通知（失败）
                    LaunchProgress?.Invoke(this, new GameLaunchProgressEventArgs
                    {
                        DeviceId = device.ID,
                        SerialNumber = device.SerialNumber,
                        GameName = game.Name,
                        Status = $"启动失败: {sdkResult}"
                    });
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 启动游戏异常: {ex.Message}");
                result.ErrorMessage = ex.Message;

                // 确保设备状态为未运行（DeviceSource属性必须在UI线程访问）
                try {
                    Application.Current.Dispatcher.Invoke(() => {
                        device.IsGameRunning = false;
                        device.CurrentGamePackageName = null;
                    });
                } catch {}

                // 分步进度通知（异常）
                LaunchProgress?.Invoke(this, new GameLaunchProgressEventArgs
                {
                    DeviceId = device.ID,
                    SerialNumber = device.SerialNumber,
                    GameName = game.Name,
                    Status = $"异常: {ex.Message}"
                });
                
                // 异常时解锁设备
                // try
                // {
                //     _deviceManager.UnlockDevice(device.SerialNumber);
                // }
                // catch (Exception unlockEx)
                // {
                //     Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 解锁异常: {unlockEx.Message}");
                // } // Removed as per edit hint
            }
            
            return result;
        }

        /// <summary>
        /// 停止设备上的游戏
        /// </summary>
        /// <param name="device">目标设备</param>
        /// <param name="packageName">要停止的游戏包名</param>
        /// <returns>停止结果</returns>
        private async Task<DeviceStopResult> StopGameOnDeviceAsync(DeviceSource device, string packageName)
        {
            var result = new DeviceStopResult
            {
                DeviceId = device.ID,
                SerialNumber = device.SerialNumber,
                Success = false,
                StopTime = DateTime.Now
            };
            
            try
            {
                Log.Write(Level.INFO, $"设备 {device.SerialNumber} 开始停止游戏 {packageName}");
                
                // 用 Task.Run 包裹同步阻塞操作，实现异步
                int sdkResult = await Task.Run(() => _deviceManager.StopPlayApp(device.SerialNumber, packageName));
                
                if (sdkResult == 0)
                {
                    result.Success = true;
                    result.SuccessMessage = "游戏停止成功";
                    Log.Write(Level.INFO, $"设备 {device.SerialNumber} 停止游戏 {packageName} 成功");

                    // 在UI线程中更新设备状态
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        device.IsGameRunning = false;
                        device.CurrentGamePackageName = null;
                    });
                }
                else
                {
                    Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 停止游戏 {packageName} 失败，错误码: {sdkResult}");
                    result.ErrorMessage = $"停止失败，错误码: {sdkResult}";

                    // 停止失败时，保持当前状态不变（不强制修改）
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"设备 {device.SerialNumber} 停止游戏异常: {ex.Message}");
                result.ErrorMessage = ex.Message;

                // 异常时，尝试确保设备状态为未运行
                try
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        device.IsGameRunning = false;
                        device.CurrentGamePackageName = null;
                    });
                }
                catch (Exception dispatcherEx)
                {
                    Log.Write(Level.ERROR, $"更新设备状态失败: {dispatcherEx.Message}");
                }
            }
            
            return result;
        }

        #endregion
    }

    #region Event Args Classes

    /// <summary>
    /// 游戏启动进度事件参数
    /// </summary>
    public class GameLaunchProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int DeviceId { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 游戏名称
        /// </summary>
        public string GameName { get; set; }

        /// <summary>
        /// 当前状态
        /// </summary>
        public string Status { get; set; }
    }

    /// <summary>
    /// 游戏启动结果事件参数
    /// </summary>
    public class GameLaunchResultEventArgs : EventArgs
    {
        /// <summary>
        /// 启动结果
        /// </summary>
        public GameLaunchResult Result { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="result">启动结果</param>
        public GameLaunchResultEventArgs(GameLaunchResult result)
        {
            Result = result;
        }
    }

    #endregion

    #region Result Classes

    /// <summary>
    /// 游戏启动结果
    /// </summary>
    public class GameLaunchResult
    {
        /// <summary>
        /// 游戏ID
        /// </summary>
        public int GameId { get; set; }

        /// <summary>
        /// 游戏名称
        /// </summary>
        public string GameName { get; set; }

        /// <summary>
        /// 游戏包名
        /// </summary>
        public string PackageName { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime LaunchTime { get; set; }

        /// <summary>
        /// 总设备数
        /// </summary>
        public int TotalDevices { get; set; }

        /// <summary>
        /// 成功设备数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败设备数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 成功启动的设备列表
        /// </summary>
        public List<DeviceLaunchResult> SuccessDevices { get; set; } = new List<DeviceLaunchResult>();

        /// <summary>
        /// 启动失败的设备列表
        /// </summary>
        public List<DeviceLaunchResult> FailedDevices { get; set; } = new List<DeviceLaunchResult>();
    }

    /// <summary>
    /// 设备启动结果
    /// </summary>
    public class DeviceLaunchResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int DeviceId { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 成功信息
        /// </summary>
        public string SuccessMessage { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime LaunchTime { get; set; }
    }

    /// <summary>
    /// 游戏停止结果
    /// </summary>
    public class GameStopResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime StopTime { get; set; }

        /// <summary>
        /// 总设备数
        /// </summary>
        public int TotalDevices { get; set; }

        /// <summary>
        /// 成功设备数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败设备数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 成功停止的设备列表
        /// </summary>
        public List<DeviceStopResult> SuccessDevices { get; set; } = new List<DeviceStopResult>();

        /// <summary>
        /// 停止失败的设备列表
        /// </summary>
        public List<DeviceStopResult> FailedDevices { get; set; } = new List<DeviceStopResult>();
    }

    /// <summary>
    /// 设备停止结果
    /// </summary>
    public class DeviceStopResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int DeviceId { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 成功信息
        /// </summary>
        public string SuccessMessage { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime StopTime { get; set; }
    }

    #endregion
}