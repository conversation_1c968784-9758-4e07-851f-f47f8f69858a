﻿using Jskj.XRSystem.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using XRSvc.CustomControl;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 游戏列表, key=GameID
        /// </summary>
        private readonly Dictionary<int, GameInfo> GameList;

        public ObservableCollection<GameInfo> GameListValues { get; set; }

        public MainWindow()
        {
            InitializeComponent();
        }

        public MainWindow(List<GameInfo> gameList)
        {
            this.GameList = gameList.ToDictionary(g => g.ID, g => g);
            this.GameListValues = new ObservableCollection<GameInfo>(gameList);
            DataContext = this;

            InitializeComponent();
            InitizlizeeCustomComponent();
        }

        private void InitizlizeeCustomComponent()
        {
            //1、主窗口设置了透明，并且清除了背景色
            //2、主窗口默认是不显示的，根据配置参数布局完成后才显示

            //清除当前的资源加载项，运行时使用程序入口统一的资源
            this.Resources.MergedDictionaries.Clear();

            // 创建游戏列表
            GroupBox groupBox_games = new GroupBox
            {
                Header = "游戏列表",
                Height = 60,
                Margin = new Thickness(10, 5, 10, 0),
                Padding = new Thickness(5)
            };

            WrapPanel wrapPanel = new WrapPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Left,
                VerticalAlignment = VerticalAlignment.Center
            };

            foreach (var gameInfo in GameListValues)
            {
                GameStyle gameStyle = new GameStyle
                {
                    DataContext = gameInfo,
                    Margin = new Thickness(5)
                };
                wrapPanel.Children.Add(gameStyle);
            }

            groupBox_games.Content = wrapPanel;

            // 添加到主界面
            if (FindName("mainStackPanel") is StackPanel mainStackPanel)
            {
                mainStackPanel.Children.Add(groupBox_games);
            }
        }
    }
}

