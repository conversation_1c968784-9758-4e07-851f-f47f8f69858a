﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XRSvc.DataPack;

namespace Jskj.XRSvc.Config
{

    public class Setting
    {
        /// <summary>
        /// 设备服务端列表
        /// </summary>
        public List<DeviceServerInfo> DeviceServers { get; set; } = new List<DeviceServerInfo>();

        /// <summary>
        /// 设备信息列表
        /// </summary>
        public List<DeviceInfo> DeviceList { get; set; } = new List<DeviceInfo>();

        /// <summary>
        /// 游戏信息列表
        /// </summary>
        public List<GameInfo> GameList { get; set; } = new List<GameInfo>();

        /// <summary>
        /// 上一次选择的游戏ID
        /// </summary>
        public int LastSelectedGameId { get; set; }

        /// <summary>
        /// 上一次选择的设备管理服务器Tag
        /// </summary>
        public string LastSelectedDeviceServerTag { get; set; }

        /// <summary>
        /// 生成16位大写字母+数字的随机字符串
        /// </summary>
        private string GenerateRandomSN()
        {
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random(Guid.NewGuid().GetHashCode());
            return new string(Enumerable.Repeat(chars, 16)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        /// <summary>
        /// 初始化默认配置
        /// </summary>
        public void InitDefault()
        {
            // 初始化默认设备服务端列表
            DeviceServers = new List<DeviceServerInfo>
            {
                new DeviceServerInfo { Name = "PICO企业套件", Tag = "10001", IsOnline = false },
                new DeviceServerInfo { Name = "JS控制", Tag = "10002", IsOnline = false }
            };

            // 初始化默认设备列表
                DeviceList = new List<DeviceInfo>
            {
                new DeviceInfo { ID = 8001, SerialNumber = GenerateRandomSN(), Name = "1号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8002, SerialNumber = GenerateRandomSN(), Name = "2号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8003, SerialNumber = GenerateRandomSN(), Name = "3号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8004, SerialNumber = GenerateRandomSN(), Name = "4号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8005, SerialNumber = GenerateRandomSN(), Name = "5号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8006, SerialNumber = GenerateRandomSN(), Name = "6号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8007, SerialNumber = GenerateRandomSN(), Name = "7号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8008, SerialNumber = GenerateRandomSN(), Name = "8号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8009, SerialNumber = GenerateRandomSN(), Name = "9号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8010, SerialNumber = GenerateRandomSN(), Name = "10号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8011, SerialNumber = GenerateRandomSN(), Name = "11号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8012, SerialNumber = GenerateRandomSN(), Name = "12号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8013, SerialNumber = GenerateRandomSN(), Name = "13号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8014, SerialNumber = GenerateRandomSN(), Name = "14号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8015, SerialNumber = GenerateRandomSN(), Name = "15号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                // 新增ID为8101~8115，Name为1号机~15号机的设备
                new DeviceInfo { ID = 8101, SerialNumber = GenerateRandomSN(), Name = "1号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8102, SerialNumber = GenerateRandomSN(), Name = "2号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8103, SerialNumber = GenerateRandomSN(), Name = "3号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8104, SerialNumber = GenerateRandomSN(), Name = "4号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8105, SerialNumber = GenerateRandomSN(), Name = "5号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8106, SerialNumber = GenerateRandomSN(), Name = "6号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8107, SerialNumber = GenerateRandomSN(), Name = "7号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8108, SerialNumber = GenerateRandomSN(), Name = "8号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8109, SerialNumber = GenerateRandomSN(), Name = "9号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8110, SerialNumber = GenerateRandomSN(), Name = "10号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8111, SerialNumber = GenerateRandomSN(), Name = "11号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8112, SerialNumber = GenerateRandomSN(), Name = "12号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8113, SerialNumber = GenerateRandomSN(), Name = "13号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8114, SerialNumber = GenerateRandomSN(), Name = "14号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 8115, SerialNumber = GenerateRandomSN(), Name = "15号机", DeviceType = DeviceType.HMD, IpAddress = "*************", BatteryLevel = 0, IsEnabled = false, IsOnline = false },

                // 外设设备配置 (与Android端保持一致)
                new DeviceInfo { ID = 9001, SerialNumber = "MOTION_PLATFORM_001", Name = "动感平台", DeviceType = DeviceType.MotionPlatform, IpAddress = "*************", BatteryLevel = 100, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 9002, SerialNumber = "FAN_001", Name = "可调速风扇", DeviceType = DeviceType.Fan, IpAddress = "*************", BatteryLevel = 100, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 9003, SerialNumber = "WATER_SPRAY_001", Name = "喷水设备", DeviceType = DeviceType.WaterSpray, IpAddress = "*************", BatteryLevel = 100, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 9004, SerialNumber = "HOTNESS_001", Name = "热感设备", DeviceType = DeviceType.Hotness, IpAddress = "*************", BatteryLevel = 100, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 9005, SerialNumber = "DOOR_MANUAL_001", Name = "可推拉门", DeviceType = DeviceType.DoorManual, IpAddress = "*************", BatteryLevel = 100, IsEnabled = false, IsOnline = false },
                new DeviceInfo { ID = 9006, SerialNumber = "DOOR_CMD_001", Name = "指令控制门", DeviceType = DeviceType.DoorCmdControl, IpAddress = "*************", BatteryLevel = 100, IsEnabled = false, IsOnline = false }
            };

            // 初始化默认游戏列表
            GameList = new List<GameInfo>
            {
                new GameInfo 
                { 
                    ID = 10001, 
                    Name = "唐诡",
                    GameCategoryIndex = 1, 
                    PackageName = "com.fantawildgames.ratitT",
                    GameType = GameType.Video, 
                    VideoFormat = VideoFormat.TYPE_2D, 
                    VideoFilePath = "/唐诡/唐诡.mp4", 
                    Duration = 125, 
                    ShowOrder = 50, 
                    IsShow = true, 
                    IsPlayAction = true, 
                    ActionFilePath = "/唐诡/10001.jdz" 
                },
                new GameInfo 
                { 
                    ID = 10002, 
                    Name = "秦陵", 
                    GameCategoryIndex = 1, 
                    PackageName = "com.fantawildgames.ZombieApocalypse",
                    GameType = GameType.Video, 
                    VideoFormat = VideoFormat.TYPE_2D, 
                    VideoFilePath = "/秦陵/秦陵.mp4", 
                    Duration = 125, 
                    ShowOrder = 50, 
                    IsShow = true, 
                    IsPlayAction = true, 
                    ActionFilePath = "/秦陵/01.jdz" 
                },
                new GameInfo 
                { 
                    ID = 10003, 
                    Name = "长征", 
                    GameCategoryIndex = 1, 
                    PackageName = "com.fantawildgames.slimeT",
                    GameType = GameType.Video, 
                    VideoFormat = VideoFormat.TYPE_2D, 
                    VideoFilePath = "/长征/长征.mp4", 
                    Duration = 125, 
                    ShowOrder = 50, 
                    IsShow = true, 
                    IsPlayAction = true, 
                    ActionFilePath = "/长征/01.jdz" 
                }
            };

            // 初始化默认游戏选择（取第一个游戏ID，若有）
            LastSelectedGameId = GameList.Count > 0 ? GameList[0].ID : 0;
            // 初始化默认设备管理服务器选择（取第一个Tag，若有）
            LastSelectedDeviceServerTag = DeviceServers.Count > 0 ? DeviceServers[0].Tag : string.Empty;
        }
    }
}
