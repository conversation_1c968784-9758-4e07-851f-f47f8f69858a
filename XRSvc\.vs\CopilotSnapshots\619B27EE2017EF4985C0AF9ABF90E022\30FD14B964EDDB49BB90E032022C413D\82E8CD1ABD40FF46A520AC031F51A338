﻿<UserControl x:Class="XRSvc.CustomControl.DeviceStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="60" d:DesignWidth="150">
    <Border BorderBrush="#DDDDDD" BorderThickness="1" CornerRadius="3" Margin="2">
        <Grid Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 设备编号 -->
            <TextBlock Grid.Row="0" Grid.Column="0" 
                       Text="8001" 
                       FontWeight="Bold" 
                       VerticalAlignment="Center"
                       TextTrimming="CharacterEllipsis"/>
            
            <!-- 在线状态指示器 -->
            <Ellipse Grid.Row="0" Grid.Column="1" 
                     Width="12" Height="12" 
                     Margin="5,0,0,0"
                     VerticalAlignment="Center">
                <Ellipse.Style>
                    <Style TargetType="Ellipse">
                        <Setter Property="Fill" Value="Red"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsOnline, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="True">
                                <Setter Property="Fill" Value="Green"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Ellipse.Style>
            </Ellipse>
            
            <!-- SN码 -->
            <TextBlock Grid.Row="1" Grid.Column="0" 
                       Text="SN码：PN1635130635" 
                       FontSize="8" 
                       Foreground="Gray"
                       VerticalAlignment="Center"
                       TextTrimming="CharacterEllipsis"/>
            
            <!-- 电量进度条 -->
            <ProgressBar Grid.Row="1" Grid.Column="1" 
                         Width="40" Height="8" 
                         Margin="5,0,0,0"
                         Value="{Binding BatteryLevel, RelativeSource={RelativeSource AncestorType=UserControl}}"
                         Maximum="100"
                         VerticalAlignment="Center">
                <ProgressBar.Style>
                    <Style TargetType="ProgressBar">
                        <Setter Property="Foreground" Value="Green"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding BatteryLevel, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="0">
                                <Setter Property="Foreground" Value="Red"/>
                            </DataTrigger>
                            <Trigger Property="Value" Value="0">
                                <Setter Property="Foreground" Value="Red"/>
                            </Trigger>
                            <Trigger Property="Value" Value="100">
                                <Setter Property="Foreground" Value="Green"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ProgressBar.Style>
            </ProgressBar>
            
            <!-- 电量百分比显示 -->
            <TextBlock Grid.Row="1" Grid.Column="1" 
                       Text="{Binding BatteryLevel, RelativeSource={RelativeSource AncestorType=UserControl}, StringFormat={}{0}%}" 
                       FontSize="9" 
                       Foreground="Black"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Center"
                       Margin="0,0,0,0"/>
        </Grid>
    </Border>
</UserControl>