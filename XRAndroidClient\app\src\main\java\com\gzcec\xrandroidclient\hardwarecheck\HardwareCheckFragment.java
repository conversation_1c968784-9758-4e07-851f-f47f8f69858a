package com.gzcec.xrandroidclient.hardwarecheck;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.device.DeviceType;
import com.gzcec.xrandroidclient.data.DeviceDataRepository;
import com.gzcec.xrandroidclient.communication.MqttCommunicationManager;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceListResponseMessage;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceStatusUpdateMessage;
import com.gzcec.xrandroidclient.R;

import java.util.ArrayList;
import java.util.List;

public class HardwareCheckFragment extends Fragment implements
    MqttCommunicationManager.DeviceStatusListener {

    private static final String TAG = "HardwareCheckFragment";

    private RecyclerView recyclerView;
    private HardwareCheckDeviceAdapter deviceAdapter;
    private List<DeviceInfo> deviceList;
    private DeviceDataRepository deviceRepository;
    private Handler mainHandler;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "创建HardwareCheckFragment视图");

        View view = inflater.inflate(R.layout.fragment_hardware_check, container, false);

        // 初始化Handler
        mainHandler = new Handler(Looper.getMainLooper());

        // 获取设备数据仓库
        deviceRepository = DeviceDataRepository.getInstance();

        // 初始化UI组件
        initViews(view);

        // 设置数据绑定
        setupDataBinding();

        // 注册到MQTT通信管理器
        MqttCommunicationManager.getInstance().addDeviceStatusListener(this);

        return view;
    }

    /**
     * 初始化UI组件
     */
    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        deviceList = new ArrayList<>();
        deviceAdapter = new HardwareCheckDeviceAdapter(deviceList);
        recyclerView.setAdapter(deviceAdapter);

        Button btnAllStart = view.findViewById(R.id.btn_all_start);
        Button btnAllStop = view.findViewById(R.id.btn_all_stop);

        btnAllStart.setOnClickListener(v -> {
            Log.d(TAG, "启动所有硬件设备");
            // 这里可以发送MQTT消息给PC端，启动所有硬件设备
            // TODO: 实现硬件设备启动逻辑
            for (DeviceInfo device : deviceList) {
                device.setInGame(true);
            }
            deviceAdapter.notifyDataSetChanged();
        });

        btnAllStop.setOnClickListener(v -> {
            Log.d(TAG, "停止所有硬件设备");
            // 这里可以发送MQTT消息给PC端，停止所有硬件设备
            // TODO: 实现硬件设备停止逻辑
            for (DeviceInfo device : deviceList) {
                device.setInGame(false);
            }
            deviceAdapter.notifyDataSetChanged();
        });
    }

    /**
     * 设置数据绑定
     */
    private void setupDataBinding() {
        Log.d(TAG, "设置硬件设备数据绑定");

        // 观察硬件设备列表
        deviceRepository.getHardwareDevicesLiveData().observe(this, new Observer<List<DeviceInfo>>() {
            @Override
            public void onChanged(List<DeviceInfo> hardwareDevices) {
                if (hardwareDevices != null) {
                    Log.d(TAG, "数据绑定更新: 收到 " + hardwareDevices.size() + " 台硬件设备");

                    // 更新本地设备列表
                    deviceList.clear();
                    deviceList.addAll(hardwareDevices);

                    // 通知适配器数据变化
                    if (deviceAdapter != null) {
                        deviceAdapter.notifyDataSetChanged();
                    }

                    Log.d(TAG, "硬件设备列表UI已更新");
                }
            }
        });

        // 观察设备统计信息
        deviceRepository.getStatisticsLiveData().observe(this, new Observer<DeviceDataRepository.DeviceStatistics>() {
            @Override
            public void onChanged(DeviceDataRepository.DeviceStatistics statistics) {
                if (statistics != null) {
                    Log.d(TAG, "设备统计更新: " + statistics.toString());

                    // 可以在这里更新UI显示硬件设备统计信息
                    // 例如：更新标题栏的硬件设备数量等
                }
            }
        });
    }



    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "HardwareCheckFragment销毁");

        // 从MQTT通信管理器中移除监听器
        MqttCommunicationManager.getInstance().removeDeviceStatusListener(this);
    }

    // ========== MqttCommunicationManager.DeviceStatusListener实现 ==========

    @Override
    public void onDeviceListResponse(DeviceListResponseMessage response) {
        mainHandler.post(() -> {
            Log.d(TAG, "收到设备列表更新: " + response.getTotalCount() + " 台设备");
            // 数据已通过DeviceDataRepository自动更新，这里只需要记录日志
        });
    }

    @Override
    public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update) {
        mainHandler.post(() -> {
            Log.d(TAG, "收到设备状态更新: " + update.getDevices().size() + " 台设备");
            // 数据已通过DeviceDataRepository自动更新，这里只需要记录日志
        });
    }
}