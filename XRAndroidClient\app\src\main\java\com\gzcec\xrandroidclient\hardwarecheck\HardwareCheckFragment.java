package com.gzcec.xrandroidclient.hardwarecheck;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.device.DeviceType;
import com.gzcec.xrandroidclient.data.DeviceDataRepository;
import com.gzcec.xrandroidclient.communication.MqttCommunicationManager;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceListResponseMessage;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceStatusUpdateMessage;
import com.gzcec.xrandroidclient.communication.messages.device.DevicePingResponseMessage;
import com.gzcec.xrandroidclient.device.DeviceControlManager;
import com.gzcec.xrandroidclient.R;

import java.util.ArrayList;
import java.util.List;

public class HardwareCheckFragment extends Fragment implements
    MqttCommunicationManager.DeviceStatusListener {

    private static final String TAG = "HardwareCheckFragment";

    private RecyclerView recyclerView;
    private HardwareCheckDeviceAdapter deviceAdapter;
    private List<DeviceInfo> deviceList;
    private DeviceDataRepository deviceRepository;
    private Handler mainHandler;
    private DeviceControlManager deviceControlManager;
    private MqttCommunicationManager mqttManager;

    // 定期Ping检测相关
    private static final long PING_INTERVAL_MS = 30000; // 30秒检测一次
    private Runnable pingRunnable;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "创建HardwareCheckFragment视图");

        View view = inflater.inflate(R.layout.fragment_hardware_check, container, false);

        // 初始化Handler
        mainHandler = new Handler(Looper.getMainLooper());

        // 获取设备数据仓库
        deviceRepository = DeviceDataRepository.getInstance();

        // 获取MQTT通信管理器
        mqttManager = MqttCommunicationManager.getInstance(getContext());

        // 初始化设备控制管理器
        deviceControlManager = new DeviceControlManager(getContext(), mqttManager);

        // 初始化UI组件
        initViews(view);

        // 设置数据绑定
        setupDataBinding();

        // 启动定期Ping检测
        startPeriodicPing();

        // 注册到MQTT通信管理器
        MqttCommunicationManager.getInstance().addDeviceStatusListener(this);

        return view;
    }

    /**
     * 初始化UI组件
     */
    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        deviceList = new ArrayList<>();
        deviceAdapter = new HardwareCheckDeviceAdapter(deviceList);
        recyclerView.setAdapter(deviceAdapter);

        Button btnAllStart = view.findViewById(R.id.btn_all_start);
        Button btnAllStop = view.findViewById(R.id.btn_all_stop);
        Button btnPingDevices = view.findViewById(R.id.btn_ping_devices);

        btnAllStart.setOnClickListener(v -> {
            Log.d(TAG, "启动所有硬件设备");
            enableAllHardwareDevices();
        });

        btnAllStop.setOnClickListener(v -> {
            Log.d(TAG, "停止所有硬件设备");
            disableAllHardwareDevices();
        });

        btnPingDevices.setOnClickListener(v -> {
            Log.d(TAG, "手动检测设备状态");
            pingAllDevices();
        });
    }

    /**
     * 设置数据绑定
     */
    private void setupDataBinding() {
        Log.d(TAG, "设置硬件设备数据绑定");

        // 观察硬件设备列表
        deviceRepository.getHardwareDevicesLiveData().observe(this, new Observer<List<DeviceInfo>>() {
            @Override
            public void onChanged(List<DeviceInfo> hardwareDevices) {
                if (hardwareDevices != null) {
                    Log.d(TAG, "数据绑定更新: 收到 " + hardwareDevices.size() + " 台硬件设备");

                    // 更新本地设备列表
                    deviceList.clear();
                    deviceList.addAll(hardwareDevices);

                    // 通知适配器数据变化
                    if (deviceAdapter != null) {
                        deviceAdapter.notifyDataSetChanged();
                    }

                    Log.d(TAG, "硬件设备列表UI已更新");
                }
            }
        });

        // 观察设备统计信息
        deviceRepository.getStatisticsLiveData().observe(this, new Observer<DeviceDataRepository.DeviceStatistics>() {
            @Override
            public void onChanged(DeviceDataRepository.DeviceStatistics statistics) {
                if (statistics != null) {
                    Log.d(TAG, "设备统计更新: " + statistics.toString());

                    // 可以在这里更新UI显示硬件设备统计信息
                    // 例如：更新标题栏的硬件设备数量等
                }
            }
        });
    }



    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "HardwareCheckFragment销毁");

        // 从MQTT通信管理器中移除监听器
        MqttCommunicationManager.getInstance().removeDeviceStatusListener(this);
    }

    // ========== MqttCommunicationManager.DeviceStatusListener实现 ==========

    @Override
    public void onDeviceListResponse(DeviceListResponseMessage response) {
        mainHandler.post(() -> {
            Log.d(TAG, "收到设备列表更新: " + response.getTotalCount() + " 台设备");
            // 数据已通过DeviceDataRepository自动更新，这里只需要记录日志
        });
    }

    @Override
    public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update) {
        mainHandler.post(() -> {
            Log.d(TAG, "收到设备状态更新: " + update.getDevices().size() + " 台设备");
            // 数据已通过DeviceDataRepository自动更新，这里只需要记录日志
        });
    }

    /**
     * 启用所有硬件设备
     */
    private void enableAllHardwareDevices() {
        if (deviceList == null || deviceList.isEmpty()) {
            Log.w(TAG, "没有硬件设备可以启用");
            return;
        }

        Log.i(TAG, "开始启用所有硬件设备，共 " + deviceList.size() + " 台");

        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HARDWARE) {
                String deviceId = String.valueOf(device.getId());
                deviceControlManager.enableDevice(deviceId, new DeviceControlManager.DeviceControlCallback() {
                    @Override
                    public void onSuccess(String deviceId, String action) {
                        mainHandler.post(() -> {
                            Log.i(TAG, "设备 " + deviceId + " 启用成功");
                            // 更新本地设备状态
                            updateLocalDeviceStatus(deviceId, true);
                        });
                    }

                    @Override
                    public void onError(String errorMessage) {
                        mainHandler.post(() -> {
                            Log.e(TAG, "设备 " + deviceId + " 启用失败: " + errorMessage);
                        });
                    }
                });
            }
        }
    }

    /**
     * 禁用所有硬件设备
     */
    private void disableAllHardwareDevices() {
        if (deviceList == null || deviceList.isEmpty()) {
            Log.w(TAG, "没有硬件设备可以禁用");
            return;
        }

        Log.i(TAG, "开始禁用所有硬件设备，共 " + deviceList.size() + " 台");

        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == DeviceType.HARDWARE) {
                String deviceId = String.valueOf(device.getId());
                deviceControlManager.disableDevice(deviceId, new DeviceControlManager.DeviceControlCallback() {
                    @Override
                    public void onSuccess(String deviceId, String action) {
                        mainHandler.post(() -> {
                            Log.i(TAG, "设备 " + deviceId + " 禁用成功");
                            // 更新本地设备状态
                            updateLocalDeviceStatus(deviceId, false);
                        });
                    }

                    @Override
                    public void onError(String errorMessage) {
                        mainHandler.post(() -> {
                            Log.e(TAG, "设备 " + deviceId + " 禁用失败: " + errorMessage);
                        });
                    }
                });
            }
        }
    }

    /**
     * 检测所有设备在线状态
     */
    private void pingAllDevices() {
        Log.i(TAG, "开始检测所有设备在线状态");

        deviceControlManager.pingAllDevices(new DeviceControlManager.DevicePingCallback() {
            @Override
            public void onPingResult(List<DevicePingResponseMessage.DevicePingResult> results) {
                mainHandler.post(() -> {
                    Log.i(TAG, "设备Ping检测完成，共检测 " + results.size() + " 台设备");

                    // 更新设备在线状态
                    for (DevicePingResponseMessage.DevicePingResult result : results) {
                        updateDeviceOnlineStatus(result.getDeviceId(), result.isOnline());

                        if (result.isOnline()) {
                            Log.d(TAG, "设备 " + result.getDeviceId() + " 在线，响应时间: " + result.getResponseTime() + "ms");
                        } else {
                            Log.d(TAG, "设备 " + result.getDeviceId() + " 离线: " + result.getErrorMessage());
                        }
                    }

                    // 刷新UI
                    if (deviceAdapter != null) {
                        deviceAdapter.notifyDataSetChanged();
                    }
                });
            }

            @Override
            public void onError(String errorMessage) {
                mainHandler.post(() -> {
                    Log.e(TAG, "设备Ping检测失败: " + errorMessage);
                });
            }
        });
    }

    /**
     * 更新本地设备状态
     */
    private void updateLocalDeviceStatus(String deviceId, boolean enabled) {
        try {
            int id = Integer.parseInt(deviceId);
            for (DeviceInfo device : deviceList) {
                if (device.getId() == id) {
                    device.setEnabled(enabled);
                    break;
                }
            }

            if (deviceAdapter != null) {
                deviceAdapter.notifyDataSetChanged();
            }
        } catch (NumberFormatException e) {
            Log.e(TAG, "无效的设备ID: " + deviceId);
        }
    }

    /**
     * 更新设备在线状态
     */
    private void updateDeviceOnlineStatus(String deviceId, boolean online) {
        try {
            int id = Integer.parseInt(deviceId);
            for (DeviceInfo device : deviceList) {
                if (device.getId() == id) {
                    device.setOnline(online);
                    break;
                }
            }
        } catch (NumberFormatException e) {
            Log.e(TAG, "无效的设备ID: " + deviceId);
        }
    }

    /**
     * 启动定期Ping检测
     */
    private void startPeriodicPing() {
        if (pingRunnable != null) {
            mainHandler.removeCallbacks(pingRunnable);
        }

        pingRunnable = new Runnable() {
            @Override
            public void run() {
                // 只有在Fragment可见且MQTT连接正常时才执行Ping检测
                if (isVisible() && mqttManager != null && mqttManager.isConnected()) {
                    pingAllDevices();
                }

                // 安排下次检测
                mainHandler.postDelayed(this, PING_INTERVAL_MS);
            }
        };

        // 延迟5秒后开始第一次检测，给MQTT连接一些时间
        mainHandler.postDelayed(pingRunnable, 5000);
        Log.d(TAG, "定期Ping检测已启动，间隔: " + PING_INTERVAL_MS + "ms");
    }

    /**
     * 停止定期Ping检测
     */
    private void stopPeriodicPing() {
        if (pingRunnable != null && mainHandler != null) {
            mainHandler.removeCallbacks(pingRunnable);
            pingRunnable = null;
            Log.d(TAG, "定期Ping检测已停止");
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 停止定期Ping检测
        stopPeriodicPing();

        // 取消注册MQTT监听器
        if (mqttManager != null) {
            mqttManager.removeDeviceStatusListener(this);
        }

        Log.d(TAG, "HardwareCheckFragment视图已销毁");
    }

    @Override
    public void onPause() {
        super.onPause();
        // Fragment不可见时停止Ping检测以节省资源
        stopPeriodicPing();
    }

    @Override
    public void onResume() {
        super.onResume();
        // Fragment可见时重新启动Ping检测
        startPeriodicPing();
    }
}