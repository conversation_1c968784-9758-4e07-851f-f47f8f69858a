using System;

namespace XRSvc.Communication
{
    /// <summary>
    /// MQTT主题定义
    /// </summary>
    public static class MqttTopics
    {
        #region 基础主题
        
        /// <summary>
        /// 根主题
        /// </summary>
        public const string ROOT = "xr_system";
        
        /// <summary>
        /// 游戏控制主题根路径
        /// </summary>
        public const string GAME_CONTROL = ROOT + "/game_control";
        
        /// <summary>
        /// 设备管理主题根路径
        /// </summary>
        public const string DEVICE_MANAGEMENT = ROOT + "/device_management";
        
        /// <summary>
        /// 系统状态主题根路径
        /// </summary>
        public const string SYSTEM_STATUS = ROOT + "/system_status";
        
        #endregion

        #region 游戏控制主题
        
        /// <summary>
        /// 游戏列表请求 - Android端发布
        /// 消息格式: GameListRequest
        /// </summary>
        public const string GAME_LIST_REQUEST = GAME_CONTROL + "/list_request";

        /// <summary>
        /// 游戏列表响应 - PC端发布
        /// 消息格式: GameListResponse
        /// </summary>
        public const string GAME_LIST_RESPONSE = GAME_CONTROL + "/list_response";

        /// <summary>
        /// 游戏启动请求 - Android端发布
        /// 消息格式: GameStartRequest
        /// </summary>
        public const string GAME_START_REQUEST = GAME_CONTROL + "/start_request";

        /// <summary>
        /// 游戏启动响应 - PC端发布
        /// 消息格式: GameStartResponse
        /// </summary>
        public const string GAME_START_RESPONSE = GAME_CONTROL + "/start_response";

        /// <summary>
        /// 游戏停止请求 - Android端发布
        /// 消息格式: GameStopRequest
        /// </summary>
        public const string GAME_STOP_REQUEST = GAME_CONTROL + "/stop_request";

        /// <summary>
        /// 游戏停止响应 - PC端发布
        /// 消息格式: GameStopResponse
        /// </summary>
        public const string GAME_STOP_RESPONSE = GAME_CONTROL + "/stop_response";
        
        /// <summary>
        /// 游戏进度更新 - PC端发布
        /// 消息格式: GameProgressUpdate
        /// </summary>
        public const string GAME_PROGRESS_UPDATE = GAME_CONTROL + "/progress_update";
        
        /// <summary>
        /// 游戏服务器状态 - PC端发布
        /// 消息格式: GameServerStatus
        /// </summary>
        public const string GAME_SERVER_STATUS = GAME_CONTROL + "/server_status";
        
        #endregion

        #region 设备管理主题
        
        /// <summary>
        /// 设备列表请求 - Android端发布
        /// 消息格式: DeviceListRequest
        /// </summary>
        public const string DEVICE_LIST_REQUEST = DEVICE_MANAGEMENT + "/list_request";
        
        /// <summary>
        /// 设备列表响应 - PC端发布
        /// 消息格式: DeviceListResponse
        /// </summary>
        public const string DEVICE_LIST_RESPONSE = DEVICE_MANAGEMENT + "/list_response";
        
        /// <summary>
        /// 设备状态更新 - PC端发布
        /// 消息格式: DeviceStatusUpdate
        /// </summary>
        public const string DEVICE_STATUS_UPDATE = DEVICE_MANAGEMENT + "/status_update";
        
        /// <summary>
        /// 设备选择变更 - Android端发布
        /// 消息格式: DeviceSelectionChanged
        /// </summary>
        public const string DEVICE_SELECTION_CHANGED = DEVICE_MANAGEMENT + "/selection_changed";
        
        /// <summary>
        /// 设备电池状态 - PC端发布
        /// 消息格式: DeviceBatteryStatus
        /// </summary>
        public const string DEVICE_BATTERY_STATUS = DEVICE_MANAGEMENT + "/battery_status";
        
        #endregion

        #region 系统状态主题
        
        /// <summary>
        /// 心跳消息 - 双向
        /// 消息格式: HeartbeatMessage
        /// </summary>
        public const string HEARTBEAT = SYSTEM_STATUS + "/heartbeat";
        
        /// <summary>
        /// 系统错误 - PC端发布
        /// 消息格式: SystemError
        /// </summary>
        public const string SYSTEM_ERROR = SYSTEM_STATUS + "/error";
        
        /// <summary>
        /// 连接状态 - 双向
        /// 消息格式: ConnectionStatus
        /// </summary>
        public const string CONNECTION_STATUS = SYSTEM_STATUS + "/connection";
        
        /// <summary>
        /// 系统日志 - PC端发布
        /// 消息格式: SystemLog
        /// </summary>
        public const string SYSTEM_LOG = SYSTEM_STATUS + "/log";
        
        #endregion

        #region 主题工具方法
        
        /// <summary>
        /// 构建设备特定主题
        /// </summary>
        /// <param name="baseTopic">基础主题</param>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备特定主题</returns>
        public static string BuildDeviceTopic(string baseTopic, string deviceId)
        {
            return $"{baseTopic}/device/{deviceId}";
        }
        
        /// <summary>
        /// 构建游戏特定主题
        /// </summary>
        /// <param name="baseTopic">基础主题</param>
        /// <param name="gameId">游戏ID</param>
        /// <returns>游戏特定主题</returns>
        public static string BuildGameTopic(string baseTopic, string gameId)
        {
            return $"{baseTopic}/game/{gameId}";
        }
        
        /// <summary>
        /// 构建客户端特定主题
        /// </summary>
        /// <param name="baseTopic">基础主题</param>
        /// <param name="clientId">客户端ID</param>
        /// <returns>客户端特定主题</returns>
        public static string BuildClientTopic(string baseTopic, string clientId)
        {
            return $"{baseTopic}/client/{clientId}";
        }
        
        /// <summary>
        /// 解析主题获取设备ID
        /// </summary>
        /// <param name="topic">完整主题</param>
        /// <returns>设备ID，如果解析失败返回null</returns>
        public static string ExtractDeviceId(string topic)
        {
            if (string.IsNullOrEmpty(topic)) return null;
            
            var parts = topic.Split('/');
            for (int i = 0; i < parts.Length - 1; i++)
            {
                if (parts[i] == "device")
                {
                    return parts[i + 1];
                }
            }
            return null;
        }
        
        /// <summary>
        /// 解析主题获取游戏ID
        /// </summary>
        /// <param name="topic">完整主题</param>
        /// <returns>游戏ID，如果解析失败返回null</returns>
        public static string ExtractGameId(string topic)
        {
            if (string.IsNullOrEmpty(topic)) return null;
            
            var parts = topic.Split('/');
            for (int i = 0; i < parts.Length - 1; i++)
            {
                if (parts[i] == "game")
                {
                    return parts[i + 1];
                }
            }
            return null;
        }
        
        /// <summary>
        /// 解析主题获取客户端ID
        /// </summary>
        /// <param name="topic">完整主题</param>
        /// <returns>客户端ID，如果解析失败返回null</returns>
        public static string ExtractClientId(string topic)
        {
            if (string.IsNullOrEmpty(topic)) return null;
            
            var parts = topic.Split('/');
            for (int i = 0; i < parts.Length - 1; i++)
            {
                if (parts[i] == "client")
                {
                    return parts[i + 1];
                }
            }
            return null;
        }
        
        /// <summary>
        /// 验证主题格式是否正确
        /// </summary>
        /// <param name="topic">主题</param>
        /// <returns>是否有效</returns>
        public static bool IsValidTopic(string topic)
        {
            if (string.IsNullOrEmpty(topic)) return false;
            if (!topic.StartsWith(ROOT)) return false;
            
            // 检查主题层级不超过10层
            var parts = topic.Split('/');
            return parts.Length <= 10;
        }
        
        /// <summary>
        /// 获取所有需要订阅的主题列表（PC端）
        /// </summary>
        /// <returns>主题列表</returns>
        public static string[] GetPCSubscriptionTopics()
        {
            return new[]
            {
                GAME_LIST_REQUEST,
                GAME_START_REQUEST,
                GAME_STOP_REQUEST,
                DEVICE_LIST_REQUEST,
                DEVICE_SELECTION_CHANGED,
                HEARTBEAT,
                CONNECTION_STATUS
            };
        }
        
        /// <summary>
        /// 获取所有需要订阅的主题列表（Android端）
        /// </summary>
        /// <returns>主题列表</returns>
        public static string[] GetAndroidSubscriptionTopics()
        {
            return new[]
            {
                GAME_LIST_RESPONSE,
                GAME_START_RESPONSE,
                GAME_STOP_RESPONSE,
                GAME_PROGRESS_UPDATE,
                GAME_SERVER_STATUS,
                DEVICE_LIST_RESPONSE,
                DEVICE_STATUS_UPDATE,
                DEVICE_BATTERY_STATUS,
                SYSTEM_ERROR,
                HEARTBEAT,
                CONNECTION_STATUS,
                SYSTEM_LOG
            };
        }
        
        #endregion
    }
    
    /// <summary>
    /// MQTT消息质量等级
    /// </summary>
    public enum MqttQosLevel
    {
        /// <summary>
        /// 最多一次传递
        /// </summary>
        AtMostOnce = 0,
        
        /// <summary>
        /// 至少一次传递
        /// </summary>
        AtLeastOnce = 1,
        
        /// <summary>
        /// 恰好一次传递
        /// </summary>
        ExactlyOnce = 2
    }
    
    /// <summary>
    /// 主题配置
    /// </summary>
    public class TopicConfig
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Topic { get; set; }
        
        /// <summary>
        /// 服务质量等级
        /// </summary>
        public MqttQosLevel QosLevel { get; set; }
        
        /// <summary>
        /// 是否保留消息
        /// </summary>
        public bool Retain { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        
        public TopicConfig(string topic, MqttQosLevel qosLevel = MqttQosLevel.AtLeastOnce, bool retain = false, string description = "")
        {
            Topic = topic;
            QosLevel = qosLevel;
            Retain = retain;
            Description = description;
        }
    }
}
