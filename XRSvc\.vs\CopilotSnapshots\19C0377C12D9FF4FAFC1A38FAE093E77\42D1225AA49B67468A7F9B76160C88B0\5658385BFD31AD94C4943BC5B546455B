﻿using MQTTnet;
using MQTTnet.Client;
using System;
using System.Text;
using System.Threading.Tasks;
using Jskj.AppLog;
using MQTTnet.Server;
using MQTTnet.Protocol;
using System.Collections.Generic;
using System.Threading;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using XRSvc.Communication;
using XRSvc.Services;
using CommDeviceStopResult = XRSvc.Communication.DeviceStopResult;
using XRSvc.ViewModels;
using System.Linq;
using XRSvc.DataPack;
using System.Windows;
using XRSvc.DataSource;

namespace Jskj.XRSvc.MQTT
{
    /// <summary>
    /// 增强的MQTT客户端服务，支持游戏控制通信
    /// </summary>
    public class MqttClientService
    {
        #region Fields

        /// <summary>
        /// JSON序列化设置 - 使用小写开头的命名策略
        /// </summary>
        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            Formatting = Formatting.None,
            DateTimeZoneHandling = DateTimeZoneHandling.Local // 新增，确保带时区
        };

        private IMqttClient mqttClient;
        private MqttClientOptions options;
        private readonly GameLaunchService _gameLaunchService;
        private readonly DeviceViewModel _deviceViewModel;
        private readonly GameViewModel _gameViewModel;
        private readonly string _clientId;
        private bool _isConnected = false;

        #endregion

        #region Events

        // 注意：GameStartRequested 和 DeviceListRequested 事件已移除
        // 现在直接在消息处理方法中处理请求，不再使用事件模式

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;

        #endregion

        public MqttClientService(string brokerHost = "127.0.0.1", int brokerPort = 1883,
            GameLaunchService gameLaunchService = null, DeviceViewModel deviceViewModel = null, GameViewModel gameViewModel = null)
        {
            _clientId = $"PCServer_{Environment.MachineName}_{DateTime.Now.Ticks}";
            _gameLaunchService = gameLaunchService;
            _deviceViewModel = deviceViewModel;
            _gameViewModel = gameViewModel;

            mqttClient = new MqttFactory().CreateMqttClient();
            options = new MqttClientOptionsBuilder()
                .WithTcpServer(brokerHost, brokerPort)
                .WithClientId(_clientId)
                .WithCleanSession(true)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(30))
                .Build();
        }

        /// <summary>
        /// 连接到MQTT Broker并订阅相关主题
        /// </summary>
        public async Task ConnectAsync()
        {
            mqttClient.ConnectedAsync += async e =>
            {
                _isConnected = true;
                Log.Write(Level.INFO, $"MQTT客户端已连接: {_clientId}");

                // 订阅PC端需要的主题
                await SubscribeToTopicsAsync();

                // 发布连接状态
                await PublishConnectionStatusAsync(true);

                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusEventArgs(true, "已连接到MQTT Broker"));
            };

            mqttClient.DisconnectedAsync += async e =>
            {
                _isConnected = false;
                Log.Write(Level.INFO, $"MQTT客户端已断开: {_clientId}");
                ConnectionStatusChanged?.Invoke(this, new ConnectionStatusEventArgs(false, "与MQTT Broker断开连接"));
                await Task.CompletedTask;
            };

            mqttClient.ApplicationMessageReceivedAsync += async e =>
            {
                Log.Write(Level.DEBUG, $"PC端收到MQTT消息: 主题={e.ApplicationMessage.Topic}");
                await HandleReceivedMessageAsync(e.ApplicationMessage);
            };

            await mqttClient.ConnectAsync(options);
        }

        /// <summary>
        /// 订阅PC端需要的主题
        /// </summary>
        private async Task SubscribeToTopicsAsync()
        {
            var topics = MqttTopics.GetPCSubscriptionTopics();
            foreach (var topic in topics)
            {
                await SubscribeAsync(topic);
            }
        }

        /// <summary>
        /// 处理接收到的MQTT消息
        /// </summary>
        private async Task HandleReceivedMessageAsync(MqttApplicationMessage message)
        {
            try
            {
                var topic = message.Topic;
                var payload = Encoding.UTF8.GetString(
                    message.PayloadSegment.Array,
                    message.PayloadSegment.Offset,
                    message.PayloadSegment.Count);

                Log.Write(Level.DEBUG, $"收到MQTT消息: 主题={topic}, 内容长度={payload.Length}");

                // 根据主题分发消息
                switch (topic)
                {
                    case MqttTopics.GAME_LIST_REQUEST:
                        await HandleGameListRequestAsync(payload);
                        break;

                    case MqttTopics.GAME_START_REQUEST:
                        await HandleGameStartRequestAsync(payload);
                        break;

                    case MqttTopics.GAME_STOP_REQUEST:
                        await HandleGameStopRequestAsync(payload);
                        break;

                    case MqttTopics.DEVICE_LIST_REQUEST:
                        await HandleDeviceListRequestAsync(payload);
                        break;

                    case MqttTopics.DEVICE_SELECTION_CHANGED:
                        await HandleDeviceSelectionChangedAsync(payload);
                        break;

                    case MqttTopics.HEARTBEAT:
                        await HandleHeartbeatAsync(payload);
                        break;

                    case MqttTopics.CONNECTION_STATUS:
                        await HandleConnectionStatusAsync(payload);
                        break;

                    case MqttTopics.DEVICE_STATUS_UPDATE:
                        await HandleDeviceStatusUpdateAsync(payload);
                        break;

                    default:
                        Log.Write(Level.INFO, $"未处理的MQTT主题: {topic}");
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理MQTT消息失败: {ex.Message}");
            }
        }

        public async Task SubscribeAsync(string topic)
        {
            var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
                .WithTopicFilter(f => { f.WithTopic(topic); })
                .Build();
            await mqttClient.SubscribeAsync(subscribeOptions);
            Log.Write(Level.INFO, $"Subscribed to topic: {topic}");
        }

        public async Task PublishAsync(string topic, string payload)
        {
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(payload)
                .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.ExactlyOnce)
                .WithRetainFlag(false)
                .Build();
            await mqttClient.PublishAsync(message);

            // 对设备相关的大消息进行简化日志输出
            if (topic == "xr_system/device_management/status_update" ||
                topic == "xr_system/device_management/list_response")
            {
                Log.Write(Level.INFO, $"Published message: Topic={topic}, Payload长度={payload.Length}字符");
            }
            else
            {
                Log.Write(Level.INFO, $"Published message: Topic={topic}, Payload={payload}");
            }
        }

        /// <summary>
        /// 处理游戏启动请求
        /// </summary>
        private async Task HandleGameStartRequestAsync(string payload)
        {
            try
            {
                var request = JsonConvert.DeserializeObject<GameStartRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到游戏启动请求: 游戏={request.SelectedGame?.Name}, 设备数量={request.SelectedDeviceIds?.Count}");

                if (_gameLaunchService != null && _deviceViewModel != null && _gameViewModel != null)
                {
                    // 在UI线程中查找游戏和设备
                    GameSource game = null;
                    List<DeviceSource> selectedDevices = null;

                    if (Application.Current?.Dispatcher != null)
                    {
                        try
                        {
                            Log.Write(Level.DEBUG, $"开始在UI线程中查找游戏ID={request.SelectedGame.ID}和设备");

                            // 检查ViewModel是否可用
                            if (_gameViewModel?.Source == null)
                            {
                                Log.Write(Level.ERROR, "GameViewModel.Source为空");
                                await PublishGameStartResponseAsync(request.MessageId, false, "游戏数据未初始化", null);
                                return;
                            }

                            if (_deviceViewModel?.Source == null)
                            {
                                Log.Write(Level.ERROR, "DeviceViewModel.Source为空");
                                await PublishGameStartResponseAsync(request.MessageId, false, "设备数据未初始化", null);
                                return;
                            }

                            // 使用同步调用避免线程切换问题
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                try
                                {
                                    Log.Write(Level.DEBUG, $"在UI线程中查找游戏，总游戏数: {_gameViewModel.Source.Count}");

                                    // 查找游戏
                                    game = _gameViewModel.Source.FirstOrDefault(g => g.ID == request.SelectedGame.ID);
                                    Log.Write(Level.DEBUG, $"游戏查找结果: {(game != null ? $"找到游戏 {game.Name}" : "未找到游戏")}");

                                    // 查找设备
                                    if (game != null)
                                    {
                                        Log.Write(Level.DEBUG, $"在UI线程中查找设备，总设备数: {_deviceViewModel.Source.Count}");
                                        selectedDevices = _deviceViewModel.Source
                                            .Where(d => request.SelectedDeviceIds.Contains(d.ID.ToString()))
                                            .ToList();
                                        Log.Write(Level.DEBUG, $"设备查找结果: 找到 {selectedDevices.Count} 个设备");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Log.Write(Level.ERROR, $"在UI线程中查找游戏和设备失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Log.Write(Level.ERROR, $"调用UI线程失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                            await PublishGameStartResponseAsync(request.MessageId, false, $"UI线程调用失败: {ex.Message}", null);
                            return;
                        }
                    }
                    else
                    {
                        Log.Write(Level.ERROR, "Application.Current.Dispatcher 不可用");
                        await PublishGameStartResponseAsync(request.MessageId, false, "UI线程不可用", null);
                        return;
                    }

                    if (game == null)
                    {
                        await PublishGameStartResponseAsync(request.MessageId, false, "游戏不存在", null);
                        return;
                    }

                    if (selectedDevices == null || !selectedDevices.Any())
                    {
                        await PublishGameStartResponseAsync(request.MessageId, false, "没有选择有效的设备", null);
                        return;
                    }

                    Log.Write(Level.INFO, $"找到游戏: {game.Name}, 设备数量: {selectedDevices.Count}");

                    // 启动游戏
                    var result = await _gameLaunchService.LaunchGameAsync(game, selectedDevices);

                    // 发布响应
                    await PublishGameStartResponseAsync(request.MessageId, result.Success, result.ErrorMessage, result);
                }
                else
                {
                    await PublishGameStartResponseAsync(request.MessageId, false, "服务未初始化", null);
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理游戏启动请求失败: {ex.Message}");
                // 发送错误响应
                try
                {
                    var request = JsonConvert.DeserializeObject<GameStartRequestMessage>(payload);
                    await PublishGameStartResponseAsync(request?.MessageId ?? Guid.NewGuid().ToString(), false, $"处理请求时发生错误: {ex.Message}", null);
                }
                catch
                {
                    // 忽略响应发送失败
                }
            }
        }

        /// <summary>
        /// 处理设备列表请求
        /// </summary>
        private async Task HandleDeviceListRequestAsync(string payload)
        {
            try
            {
                Log.Write(Level.DEBUG, $"开始处理设备列表请求，消息内容: {payload}");
                var request = JsonConvert.DeserializeObject<DeviceListRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到设备列表请求，消息ID: {request?.MessageId}, 发送者: {request?.SenderId}");

                if (_deviceViewModel != null)
                {
                    // 使用Dispatcher在UI线程中访问DeviceViewModel
                    List<DeviceInfo> devices = null;
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        devices = _deviceViewModel.Source
                            .Where(d => !request.OnlineOnly || d.IsOnline)
                            .Select(d => new DeviceInfo
                            {
                                ID = d.ID,
                                SerialNumber = d.SerialNumber,
                                Name = d.Name,
                                IpAddress = d.IP,
                                IsOnline = d.IsOnline,
                                BatteryLevel = (int)d.BatteryLevel,
                                IsInGame = d.IsGameRunning,
                                CurrentGamePackage = d.CurrentGamePackageName,
                                DeviceType = d.Type,
                                LastUpdated = DateTime.Now
                            })
                           .ToList();
                    });

                    await PublishDeviceListResponseAsync(request.MessageId, devices ?? new List<DeviceInfo>());
                }
                else
                {
                    await PublishDeviceListResponseAsync(request.MessageId, new List<DeviceInfo>());
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理设备列表请求失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理游戏列表请求
        /// </summary>
        private async Task HandleGameListRequestAsync(string payload)
        {
            try
            {
                Log.Write(Level.DEBUG, $"开始处理游戏列表请求，消息内容: {payload}");
                var request = JsonConvert.DeserializeObject<GameListRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到游戏列表请求，消息ID: {request?.MessageId}, 发送者: {request?.SenderId}");

                if (_gameViewModel != null)
                {
                    // 使用Dispatcher在UI线程中访问GameViewModel
                    List<GameInfo> games = null;
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        var gameInfos = _gameViewModel.GetSourceInfos();
                        games = gameInfos
                            .Where(g => request.IncludeDisabled || g.IsShow) // 根据请求过滤是否包含禁用游戏
                            .ToList();
                    });

                    await PublishGameListResponseAsync(request.RequestId ?? request.MessageId, games ?? new List<GameInfo>(), true, null);
                }
                else
                {
                    await PublishGameListResponseAsync(request.RequestId ?? request.MessageId, new List<GameInfo>(), false, "游戏服务未初始化");
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理游戏列表请求失败: {ex.Message}");
                // 发送错误响应
                await PublishGameListResponseAsync("unknown", new List<GameInfo>(), false, ex.Message);
            }
        }

        /// <summary>
        /// 发布游戏启动响应
        /// </summary>
        private async Task PublishGameStartResponseAsync(string requestId, bool success, string errorMessage, GameLaunchResult result)
        {
            var response = new GameStartResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Success = success,
                ErrorMessage = errorMessage,
                Results = result?.SuccessDevices?.Concat(result.FailedDevices)?.Select(r => new DeviceStartResult
                {
                    DeviceId = r.DeviceId.ToString(),
                    SerialNumber = r.SerialNumber,
                    Success = r.Success,
                    ErrorMessage = r.ErrorMessage,
                    StartTime = r.LaunchTime
                }).ToList() ?? new List<DeviceStartResult>()
            };

            await PublishAsync(MqttTopics.GAME_START_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 发布游戏停止响应
        /// </summary>
        private async Task PublishGameStopResponseAsync(string requestId, bool success, string errorMessage, GameStopResult result)
        {
            var response = new GameStopResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Success = success,
                ErrorMessage = errorMessage,
                Results = result?.SuccessDevices?.Concat(result.FailedDevices)?.Select(r => new CommDeviceStopResult
                {
                    DeviceId = r.DeviceId.ToString(),
                    SerialNumber = r.SerialNumber,
                    Success = r.Success,
                    ErrorMessage = r.ErrorMessage,
                    StopTime = r.StopTime
                }).ToList() ?? new List<CommDeviceStopResult>()
            };

            await PublishAsync(MqttTopics.GAME_STOP_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 发布设备列表响应
        /// </summary>
        private async Task PublishDeviceListResponseAsync(string requestId, List<DeviceInfo> devices)
        {
            var response = new DeviceListResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Devices = devices,
                TotalCount = devices.Count,
                OnlineCount = devices.Count(d => d.IsOnline)
            };

            await PublishAsync(MqttTopics.DEVICE_LIST_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 发布游戏列表响应
        /// </summary>
        private async Task PublishGameListResponseAsync(string requestId, List<GameInfo> games, bool success, string errorMessage)
        {
            var response = new GameListResponseMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                RequestId = requestId,
                SenderId = _clientId,
                Games = games,
                TotalCount = games.Count,
                Success = success,
                ErrorMessage = errorMessage,
                Type = "game_list_response" // 确保类型与Android端匹配
            };

            await PublishAsync(MqttTopics.GAME_LIST_RESPONSE, JsonConvert.SerializeObject(response, JsonSettings));
            Log.Write(Level.INFO, $"游戏列表响应已发送: 游戏数量={games.Count}, 成功={success}, 请求ID={requestId}");
        }

        /// <summary>
        /// 发布连接状态
        /// </summary>
        private async Task PublishConnectionStatusAsync(bool isConnected)
        {
            var status = new ConnectionStatusMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _clientId,
                IsConnected = isConnected,
                Timestamp = DateTime.Now,
                ClientType = "PCServer"
            };

            await PublishAsync(MqttTopics.CONNECTION_STATUS, JsonConvert.SerializeObject(status, JsonSettings));
        }

        public async Task DisconnectAsync()
        {
            if (_isConnected)
            {
                await PublishConnectionStatusAsync(false);
            }
            await mqttClient.DisconnectAsync();
            Log.Write(Level.INFO, "MQTT Client disconnected.");
        }

        /// <summary>
        /// 处理游戏停止请求
        /// </summary>
        private async Task HandleGameStopRequestAsync(string payload)
        {
            try
            {
                var request = JsonConvert.DeserializeObject<GameStopRequestMessage>(payload);
                Log.Write(Level.INFO, $"收到游戏停止请求: 游戏={request.SelectedGame?.Name}, 设备数量={request.SelectedDeviceIds?.Count}");

                if (_gameLaunchService != null && _deviceViewModel != null && _gameViewModel != null)
                {
                    // 在UI线程中查找游戏和设备
                    GameSource game = null;
                    List<DeviceSource> selectedDevices = null;

                    if (Application.Current?.Dispatcher != null)
                    {
                        try
                        {
                            // 使用同步调用避免线程切换问题
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                try
                                {
                                    // 查找游戏
                                    game = _gameViewModel.Source.FirstOrDefault(g => g.ID == request.SelectedGame.ID);

                                    // 查找设备
                                    if (game != null)
                                    {
                                        selectedDevices = _deviceViewModel.Source
                                            .Where(d => request.SelectedDeviceIds.Contains(d.ID.ToString()))
                                            .ToList();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Log.Write(Level.ERROR, $"在UI线程中查找游戏和设备失败: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Log.Write(Level.ERROR, $"调用UI线程失败: {ex.Message}");
                            await PublishGameStopResponseAsync(request.MessageId, false, $"UI线程调用失败: {ex.Message}", null);
                            return;
                        }
                    }
                    else
                    {
                        Log.Write(Level.ERROR, "Application.Current.Dispatcher 不可用");
                        await PublishGameStopResponseAsync(request.MessageId, false, "UI线程不可用", null);
                        return;
                    }

                    if (game == null)
                    {
                        await PublishGameStopResponseAsync(request.MessageId, false, "游戏不存在", null);
                        return;
                    }

                    if (selectedDevices == null || !selectedDevices.Any())
                    {
                        await PublishGameStopResponseAsync(request.MessageId, false, "没有选择有效的设备", null);
                        return;
                    }

                    Log.Write(Level.INFO, $"找到游戏: {game.Name}, 设备数量: {selectedDevices.Count}");

                    // 停止游戏
                    var result = await _gameLaunchService.StopGameAsync(selectedDevices, game.PackageName);

                    // 发布响应
                    await PublishGameStopResponseAsync(request.MessageId, result.Success, result.ErrorMessage, result);
                }
                else
                {
                    await PublishGameStopResponseAsync(request.MessageId, false, "服务未初始化", null);
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理游戏停止请求失败: {ex.Message}");
                // 发送错误响应
                try
                {
                    var request = JsonConvert.DeserializeObject<GameStopRequestMessage>(payload);
                    await PublishGameStopResponseAsync(request?.MessageId ?? Guid.NewGuid().ToString(), false, $"处理请求时发生错误: {ex.Message}", null);
                }
                catch
                {
                    // 忽略响应发送失败
                }
            }
        }

        /// <summary>
        /// 处理设备选择变更
        /// </summary>
        private async Task HandleDeviceSelectionChangedAsync(string payload)
        {
            Log.Write(Level.INFO, "收到设备选择变更");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 处理心跳消息
        /// </summary>
        private async Task HandleHeartbeatAsync(string payload)
        {
            var heartbeat = JsonConvert.DeserializeObject<HeartbeatMessage>(payload);
            Log.Write(Level.DEBUG, $"收到心跳: {heartbeat.SenderId}");

            // 回复心跳
            var response = new HeartbeatMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _clientId,
                ReceiverId = heartbeat.SenderId,
                Status = "OK",
                Info = new Dictionary<string, object>
                {
                    ["ServerTime"] = DateTime.Now,
                    ["ConnectedDevices"] = _deviceViewModel?.Source?.Count(d => d.IsOnline) ?? 0
                }
            };

            await PublishAsync(MqttTopics.HEARTBEAT, JsonConvert.SerializeObject(response, JsonSettings));
        }

        /// <summary>
        /// 处理连接状态消息
        /// </summary>
        private async Task HandleConnectionStatusAsync(string payload)
        {
            try
            {
                Log.Write(Level.DEBUG, $"收到连接状态消息: {payload}");

                var connectionMessage = JsonConvert.DeserializeObject<ConnectionStatusMessage>(payload);
                if (connectionMessage != null)
                {
                    Log.Write(Level.INFO, $"客户端连接状态: {connectionMessage.SenderId}, 连接={connectionMessage.IsConnected}, 类型={connectionMessage.ClientType}");

                    // 如果是Android客户端首次连接，主动推送设备列表
                    if (connectionMessage.IsConnected &&
                        connectionMessage.ClientType == "AndroidClient" &&
                        _deviceViewModel?.Source != null)
                    {
                        Log.Write(Level.INFO, $"检测到Android客户端连接，主动推送设备列表给: {connectionMessage.SenderId}");
                        await PushDeviceListToClientAsync(connectionMessage.SenderId);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理连接状态消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 主动推送设备列表给指定客户端
        /// </summary>
        private async Task PushDeviceListToClientAsync(string clientId)
        {
            try
            {
                if (_deviceViewModel?.Source == null)
                {
                    Log.Write(Level.INFO, "设备数据源为空，无法推送设备列表");
                    return;
                }

                // 使用Dispatcher在UI线程中获取所有设备（包括离线设备）
                List<DeviceInfo> devices = null;
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    devices = _deviceViewModel.Source
                        .Select(d => new DeviceInfo
                        {
                            ID = d.ID,
                            SerialNumber = d.SerialNumber,
                            Name = d.Name,
                            IpAddress = d.IP,
                            IsOnline = d.IsOnline,
                            BatteryLevel = (int)d.BatteryLevel,
                            IsInGame = d.IsGameRunning,
                            CurrentGamePackage = d.CurrentGamePackageName,
                            DeviceType = d.Type,
                            LastUpdated = DateTime.Now
                        })
                        .ToList();
                });

                if (devices == null)
                {
                    Log.Write(Level.INFO, "无法获取设备列表");
                    return;
                }

                var response = new DeviceListResponseMessage
                {
                    MessageId = Guid.NewGuid().ToString(),
                    SenderId = _clientId,
                    ReceiverId = clientId,
                    Devices = devices,
                    TotalCount = devices.Count,
                    OnlineCount = devices.Count(d => d.IsOnline)
                };

                string jsonResponse = JsonConvert.SerializeObject(response, JsonSettings);
                await PublishAsync(MqttTopics.DEVICE_LIST_RESPONSE, jsonResponse);

                Log.Write(Level.INFO, $"主动推送设备列表完成: 总设备数={devices.Count}, 在线设备数={response.OnlineCount}, 目标客户端={clientId}");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"主动推送设备列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 广播设备状态更新
        /// </summary>
        public async Task BroadcastDeviceStatusUpdateAsync()
        {
            if (!_isConnected || _deviceViewModel == null) return;

            // 使用Dispatcher在UI线程中访问DeviceViewModel
            List<DeviceInfo> devices = null;
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                devices = _deviceViewModel.Source.Select(d => new DeviceInfo
                {
                    ID = d.ID,
                    SerialNumber = d.SerialNumber,
                    Name = d.Name,
                    IpAddress = d.IP,
                    IsOnline = d.IsOnline,
                    BatteryLevel = d.BatteryLevel,
                    IsInGame = d.IsGameRunning,
                    CurrentGamePackage = d.CurrentGamePackageName,
                    DeviceType = d.Type,
                    LastUpdated = DateTime.Now
                }).ToList();
            });

            if (devices != null)
            {
                var update = new DeviceStatusUpdateMessage
                {
                    MessageId = Guid.NewGuid().ToString(),
                    SenderId = _clientId,
                    Devices = devices,
                    Timestamp = DateTime.Now
                };

                await PublishAsync(MqttTopics.DEVICE_STATUS_UPDATE, JsonConvert.SerializeObject(update, JsonSettings));
            }
        }

        /// <summary>
        /// 处理设备状态更新消息，只记录关键信息
        /// </summary>
        private async Task HandleDeviceStatusUpdateAsync(string payload)
        {
            try
            {
                // 反序列化payload，假设格式为{"devices":[...], ...}
                var json = Newtonsoft.Json.Linq.JObject.Parse(payload);
                var devices = json["devices"] as Newtonsoft.Json.Linq.JArray;
                int total = devices?.Count ?? 0;
                int online = 0;
                int offline = 0;
                List<string> changedDeviceIds = new List<string>();
                if (devices != null)
                {
                    foreach (var d in devices)
                    {
                        bool isOnline = d["isOnline"] != null && (bool)d["isOnline"];
                        if (isOnline) online++; else offline++;
                        // 可根据业务需求，记录变更设备ID或序列号
                        if (d["isSelected"] != null && (bool)d["isSelected"])
                        {
                            changedDeviceIds.Add(d["serialNumber"]?.ToString() ?? d["id"]?.ToString());
                        }
                    }
                }
                Log.Write(Level.INFO, $"[Broker] 设备状态批量上报: 总数={total}，在线={online}，离线={offline}，变更设备: {string.Join(",", changedDeviceIds)}");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"处理设备状态更新消息异常: {ex.Message}");
            }
            await Task.CompletedTask;
        }

        /// <summary>
        /// 获取连接状态
        /// </summary>
        public bool IsConnected => _isConnected;
    }

    #region Event Args Classes

    /// <summary>
    /// 游戏启动请求事件参数
    /// </summary>
    public class GameStartRequestEventArgs : EventArgs
    {
        public GameStartRequestMessage Request { get; }

        public GameStartRequestEventArgs(GameStartRequestMessage request)
        {
            Request = request;
        }
    }

    /// <summary>
    /// 设备列表请求事件参数
    /// </summary>
    public class DeviceListRequestEventArgs : EventArgs
    {
        public DeviceListRequestMessage Request { get; }

        public DeviceListRequestEventArgs(DeviceListRequestMessage request)
        {
            Request = request;
        }
    }

    /// <summary>
    /// 连接状态事件参数
    /// </summary>
    public class ConnectionStatusEventArgs : EventArgs
    {
        public bool IsConnected { get; }
        public string Message { get; }

        public ConnectionStatusEventArgs(bool isConnected, string message)
        {
            IsConnected = isConnected;
            Message = message;
        }
    }

    #endregion
}