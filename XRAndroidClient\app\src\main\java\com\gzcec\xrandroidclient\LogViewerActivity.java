package com.gzcec.xrandroidclient;

import android.app.AlertDialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.gzcec.xrandroidclient.utils.LogManager;

import java.util.ArrayList;
import java.util.List;

/**
 * 日志查看器Activity
 */
public class LogViewerActivity extends AppCompatActivity {
    private static final String TAG = "LogViewerActivity";
    
    private TextView tvLogContent;
    private EditText etSearch;
    private ScrollView scrollView;
    private Handler mainHandler;
    
    private List<String> allLogs = new ArrayList<>();
    private List<String> filteredLogs = new ArrayList<>();
    private String currentFilter = "";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_log_viewer_simple);

        mainHandler = new Handler(Looper.getMainLooper());

        initViews();
        setupSimpleToolbar();
        loadLogs();
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        tvLogContent = findViewById(R.id.tv_log_content);
        etSearch = findViewById(R.id.et_search);
        scrollView = findViewById(R.id.scroll_view);
        
        // 设置搜索功能
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                currentFilter = s.toString().trim();
                filterLogs();
            }
        });
    }
    
    /**
     * 设置简单工具栏
     */
    private void setupSimpleToolbar() {
        Button btnBack = findViewById(R.id.btn_back);
        Button btnRefresh = findViewById(R.id.btn_refresh);
        Button btnMenu = findViewById(R.id.btn_menu);

        // 返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 刷新按钮
        btnRefresh.setOnClickListener(v -> refreshLogs());

        // 菜单按钮
        btnMenu.setOnClickListener(v -> showOptionsMenu());
    }
    
    /**
     * 加载日志
     */
    private void loadLogs() {
        tvLogContent.setText("正在加载日志...");
        
        LogManager.getInstance().getAllLogs(new LogManager.LogCallback() {
            @Override
            public void onLogsRetrieved(List<String> logs) {
                mainHandler.post(() -> {
                    allLogs.clear();
                    allLogs.addAll(logs);
                    
                    if (logs.isEmpty()) {
                        tvLogContent.setText("暂无日志数据");
                    } else {
                        filterLogs();
                        Log.d(TAG, "加载日志完成: " + logs.size() + " 行");
                    }
                });
            }
        });
    }
    
    /**
     * 过滤日志
     */
    private void filterLogs() {
        filteredLogs.clear();
        
        if (currentFilter.isEmpty()) {
            filteredLogs.addAll(allLogs);
        } else {
            String filter = currentFilter.toLowerCase();
            for (String log : allLogs) {
                if (log.toLowerCase().contains(filter)) {
                    filteredLogs.add(log);
                }
            }
        }
        
        displayLogs();
    }
    
    /**
     * 显示日志
     */
    private void displayLogs() {
        if (filteredLogs.isEmpty()) {
            if (currentFilter.isEmpty()) {
                tvLogContent.setText("暂无日志数据");
            } else {
                tvLogContent.setText("未找到包含 \"" + currentFilter + "\" 的日志");
            }
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        
        // 添加统计信息
        if (!currentFilter.isEmpty()) {
            sb.append("搜索结果: ").append(filteredLogs.size()).append(" / ").append(allLogs.size()).append(" 行\n");
            sb.append("关键词: ").append(currentFilter).append("\n");
            sb.append("----------------------------------------\n");
        }
        
        // 添加日志内容
        for (String log : filteredLogs) {
            sb.append(log).append("\n");
        }
        
        tvLogContent.setText(sb.toString());
        
        // 滚动到底部
        scrollView.post(() -> scrollView.fullScroll(ScrollView.FOCUS_DOWN));
    }
    
    /**
     * 显示选项菜单
     */
    private void showOptionsMenu() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选项");

        String[] options = {"复制日志", "查看Logcat", "统计信息", "清空日志"};
        builder.setItems(options, (dialog, which) -> {
            switch (which) {
                case 0:
                    copyLogsToClipboard();
                    break;
                case 1:
                    showLogcatLogs();
                    break;
                case 2:
                    showLogStatistics();
                    break;
                case 3:
                    showClearLogsDialog();
                    break;
            }
        });

        builder.show();
    }
    
    /**
     * 刷新日志
     */
    private void refreshLogs() {
        Toast.makeText(this, "正在刷新日志...", Toast.LENGTH_SHORT).show();
        loadLogs();
    }
    
    /**
     * 复制日志到剪贴板
     */
    private void copyLogsToClipboard() {
        if (filteredLogs.isEmpty()) {
            Toast.makeText(this, "没有日志可复制", Toast.LENGTH_SHORT).show();
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        for (String log : filteredLogs) {
            sb.append(log).append("\n");
        }
        
        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("XR日志", sb.toString());
        clipboard.setPrimaryClip(clip);
        
        Toast.makeText(this, "日志已复制到剪贴板 (" + filteredLogs.size() + " 行)", Toast.LENGTH_SHORT).show();
    }
    
    /**
     * 显示清空日志对话框
     */
    private void showClearLogsDialog() {
        new AlertDialog.Builder(this)
            .setTitle("清空日志")
            .setMessage("确定要清空所有日志吗？此操作不可恢复。")
            .setPositiveButton("确定", (dialog, which) -> clearLogs())
            .setNegativeButton("取消", null)
            .show();
    }
    
    /**
     * 清空日志
     */
    private void clearLogs() {
        LogManager.getInstance().clearAllLogs();
        
        allLogs.clear();
        filteredLogs.clear();
        tvLogContent.setText("日志已清空");
        
        Toast.makeText(this, "日志已清空", Toast.LENGTH_SHORT).show();
    }
    
    /**
     * 显示Logcat日志
     */
    private void showLogcatLogs() {
        tvLogContent.setText("正在获取Logcat日志...");

        LogManager.getInstance().getRecentLogcatLogs(new LogManager.LogCallback() {
            @Override
            public void onLogsRetrieved(List<String> logs) {
                mainHandler.post(() -> {
                    allLogs.clear();
                    allLogs.addAll(logs);

                    if (logs.isEmpty()) {
                        tvLogContent.setText("未找到Logcat日志");
                    } else {
                        currentFilter = ""; // 清空搜索过滤
                        filterLogs();
                        Toast.makeText(LogViewerActivity.this,
                            "已加载 " + logs.size() + " 行Logcat日志", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }

    /**
     * 显示日志统计信息
     */
    private void showLogStatistics() {
        LogManager.getInstance().getLogStatistics(new LogManager.StatisticsCallback() {
            @Override
            public void onStatisticsRetrieved(LogManager.LogStatistics statistics) {
                mainHandler.post(() -> {
                    String message = "日志统计信息:\n\n" +
                        "文件数量: " + statistics.fileCount + "\n" +
                        "总大小: " + statistics.getFormattedSize() + "\n" +
                        "总行数: " + statistics.totalLines + "\n" +
                        "当前显示: " + filteredLogs.size() + " 行\n" +
                        "搜索关键词: " + (currentFilter.isEmpty() ? "无" : currentFilter);

                    new AlertDialog.Builder(LogViewerActivity.this)
                        .setTitle("日志统计")
                        .setMessage(message)
                        .setPositiveButton("确定", null)
                        .show();
                });
            }
        });
    }
}
