<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#181A20">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/title_device_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/tab_device_status"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:paddingTop="20dp"
            android:paddingBottom="12dp"
            android:background="@drawable/bg_title_rounded_dark"
            android:elevation="4dp"
            android:layout_marginBottom="16dp"
            android:layout_marginTop="8dp"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="#22252A"
            android:clipToPadding="false"
            android:padding="4dp"/>
    </LinearLayout>

</FrameLayout> 