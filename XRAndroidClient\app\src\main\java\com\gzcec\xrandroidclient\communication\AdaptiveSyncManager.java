package com.gzcec.xrandroidclient.communication;

import android.util.Log;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 自适应同步管理器
 * 根据设备活跃度动态调整同步频率，优化网络资源使用
 */
public class AdaptiveSyncManager {
    private static final String TAG = "AdaptiveSyncManager";
    
    // 同步频率配置 (毫秒)
    private static final long MIN_SYNC_INTERVAL = 1000;      // 最小同步间隔 1秒
    private static final long MAX_SYNC_INTERVAL = 30000;     // 最大同步间隔 30秒
    private static final long DEFAULT_SYNC_INTERVAL = 5000;  // 默认同步间隔 5秒
    
    // 活跃度检测配置
    private static final long ACTIVITY_WINDOW = 60000;       // 活跃度检测窗口 1分钟
    private static final int HIGH_ACTIVITY_THRESHOLD = 10;   // 高活跃度阈值
    private static final int LOW_ACTIVITY_THRESHOLD = 2;     // 低活跃度阈值
    
    // 同步管理
    private final ScheduledExecutorService scheduler;
    private ScheduledFuture<?> syncTask;
    private volatile long currentSyncInterval;
    private final Object syncLock = new Object();
    
    // 活跃度统计
    private final AtomicInteger activityCount = new AtomicInteger(0);
    private final AtomicLong lastActivityTime = new AtomicLong(System.currentTimeMillis());
    private final ConcurrentLinkedQueue<Long> activityHistory = new ConcurrentLinkedQueue<>();
    
    // 同步回调
    private SyncCallback syncCallback;
    
    // 统计信息
    private final AtomicInteger totalSyncs = new AtomicInteger(0);
    private final AtomicInteger skippedSyncs = new AtomicInteger(0);
    
    /**
     * 同步回调接口
     */
    public interface SyncCallback {
        void onSync();
        boolean shouldSkipSync();
    }
    
    /**
     * 构造函数
     */
    public AdaptiveSyncManager(SyncCallback callback) {
        this.syncCallback = callback;
        this.currentSyncInterval = DEFAULT_SYNC_INTERVAL;
        
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "AdaptiveSync");
            t.setDaemon(true);
            return t;
        });
        
        Log.i(TAG, "自适应同步管理器已初始化 - 默认间隔: " + DEFAULT_SYNC_INTERVAL + "ms");
    }
    
    /**
     * 启动同步
     */
    public void start() {
        synchronized (syncLock) {
            if (syncTask != null && !syncTask.isCancelled()) {
                Log.w(TAG, "同步任务已在运行");
                return;
            }
            
            scheduleNextSync();
            Log.i(TAG, "自适应同步已启动");
        }
    }
    
    /**
     * 停止同步
     */
    public void stop() {
        synchronized (syncLock) {
            if (syncTask != null) {
                syncTask.cancel(false);
                syncTask = null;
            }
            Log.i(TAG, "自适应同步已停止");
        }
    }
    
    /**
     * 记录活跃度
     */
    public void recordActivity() {
        long currentTime = System.currentTimeMillis();
        activityCount.incrementAndGet();
        lastActivityTime.set(currentTime);
        
        // 添加到历史记录
        activityHistory.offer(currentTime);
        
        // 清理过期的历史记录
        cleanupActivityHistory(currentTime);
        
        // 重新评估同步频率
        evaluateSyncFrequency();
        
        Log.d(TAG, "记录活跃度 - 当前计数: " + activityCount.get());
    }
    
    /**
     * 强制同步
     */
    public void forceSync() {
        if (syncCallback != null) {
            try {
                syncCallback.onSync();
                totalSyncs.incrementAndGet();
                Log.d(TAG, "强制同步执行");
            } catch (Exception e) {
                Log.e(TAG, "强制同步失败", e);
            }
        }
    }
    
    /**
     * 调度下一次同步
     */
    private void scheduleNextSync() {
        synchronized (syncLock) {
            if (syncTask != null) {
                syncTask.cancel(false);
            }
            
            syncTask = scheduler.schedule(this::performSync, currentSyncInterval, TimeUnit.MILLISECONDS);
            Log.d(TAG, "已调度下次同步 - 间隔: " + currentSyncInterval + "ms");
        }
    }
    
    /**
     * 执行同步
     */
    private void performSync() {
        try {
            // 检查是否应该跳过同步
            if (syncCallback != null && syncCallback.shouldSkipSync()) {
                skippedSyncs.incrementAndGet();
                Log.d(TAG, "跳过同步 - 条件不满足");
            } else {
                // 执行同步
                if (syncCallback != null) {
                    syncCallback.onSync();
                    totalSyncs.incrementAndGet();
                }
            }
            
            // 重新评估同步频率
            evaluateSyncFrequency();
            
        } catch (Exception e) {
            Log.e(TAG, "同步执行失败", e);
        } finally {
            // 调度下一次同步
            scheduleNextSync();
        }
    }
    
    /**
     * 评估同步频率
     */
    private void evaluateSyncFrequency() {
        long currentTime = System.currentTimeMillis();
        
        // 清理过期的活跃度历史
        cleanupActivityHistory(currentTime);
        
        // 计算活跃度级别
        int recentActivityCount = activityHistory.size();
        ActivityLevel activityLevel = determineActivityLevel(recentActivityCount);
        
        // 根据活跃度调整同步间隔
        long newInterval = calculateSyncInterval(activityLevel, currentTime);
        
        if (newInterval != currentSyncInterval) {
            long oldInterval = currentSyncInterval;
            currentSyncInterval = newInterval;
            
            Log.i(TAG, String.format("同步频率已调整 - 活跃度: %s, 间隔: %dms -> %dms, 活跃计数: %d", 
                activityLevel, oldInterval, newInterval, recentActivityCount));
        }
    }
    
    /**
     * 清理过期的活跃度历史
     */
    private void cleanupActivityHistory(long currentTime) {
        long cutoffTime = currentTime - ACTIVITY_WINDOW;
        
        while (!activityHistory.isEmpty()) {
            Long timestamp = activityHistory.peek();
            if (timestamp != null && timestamp < cutoffTime) {
                activityHistory.poll();
            } else {
                break;
            }
        }
    }
    
    /**
     * 确定活跃度级别
     */
    private ActivityLevel determineActivityLevel(int recentActivityCount) {
        if (recentActivityCount >= HIGH_ACTIVITY_THRESHOLD) {
            return ActivityLevel.HIGH;
        } else if (recentActivityCount >= LOW_ACTIVITY_THRESHOLD) {
            return ActivityLevel.MEDIUM;
        } else {
            return ActivityLevel.LOW;
        }
    }
    
    /**
     * 计算同步间隔
     */
    private long calculateSyncInterval(ActivityLevel activityLevel, long currentTime) {
        long timeSinceLastActivity = currentTime - lastActivityTime.get();
        
        switch (activityLevel) {
            case HIGH:
                // 高活跃度：使用最小间隔
                return MIN_SYNC_INTERVAL;
                
            case MEDIUM:
                // 中等活跃度：使用默认间隔
                return DEFAULT_SYNC_INTERVAL;
                
            case LOW:
                // 低活跃度：根据空闲时间动态调整
                if (timeSinceLastActivity > 60000) { // 1分钟无活动
                    return Math.min(MAX_SYNC_INTERVAL, DEFAULT_SYNC_INTERVAL * 2);
                } else {
                    return DEFAULT_SYNC_INTERVAL;
                }
                
            default:
                return DEFAULT_SYNC_INTERVAL;
        }
    }
    
    /**
     * 获取当前同步间隔
     */
    public long getCurrentSyncInterval() {
        return currentSyncInterval;
    }
    
    /**
     * 获取活跃度统计
     */
    public ActivityStatistics getActivityStatistics() {
        long currentTime = System.currentTimeMillis();
        cleanupActivityHistory(currentTime);
        
        return new ActivityStatistics(
            activityCount.get(),
            activityHistory.size(),
            determineActivityLevel(activityHistory.size()),
            currentSyncInterval,
            totalSyncs.get(),
            skippedSyncs.get()
        );
    }
    
    /**
     * 关闭管理器
     */
    public void shutdown() {
        stop();
        
        try {
            scheduler.shutdown();
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
            Log.i(TAG, "自适应同步管理器已关闭");
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 活跃度级别枚举
     */
    public enum ActivityLevel {
        LOW("低"),
        MEDIUM("中"),
        HIGH("高");
        
        private final String description;
        
        ActivityLevel(String description) {
            this.description = description;
        }
        
        @Override
        public String toString() {
            return description;
        }
    }
    
    /**
     * 活跃度统计信息
     */
    public static class ActivityStatistics {
        public final int totalActivityCount;
        public final int recentActivityCount;
        public final ActivityLevel currentActivityLevel;
        public final long currentSyncInterval;
        public final int totalSyncs;
        public final int skippedSyncs;
        
        ActivityStatistics(int total, int recent, ActivityLevel level, long interval, int syncs, int skipped) {
            this.totalActivityCount = total;
            this.recentActivityCount = recent;
            this.currentActivityLevel = level;
            this.currentSyncInterval = interval;
            this.totalSyncs = syncs;
            this.skippedSyncs = skipped;
        }
        
        @Override
        public String toString() {
            return String.format("ActivityStats{total=%d, recent=%d, level=%s, interval=%dms, syncs=%d, skipped=%d}", 
                totalActivityCount, recentActivityCount, currentActivityLevel, 
                currentSyncInterval, totalSyncs, skippedSyncs);
        }
    }
}
