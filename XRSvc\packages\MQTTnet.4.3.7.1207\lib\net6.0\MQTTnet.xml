<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MQTTnet</name>
    </assembly>
    <members>
        <member name="P:MQTTnet.Client.MqttClientConnectedEventArgs.ConnectResult">
            <summary>
                Gets the authentication result.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.ResultCode">
            <summary>
            Gets the result code.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.IsSessionPresent">
            <summary>
            Gets a value indicating whether a session was already available or not.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.WildcardSubscriptionAvailable">
            <summary>
            Gets a value indicating whether wildcards can be used in subscriptions at the current server.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.RetainAvailable">
            <summary>
            Gets whether the server supports retained messages.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.AssignedClientIdentifier">
            <summary>
            Gets the client identifier which was chosen by the server.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.AuthenticationMethod">
            <summary>
            Gets the authentication method.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.AuthenticationData">
            <summary>
            Gets the authentication data.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.ReasonString">
            <summary>
            Gets the reason string.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.MaximumQoS">
            <summary>
            Gets the maximum QoS which is supported by the server.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.ResponseInformation">
            <summary>
            Gets the response information.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.TopicAliasMaximum">
            <summary>
            Gets the maximum value for a topic alias. 0 means not supported.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.ServerReference">
            <summary>
            Gets an alternate server which should be used instead of the current one.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.ServerKeepAlive">
            <summary>
            MQTTv5 only.
            Gets the keep alive interval which was chosen by the server instead of the
            keep alive interval from the client CONNECT packet.
            A value of 0 indicates that the feature is not used.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.SubscriptionIdentifiersAvailable">
            <summary>
            Gets a value indicating whether the subscription identifiers are available or not.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.SharedSubscriptionAvailable">
            <summary>
            Gets a value indicating whether the shared subscriptions are available or not.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientConnectResult.UserProperties">
            <summary>
            Gets the user properties.
            In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT packet.
            As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add metadata to MQTT messages and pass information between publisher, broker, and subscriber.
            The feature is very similar to the HTTP header concept.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientDisconnectedEventArgs.ConnectResult">
            <summary>
                Gets the authentication result.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientDisconnectedEventArgs.Reason">
            <summary>
                Gets or sets the reason.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientDisconnectOptions.Reason">
            <summary>
                Gets or sets the reason code.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientDisconnectOptions.ReasonString">
            <summary>
                Gets or sets the reason string.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientDisconnectOptions.SessionExpiryInterval">
            <summary>
                Gets or sets the session expiry interval.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientDisconnectOptions.UserProperties">
            <summary>
                Gets or sets the user properties.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="T:MQTTnet.Client.MqttClientDisconnectOptionsReason">
            <summary>
            This enum only contains values which are valid when a client sends the reason to the server.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeContext.ReasonCode">
            <summary>
            Gets the reason code.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeContext.ReasonString">
            <summary>
            Gets the reason string.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeContext.AuthenticationMethod">
            <summary>
            Gets the authentication method.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeContext.AuthenticationData">
            <summary>
            Gets the authentication data.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeContext.UserProperties">
            <summary>
            Gets the user properties.
            In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT packet.
            As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add metadata to MQTT messages and pass information between publisher, broker, and subscriber.
            The feature is very similar to the HTTP header concept.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeData.ReasonCode">
            <summary>
            Gets or sets the reason code.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeData.ReasonString">
            <summary>
            Gets or sets the reason string.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeData.AuthenticationData">
            <summary>
            Gets or sets the authentication data.
            Authentication data is binary information used to transmit multiple iterations of cryptographic secrets of protocol steps.
            The content of the authentication data is highly dependent on the specific implementation of the authentication method.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttExtendedAuthenticationExchangeData.UserProperties">
            <summary>
            Gets or sets the user properties.
            In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT packet.
            As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add metadata to MQTT messages and pass information between publisher, broker, and subscriber.
            The feature is very similar to the HTTP header concept.
            Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.AllowPacketFragmentation">
            <summary>
                Usually the MQTT packets can be send partially. This is done by using multiple TCP packets
                or WebSocket frames etc. Unfortunately not all brokers (like Amazon Web Services (AWS)) do support this feature and
                will close the connection when receiving such packets. If such a service is used this flag must
                be set to _false_.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.AuthenticationData">
            <summary>
                Gets or sets the authentication data.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.AuthenticationMethod">
            <summary>
                Gets or sets the authentication method.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.CleanSession">
            <summary>
                Gets or sets a value indicating whether clean sessions are used or not.
                When a client connects to a broker it can connect using either a non persistent connection (clean session) or a
                persistent connection.
                With a non persistent connection the broker doesn't store any subscription information or undelivered messages for
                the client.
                This mode is ideal when the client only publishes messages.
                It can also connect as a durable client using a persistent connection.
                In this mode, the broker will store subscription information, and undelivered messages for the client.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.KeepAlivePeriod">
            <summary>
                Gets or sets the keep alive period.
                The connection is normally left open by the client so that is can send and receive data at any time.
                If no data flows over an open connection for a certain time period then the client will generate a PINGREQ and
                expect to receive a PINGRESP from the broker.
                This message exchange confirms that the connection is open and working.
                This period is known as the keep alive period.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.MaximumPacketSize">
            <summary>
                Gets or sets the maximum packet size.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.ReceiveMaximum">
            <summary>
                Gets or sets the receive maximum.
                This gives the maximum length of the receive messages.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.RequestProblemInformation">
            <summary>
                Gets or sets the request problem information.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.RequestResponseInformation">
            <summary>
                Gets or sets the request response information.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.SessionExpiryInterval">
            <summary>
                Gets or sets the session expiry interval.
                The time after a session expires when it's not actively used.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.ThrowOnNonSuccessfulConnectResponse">
            <summary>
                Gets or sets whether an exception should be thrown when the server has sent a non success ACK packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.Timeout">
            <summary>
                Gets or sets the timeout which will be applied at socket level and internal operations.
                The default value is the same as for sockets in .NET in general.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.TopicAliasMaximum">
            <summary>
                Gets or sets the topic alias maximum.
                This gives the maximum length of the topic alias.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.TryPrivate">
            <summary>
                If set to true, the bridge will attempt to indicate to the remote broker that it is a bridge not an ordinary
                client.
                If successful, this means that loop detection will be more effective and that retained messages will be propagated
                correctly.
                <remarks>
                    Not all brokers support this feature so it may be necessary to set it to false if your bridge does not
                    connect properly.
                </remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.UserProperties">
            <summary>
                Gets or sets the user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.ValidateFeatures">
            <summary>
                When this feature is enabled the client will check if used properties are supported in the selected protocol
                version.
                This feature can be validated if an application message is generated one time but sent via different protocol
                versions.
                Default values are applied if the validation is off and features are not supported.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillContentType">
            <summary>
                Gets or sets the content type of the will message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillCorrelationData">
            <summary>
                Gets or sets the correlation data of the will message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillDelayInterval">
            <summary>
                Gets or sets the will delay interval.
                This is the time between the client disconnect and the time the will message will be sent.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillMessageExpiryInterval">
            <summary>
                Gets or sets the message expiry interval of the will message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillPayload">
            <summary>
                Gets or sets the payload of the will message.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillPayloadFormatIndicator">
            <summary>
                Gets or sets the payload format indicator of the will message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillQualityOfServiceLevel">
            <summary>
                Gets or sets the QoS level of the will message.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillResponseTopic">
            <summary>
                Gets or sets the response topic of the will message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillRetain">
            <summary>
                Gets or sets the retain flag of the will message.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillTopic">
            <summary>
                Gets or sets the topic of the will message.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WillUserProperties">
            <summary>
                Gets or sets the user properties of the will message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WriterBufferSize">
            <summary>
                Gets or sets the default and initial size of the packet write buffer.
                It is recommended to set this to a value close to the usual expected packet size * 1.5.
                Do not change this value when no memory issues are experienced.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientOptions.WriterBufferSizeMax">
            <summary>
                Gets or sets the maximum size of the buffer writer. The writer will reduce its internal buffer
                to this value after serializing a packet.
                Do not change this value when no memory issues are experienced.
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientOptionsBuilder.WithCleanSession(System.Boolean)">
            <summary>
                Clean session is used in MQTT versions below 5.0.0. It is the same as setting "CleanStart".
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientOptionsBuilder.WithCleanStart(System.Boolean)">
            <summary>
                Clean start is used in MQTT versions 5.0.0 and higher. It is the same as setting "CleanSession".
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientOptionsBuilder.WithoutPacketFragmentation">
            <summary>
                Usually the MQTT packets can be send partially. This is done by using multiple TCP packets
                or WebSocket frames etc. Unfortunately not all brokers (like Amazon Web Services (AWS)) do support this feature and
                will close the connection when receiving such packets. If such a service is used this flag must
                be set to _true_.
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientOptionsBuilder.WithoutThrowOnNonSuccessfulConnectResponse">
            <summary>
                The client will not throw an exception when the MQTT server responds with a non success ACK packet.
                This will become the default behavior in future versions of the library.
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientOptionsBuilder.WithTimeout(System.TimeSpan)">
            <summary>
                Sets the timeout which will be applied at socket level and internal operations.
                The default value is the same as for sockets in .NET in general.
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientOptionsBuilder.WithTryPrivate(System.Boolean)">
            <summary>
                If set to true, the bridge will attempt to indicate to the remote broker that it is a bridge not an ordinary
                client.
                If successful, this means that loop detection will be more effective and that retained messages will be propagated
                correctly.
                Not all brokers support this feature so it may be necessary to set it to false if your bridge does not connect
                properly.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientTcpOptions.DualMode">
            <summary>
                Gets or sets whether the underlying socket should run in dual mode.
                Leaving this _null_ will avoid setting this value at socket level.
                Setting this a value other than _null_ will throw an exception when only IPv4 is supported on the machine.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientTcpOptions.LocalEndpoint">
            <summary>
                Gets the local endpoint (network card) which is used by the client.
                Set it to _null_ to let the OS select the network card.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientTcpOptions.NoDelay">
            <summary>
                Enables or disables the Nagle algorithm for the socket.
                This is only supported for TCP.
                For other protocol types the value is ignored.
                Default: true
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientTcpOptions.ProtocolType">
            <summary>
                The MQTT transport is usually TCP but when using other endpoint types like
                unix sockets it must be changed (IP for unix sockets).
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientTlsOptions.ClientCertificatesProvider">
            <summary>
                Gets or sets the provider for certificates.
                This provider gets called whenever the client wants to connect
                with the server and requires certificates for authentication.
                The implementation may return different certificates each time.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientTlsOptions.TargetHost">
            <summary>
                Gets or sets the target host.
                If the value is null or empty the same host as the TCP socket host will be used.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientWebSocketOptions.KeepAliveInterval">
            <summary>
                Gets or sets the keep alive interval for the Web Socket connection.
                This is not related to the keep alive interval for the MQTT protocol.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientWebSocketOptions.UseDefaultCredentials">
            <summary>
                Gets or sets whether the default (system) credentials should be used when connecting via Web Socket connection.
                This is not related to the credentials which are used for the MQTT protocol.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientPublishResult.IsSuccess">
            <summary>
                Returns if the overall status of the publish is a success. This can be the reason code _Success_ or
                _NoMatchingSubscribers_. _NoMatchingSubscribers_ usually indicates only that no other client is interested in the
                topic but overall transmit
                to the server etc. was a success.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientPublishResult.PacketIdentifier">
            <summary>
                Gets the packet identifier which was used for this publish.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientPublishResult.ReasonCode">
            <summary>
                Gets or sets the reason code.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientPublishResult.ReasonString">
            <summary>
                Gets or sets the reason string.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientPublishResult.UserProperties">
            <summary>
                Gets or sets the user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.AutoAcknowledge">
            <summary>
                Gets or sets whether the library should send MQTT ACK packets automatically if required.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.IsHandled">
            <summary>
                Gets or sets whether this message was handled.
                This value can be used in user code for custom control flow.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.PacketIdentifier">
            <summary>
                Gets the identifier of the MQTT packet
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.ProcessingFailed">
            <summary>
                Indicates if the processing of this PUBLISH packet has failed.
                If the processing has failed the client will not send an ACK packet etc.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.ReasonCode">
            <summary>
                Gets or sets the reason code which will be sent to the server.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.ResponseReasonString">
            <summary>
                Gets or sets the reason string which will be sent to the server in the ACK packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttApplicationMessageReceivedEventArgs.ResponseUserProperties">
            <summary>
                Gets or sets the user properties which will be sent to the server in the ACK packet etc.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeOptions.SubscriptionIdentifier">
            <summary>
                Gets or sets the subscription identifier.
                The client can specify a subscription identifier when subscribing.
                The broker will establish and store the mapping relationship between this subscription and subscription identifier
                when successfully create or modify subscription.
                The broker will return the subscription identifier associated with this PUBLISH packet and the PUBLISH packet to
                the client when need to forward PUBLISH packets matching this subscription to this client.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeOptions.TopicFilters">
            <summary>
                Gets or sets a list of topic filters the client wants to subscribe to.
                Topic filters can include regular topics or wild cards.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeOptions.UserProperties">
            <summary>
                Gets or sets the user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientSubscribeOptionsBuilder.WithUserProperty(System.String,System.String)">
            <summary>
                Adds the user property to the subscribe options.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeResult.Items">
            <summary>
            Gets the result for every topic filter item.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeResult.UserProperties">
            <summary>
            Gets the user properties which were part of the SUBACK packet.
            <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeResult.ReasonString">
            <summary>
            Gets the reason string.
            <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeResult.PacketIdentifier">
            <summary>
            Gets the packet identifier which was used.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeResultItem.TopicFilter">
            <summary>
            Gets or sets the topic filter.
            The topic filter can contain topics and wildcards.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientSubscribeResultItem.ResultCode">
            <summary>
            Gets or sets the result code.
            <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeOptions.TopicFilters">
            <summary>
                Gets or sets a list of topic filters the client wants to unsubscribe from.
                Topic filters can include regular topics or wild cards.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeOptions.UserProperties">
            <summary>
                Gets or sets the user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientUnsubscribeOptionsBuilder.WithUserProperty(System.String,System.String)">
            <summary>
                Adds the user property to the unsubscribe options.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.Client.MqttClientUnsubscribeOptionsBuilder.WithUserProperty(MQTTnet.Packets.MqttUserProperty)">
            <summary>
                Adds the user property to the unsubscribe options.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeResult.Items">
            <summary>
                Gets the result for every topic filter item.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeResult.PacketIdentifier">
            <summary>
                Gets the packet identifier which was used.
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeResult.ReasonString">
            <summary>
                Gets the reason string.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeResult.UserProperties">
            <summary>
                Gets the user properties which were part of the UNSUBACK packet.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeResultItem.ResultCode">
            <summary>
                Gets or sets the result code.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Client.MqttClientUnsubscribeResultItem.TopicFilter">
            <summary>
                Gets or sets the topic filter.
                The topic filter can contain topics and wildcards.
            </summary>
        </member>
        <member name="T:MQTTnet.Diagnostics.MqttNetEventLogger">
            <summary>
                This logger fires an event when a new message was published.
            </summary>
        </member>
        <member name="T:MQTTnet.Diagnostics.MqttNetNullLogger">
            <summary>
                This logger does nothing with the messages.
            </summary>
        </member>
        <member name="T:MQTTnet.Formatter.MqttBufferWriter">
            <summary>
                This is a custom implementation of a memory stream which provides only MQTTnet relevant features.
                The goal is to avoid lots of argument checks like in the original stream. The growth rule is the
                same as for the original MemoryStream in .net. Also this implementation allows accessing the internal
                buffer for all platforms and .net framework versions (which is not available at the regular MemoryStream).
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.ContentType">
            <summary>
                Gets or sets the content type.
                The content type must be a UTF-8 encoded string. The content type value identifies the kind of UTF-8 encoded
                payload.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.CorrelationData">
            <summary>
                Gets or sets the correlation data.
                In order for the sender to know what sent message the response refers to it can also send correlation data with the
                published message.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.Dup">
            <summary>
                If the DUP flag is set to 0, it indicates that this is the first occasion that the Client or Server has attempted
                to send this MQTT PUBLISH Packet.
                If the DUP flag is set to 1, it indicates that this might be re-delivery of an earlier attempt to send the Packet.
                The DUP flag MUST be set to 1 by the Client or Server when it attempts to re-deliver a PUBLISH Packet
                [MQTT-3.3.1.-1].
                The DUP flag MUST be set to 0 for all QoS 0 messages [MQTT-3.3.1-2].
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.MessageExpiryInterval">
            <summary>
                Gets or sets the message expiry interval.
                A client can set the message expiry interval in seconds for each PUBLISH message individually.
                This interval defines the period of time that the broker stores the PUBLISH message for any matching subscribers
                that are not currently connected.
                When no message expiry interval is set, the broker must store the message for matching subscribers indefinitely.
                When the retained=true option is set on the PUBLISH message, this interval also defines how long a message is
                retained on a topic.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.Payload">
            <summary>
            Gets or sets the payload.
            The payload is the data bytes sent via the MQTT protocol.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.PayloadSegment">
            <summary>
            Get or set ArraySegment style of Payload.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.PayloadFormatIndicator">
            <summary>
                Gets or sets the payload format indicator.
                The payload format indicator is part of any MQTT packet that can contain a payload. The indicator is an optional
                byte value.
                A value of 0 indicates an “unspecified byte stream”.
                A value of 1 indicates a "UTF-8 encoded payload".
                If no payload format indicator is provided, the default value is 0.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.QualityOfServiceLevel">
            <summary>
                Gets or sets the quality of service level.
                The Quality of Service (QoS) level is an agreement between the sender of a message and the receiver of a message
                that defines the guarantee of delivery for a specific message.
                There are 3 QoS levels in MQTT:
                - At most once  (0): Message gets delivered no time, once or multiple times.
                - At least once (1): Message gets delivered at least once (one time or more often).
                - Exactly once  (2): Message gets delivered exactly once (It's ensured that the message only comes once).
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.ResponseTopic">
            <summary>
                Gets or sets the response topic.
                In MQTT 5 the ability to publish a response topic was added in the publish message which allows you to implement
                the request/response pattern between clients that is common in web applications.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.Retain">
            <summary>
                Gets or sets a value indicating whether the message should be retained or not.
                A retained message is a normal MQTT message with the retained flag set to true.
                The broker stores the last retained message and the corresponding QoS for that topic.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.SubscriptionIdentifiers">
            <summary>
                Gets or sets the subscription identifiers.
                The client can specify a subscription identifier when subscribing.
                The broker will establish and store the mapping relationship between this subscription and subscription identifier
                when successfully create or modify subscription.
                The broker will return the subscription identifier associated with this PUBLISH packet and the PUBLISH packet to
                the client when need to forward PUBLISH packets matching this subscription to this client.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.Topic">
            <summary>
                Gets or sets the MQTT topic.
                In MQTT, the word topic refers to an UTF-8 string that the broker uses to filter messages for each connected
                client.
                The topic consists of one or more topic levels. Each topic level is separated by a forward slash (topic level
                separator).
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.TopicAlias">
            <summary>
                Gets or sets the topic alias.
                Topic aliases were introduced are a mechanism for reducing the size of published packets by reducing the size of
                the topic field.
                A value of 0 indicates no topic alias is used.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="P:MQTTnet.MqttApplicationMessage.UserProperties">
            <summary>
                Gets or sets the user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                Hint: MQTT 5 feature only.
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithContentType(System.String)">
            <summary>
                Adds the content type to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithCorrelationData(System.Byte[])">
            <summary>
                Adds the correlation data to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithMessageExpiryInterval(System.UInt32)">
            <summary>
                Adds the message expiry interval in seconds to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithPayloadFormatIndicator(MQTTnet.Protocol.MqttPayloadFormatIndicator)">
            <summary>
                Adds the payload format indicator to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel)">
            <summary>
                The quality of service level.
                The Quality of Service (QoS) level is an agreement between the sender of a message and the receiver of a message
                that defines the guarantee of delivery for a specific message.
                There are 3 QoS levels in MQTT:
                - At most once  (0): Message gets delivered no time, once or multiple times.
                - At least once (1): Message gets delivered at least once (one time or more often).
                - Exactly once  (2): Message gets delivered exactly once (It's ensured that the message only comes once).
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithResponseTopic(System.String)">
            <summary>
                Adds the response topic to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithRetainFlag(System.Boolean)">
            <summary>
                A value indicating whether the message should be retained or not.
                A retained message is a normal MQTT message with the retained flag set to true.
                The broker stores the last retained message and the corresponding QoS for that topic.
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithSubscriptionIdentifier(System.UInt32)">
            <summary>
                Adds the subscription identifier to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithTopic(System.String)">
            <summary>
                The MQTT topic.
                In MQTT, the word topic refers to an UTF-8 string that the broker uses to filter messages for each connected
                client.
                The topic consists of one or more topic levels. Each topic level is separated by a forward slash (topic level
                separator).
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithTopicAlias(System.UInt16)">
            <summary>
                Adds the topic alias to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="M:MQTTnet.MqttApplicationMessageBuilder.WithUserProperty(System.String,System.String)">
            <summary>
                Adds the user property to the message.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="F:MQTTnet.MqttTopicFilterBuilder._qualityOfServiceLevel">
            <summary>
            The quality of service level.
            The Quality of Service (QoS) level is an agreement between the sender of a message and the receiver of a message that defines the guarantee of delivery for a specific message.
            There are 3 QoS levels in MQTT:
            - At most once  (0): Message gets delivered no time, once or multiple times.
            - At least once (1): Message gets delivered at least once (one time or more often).
            - Exactly once  (2): Message gets delivered exactly once (It's ensured that the message only comes once).
            </summary>
        </member>
        <member name="F:MQTTnet.MqttTopicFilterBuilder._topic">
            <summary>
            The MQTT topic.
            In MQTT, the word topic refers to an UTF-8 string that the broker uses to filter messages for each connected client.
            The topic consists of one or more topic levels. Each topic level is separated by a forward slash (topic level separator).
            </summary>
        </member>
        <member name="T:MQTTnet.Packets.MqttAuthPacket">
            <summary>Added in MQTTv5.0.0.</summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttConnAckPacket.AssignedClientIdentifier">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttConnAckPacket.IsSessionPresent">
            <summary>
                Added in MQTTv3.1.1.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttConnAckPacket.ReasonCode">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttConnectPacket.CleanSession">
            <summary>
                Also called "Clean Start" in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttDisconnectPacket.ReasonCode">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttDisconnectPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttDisconnectPacket.ServerReference">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttDisconnectPacket.SessionExpiryInterval">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttDisconnectPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubAckPacket.ReasonCode">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubAckPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubAckPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubCompPacket.ReasonCode">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubCompPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubCompPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubRecPacket.ReasonCode">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubRecPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubRecPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubRelPacket.ReasonCode">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubRelPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttPubRelPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttSubAckPacket.ReasonCodes">
            <summary>
                Reason Code is used in MQTTv5.0.0 and backward compatible to v.3.1.1. Return Code is used in MQTTv3.1.1
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttSubAckPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttSubAckPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttSubscribePacket.SubscriptionIdentifier">
            <summary>
                It is a Protocol Error if the Subscription Identifier has a value of 0.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttSubscribePacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttTopicFilter.NoLocal">
            <summary>
                Gets or sets a value indicating whether the sender will not receive its own published application messages.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttTopicFilter.QualityOfServiceLevel">
            <summary>
                Gets or sets the quality of service level.
                The Quality of Service (QoS) level is an agreement between the sender of a message and the receiver of a message
                that defines the guarantee of delivery for a specific message.
                There are 3 QoS levels in MQTT:
                - At most once  (0): Message gets delivered no time, once or multiple times.
                - At least once (1): Message gets delivered at least once (one time or more often).
                - Exactly once  (2): Message gets delivered exactly once (It's ensured that the message only comes once).
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttTopicFilter.RetainAsPublished">
            <summary>
                Gets or sets a value indicating whether messages are retained as published or not.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttTopicFilter.RetainHandling">
            <summary>
                Gets or sets the retain handling.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttTopicFilter.Topic">
            <summary>
                Gets or sets the MQTT topic.
                In MQTT, the word topic refers to an UTF-8 string that the broker uses to filter messages for each connected
                client.
                The topic consists of one or more topic levels. Each topic level is separated by a forward slash (topic level
                separator).
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttUnsubAckPacket.ReasonCodes">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttUnsubAckPacket.ReasonString">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttUnsubAckPacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="P:MQTTnet.Packets.MqttUnsubscribePacket.UserProperties">
            <summary>
                Added in MQTTv5.
            </summary>
        </member>
        <member name="F:MQTTnet.Protocol.MqttPubAckReasonCode.NoMatchingSubscribers">
            <summary>
            The message is accepted but there are no subscribers. This is sent only by the Server. If the Server knows that there are no matching subscribers, it MAY use this Reason Code instead of 0x00 (Success).
            </summary>
        </member>
        <member name="P:MQTTnet.Server.Disconnecting.MqttServerClientDisconnectOptions.ReasonString">
            <summary>
                The reason string is sent to every client via a DISCONNECT packet.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.Disconnecting.MqttServerClientDisconnectOptions.ServerReference">
            <summary>
                The server reference is sent to every client via a DISCONNECT packet.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.Disconnecting.MqttServerClientDisconnectOptions.UserProperties">
            <summary>
                These user properties are sent to every client via a DISCONNECT packet.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ApplicationMessageNotConsumedEventArgs.ApplicationMessage">
            <summary>
                Gets the application message which was not consumed by any client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ApplicationMessageNotConsumedEventArgs.SenderId">
            <summary>
                Gets the ID of the client which has sent the affected application message.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientAcknowledgedPublishPacketEventArgs.AcknowledgePacket">
            <summary>
                Gets the packet which was used for acknowledge. This can be a PubAck or PubComp packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientAcknowledgedPublishPacketEventArgs.ClientId">
            <summary>
                Gets the ID of the client which acknowledged a PUBLISH packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientAcknowledgedPublishPacketEventArgs.IsCompleted">
            <summary>
                Gets whether the PUBLISH packet is fully acknowledged. This is the case for PUBACK (QoS 1) and PUBCOMP (QoS 2.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientAcknowledgedPublishPacketEventArgs.PublishPacket">
            <summary>
                Gets the PUBLISH packet which was acknowledged.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientAcknowledgedPublishPacketEventArgs.SessionItems">
            <summary>
                Gets the session items which contain custom user data per session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientConnectedEventArgs.ClientId">
            <summary>
                Gets the client identifier of the connected client.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientConnectedEventArgs.Endpoint">
            <summary>
                Gets the endpoint of the connected client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientConnectedEventArgs.ProtocolVersion">
            <summary>
                Gets the protocol version which is used by the connected client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientConnectedEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientConnectedEventArgs.UserName">
            <summary>
                Gets the user name of the connected client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientConnectedEventArgs.UserProperties">
            <summary>
                Gets the user properties sent by the client.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientDisconnectedEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientDisconnectedEventArgs.ReasonCode">
            <summary>
                Gets the reason code sent by the client.
                Only available for clean disconnects.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientDisconnectedEventArgs.ReasonString">
            <summary>
                Gets the reason string sent by the client.
                Only available for clean disconnects.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientDisconnectedEventArgs.SessionExpiryInterval">
            <summary>
                Gets the session expiry interval sent by the client.
                Only available for clean disconnects.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientDisconnectedEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientDisconnectedEventArgs.UserProperties">
            <summary>
                Gets the user properties sent by the client.
                Only available for clean disconnects.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientSubscribedTopicEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientSubscribedTopicEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientSubscribedTopicEventArgs.TopicFilter">
            <summary>
                Gets the topic filter.
                The topic filter can contain topics and wildcards.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientUnsubscribedTopicEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientUnsubscribedTopicEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ClientUnsubscribedTopicEventArgs.TopicFilter">
            <summary>
                Gets or sets the topic filter.
                The topic filter can contain topics and wildcards.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingClientApplicationMessageEnqueueEventArgs.AcceptEnqueue">
            <summary>
                Gets or sets whether the enqueue of the application message should be performed or not.
                If set to _False_ the client will not receive the application message.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingClientApplicationMessageEnqueueEventArgs.CloseSenderConnection">
            <summary>
                Indicates if the connection with the sender should be closed.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPacketEventArgs.CancellationToken">
            <summary>
                Gets the cancellation token from the connection managing thread.
                Use this in further event processing.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPacketEventArgs.ClientId">
            <summary>
                Gets the client ID which has sent the packet or will receive the packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPacketEventArgs.Endpoint">
            <summary>
                Gets the endpoint of the sending or receiving client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPacketEventArgs.Packet">
            <summary>
                Gets or sets the MQTT packet which was received or will be sent.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPacketEventArgs.ProcessPacket">
            <summary>
                Gets or sets whether the packet should be processed or not.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPacketEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPublishEventArgs.CancellationToken">
            <summary>
                Gets the cancellation token which can indicate that the client connection gets down.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPublishEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPublishEventArgs.ProcessPublish">
            <summary>
                Gets or sets whether the publish should be processed internally.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPublishEventArgs.Response">
            <summary>
                Gets the response which will be sent to the client via the PUBACK etc. packets.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingPublishEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.CancellationToken">
            <summary>
                Gets the cancellation token which can indicate that the client connection gets down.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.CloseConnection">
            <summary>
                Gets or sets whether the broker should close the client connection.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.ProcessSubscription">
            <summary>
                Gets or sets whether the broker should create an internal subscription for the client.
                The broker can also avoid this and return "success" to the client.
                This feature allows using the MQTT Broker as the Frontend and another system as the backend.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.ReasonString">
            <summary>
                Gets or sets the reason string which will be sent to the client in the SUBACK packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.Response">
            <summary>
                Gets the response which will be sent to the client via the SUBACK packet.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.Session">
            <summary>
                Gets the current client session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.TopicFilter">
            <summary>
                Gets or sets the topic filter.
                The topic filter can contain topics and wildcards.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingSubscriptionEventArgs.UserProperties">
            <summary>
                Gets or sets the user properties.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.CancellationToken">
            <summary>
                Gets the cancellation token which can indicate that the client connection gets down.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.CloseConnection">
            <summary>
                Gets or sets whether the broker should close the client connection.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.ProcessUnsubscription">
            <summary>
                Gets or sets whether the broker should remove an internal subscription for the client.
                The broker can also avoid this and return "success" to the client.
                This feature allows using the MQTT Broker as the Frontend and another system as the backend.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.Response">
            <summary>
                Gets the response which will be sent to the client via the UNSUBACK pocket.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.Topic">
            <summary>
                Gets or sets the MQTT topic.
                In MQTT, the word topic refers to an UTF-8 string that the broker uses to filter messages for each connected
                client.
                The topic consists of one or more topic levels. Each topic level is separated by a forward slash (topic level
                separator).
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InterceptingUnsubscriptionEventArgs.UserProperties">
            <summary>
                Gets or sets the user properties.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.PreparingSessionEventArgs.WillDelayInterval">
            <summary>
                Gets the will delay interval.
                This is the time between the client disconnect and the time the will message will be sent.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.SessionDeletedEventArgs.Id">
            <summary>
                Gets the ID of the session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.SessionDeletedEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.AssignedClientIdentifier">
            <summary>
                Gets or sets the assigned client identifier.
                MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.AuthenticationData">
            <summary>
                Gets or sets the authentication data.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.AuthenticationMethod">
            <summary>
                Gets or sets the authentication method.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ChannelAdapter">
            <summary>
                Gets the channel adapter. This can be a _MqttConnectionContext_ (used in ASP.NET), a _MqttChannelAdapter_ (used for
                TCP or WebSockets) or a custom implementation.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.CleanSession">
            <summary>
                Gets or sets a value indicating whether clean sessions are used or not.
                When a client connects to a broker it can connect using either a non persistent connection (clean session) or a
                persistent connection.
                With a non persistent connection the broker doesn't store any subscription information or undelivered messages for
                the client.
                This mode is ideal when the client only publishes messages.
                It can also connect as a durable client using a persistent connection.
                In this mode, the broker will store subscription information, and undelivered messages for the client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ClientId">
            <summary>
                Gets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.KeepAlivePeriod">
            <summary>
                Gets or sets the keep alive period.
                The connection is normally left open by the client so that is can send and receive data at any time.
                If no data flows over an open connection for a certain time period then the client will generate a PINGREQ and
                expect to receive a PINGRESP from the broker.
                This message exchange confirms that the connection is open and working.
                This period is known as the keep alive period.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.MaximumPacketSize">
            <summary>
                A value of 0 indicates that the value is not used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ReasonCode">
            <summary>
                Gets or sets the reason code. When a MQTTv3 client connects the enum value must be one which is
                also supported in MQTTv3. Otherwise the connection attempt will fail because not all codes can be
                converted properly.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ReceiveMaximum">
            <summary>
                Gets or sets the receive maximum.
                This gives the maximum length of the receive messages.
                A value of 0 indicates that the value is not used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.RequestProblemInformation">
            <summary>
                Gets the request problem information.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.RequestResponseInformation">
            <summary>
                Gets the request response information.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ResponseAuthenticationData">
            <summary>
                Gets or sets the response authentication data.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ResponseUserProperties">
            <summary>
                Gets or sets the response user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.ServerReference">
            <summary>
                Gets or sets the server reference. This can be used together with i.e. "Server Moved" to send
                a different server address to the client.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.SessionExpiryInterval">
            <summary>
                Gets the session expiry interval.
                The time after a session expires when it's not actively used.
                A value of 0 means no expiation.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.SessionItems">
            <summary>
                Gets or sets a key/value collection that can be used to share data within the scope of this session.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.TopicAliasMaximum">
            <summary>
                Gets or sets the topic alias maximum.
                This gives the maximum length of the topic alias.
                A value of 0 indicates that the value is not used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.UserProperties">
            <summary>
                Gets or sets the user properties.
                In MQTT 5, user properties are basic UTF-8 string key-value pairs that you can append to almost every type of MQTT
                packet.
                As long as you don’t exceed the maximum message size, you can use an unlimited number of user properties to add
                metadata to MQTT messages and pass information between publisher, broker, and subscriber.
                The feature is very similar to the HTTP header concept.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.ValidatingConnectionEventArgs.WillDelayInterval">
            <summary>
                Gets or sets the will delay interval.
                This is the time between the client disconnect and the time the will message will be sent.
                A value of 0 indicates that the value is not used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.InjectedMqttApplicationMessage.CustomSessionItems">
            <summary>
                Gets or sets the session items which should be used for all event handlers which are involved in message
                processing.
                If _null_ is specified the singleton session items from the server are used instead.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttClientStatistics.LastPacketReceivedTimestamp">
            <summary>
                Timestamp of the last package that has been sent to the client ("received" from the client's perspective)
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttClientStatistics.LastPacketSentTimestamp">
            <summary>
                Timestamp of the last package that has been received from the client ("sent" from the client's perspective)
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttSession.IsPersistent">
            <summary>
                Session should persist if CleanSession was set to false (Mqtt3) or if SessionExpiryInterval != 0 (Mqtt5)
            </summary>
        </member>
        <member name="T:MQTTnet.Server.TopicHashMaskSubscriptions">
            <summary>
                Helper class that stores subscriptions by their topic hash mask.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServer.AcceptNewConnections">
            <summary>
                Gets or sets whether the server will accept new connections.
                If not, the server will close the connection without any notification (DISCONNECT packet).
                This feature can be used when the server is shutting down.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServer.ServerSessionItems">
            <summary>
                Gives access to the session items which belong to this server. This session items are passed
                to several events instead of the client session items if the event is caused by the server instead of a client.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerKeepAliveOptions.DisconnectClientWhenReadingPayload">
            <summary>
                When this mode is enabled the MQTT server will not close a connection when the
                client is currently sending a (large) payload. This may lead to "dead" connections
                When this mode is disabled the MQTT server will disconnect a client when the keep
                alive timeout is reached even if the client is currently sending a (large) payload.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerOptions.WriterBufferSize">
            <summary>
                Gets or sets the default and initial size of the packet write buffer.
                It is recommended to set this to a value close to the usual expected packet size * 1.5.
                Do not change this value when no memory issues are experienced.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerOptions.WriterBufferSizeMax">
            <summary>
                Gets or sets the maximum size of the buffer writer. The writer will reduce its internal buffer
                to this value after serializing a packet.
                Do not change this value when no memory issues are experienced.
            </summary>
        </member>
        <member name="M:MQTTnet.Server.MqttServerOptionsBuilder.WithoutPacketFragmentation">
            <summary>
                Usually the MQTT packets can be send partially to improve memory allocations.
                This is done by using multiple TCP packets or WebSocket frames etc.
                Unfortunately not all clients do support this and will close the connection when receiving partial packets.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerTcpEndpointBaseOptions.KeepAlive">
            <summary>
                Gets or sets whether the sockets keep alive feature should be used.
                The value _null_ indicates that the OS and framework defaults should be used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerTcpEndpointBaseOptions.AllowPacketFragmentation">
            <summary>
                Usually the MQTT packets can be send partially. This is done by using multiple TCP packets
                or WebSocket frames etc. Unfortunately not all clients do support this feature and
                will close the connection when receiving such packets. If such clients are connecting to this
                server the flag must be set to _false_.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerTcpEndpointBaseOptions.TcpKeepAliveInterval">
            <summary>
                Gets or sets the TCP keep alive interval.
                The value _null_ indicates that the OS and framework defaults should be used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerTcpEndpointBaseOptions.TcpKeepAliveRetryCount">
            <summary>
                Gets or sets the TCP keep alive retry count.
                The value _null_ indicates that the OS and framework defaults should be used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerTcpEndpointBaseOptions.TcpKeepAliveTime">
            <summary>
                Gets or sets the TCP keep alive time.
                The value _null_ indicates that the OS and framework defaults should be used.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerTcpEndpointBaseOptions.ReuseAddress">
            <summary>
                This requires admin permissions on Linux.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttClientStatus.Id">
            <summary>
                Gets or sets the client identifier.
                Hint: This identifier needs to be unique over all used clients / devices on the broker to avoid connection issues.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.MqttServerStopOptions.DefaultClientDisconnectOptions">
            <summary>
                These disconnect options are sent to every connected client via a DISCONNECT packet.
                <remarks>MQTT 5.0.0+ feature.</remarks>
            </summary>
        </member>
        <member name="P:MQTTnet.Server.SubscribeResponse.ReasonCode">
            <summary>
            Gets or sets the reason code which is sent to the client.
            The subscription is skipped when the value is not GrantedQoS_.
            MQTTv5 only.
            </summary>
        </member>
        <member name="P:MQTTnet.Server.UnsubscribeResponse.ReasonCode">
            <summary>
            Gets or sets the reason code which is sent to the client.
            MQTTv5 only.
            </summary>
        </member>
    </members>
</doc>
