namespace PICOSDK
{
using System;
using System.Collections.Generic;
using System.Linq;
using XRSvc.DataPack;
using Jskj.AppLog;

/// <summary>
/// 设备管理器，负责PICO SDK的初始化、事件分发和设备管理
/// </summary>
public class DeviceManager
{
    /// <summary>
    /// 单例实例
    /// </summary>
    private static DeviceManager _instance;
    public static DeviceManager Instance => _instance ??= new DeviceManager();

    /// <summary>
    /// SDK调用器实例
    /// </summary>
    private SDKCaller _sdkCaller;

    /// <summary>
    /// 设备字典，按设备ID管理
    /// </summary>
    private Dictionary<string, DeviceInfo> _devices = new();

    /// <summary>
    /// 设备发现事件
    /// </summary>
    public event EventHandler<DeviceEventArgs> DeviceFound;
    /// <summary>
    /// 设备丢失事件
    /// </summary>
    public event EventHandler<DeviceEventArgs> DeviceLost;
    /// <summary>
    /// 电池电量变化事件
    /// </summary>
    public event EventHandler<DeviceEventArgs> BatteryChanged;
    /// <summary>
    /// 监控帧数据事件
    /// </summary>
    public event EventHandler<DeviceEventArgs> FrameReceived;
    /// <summary>
    /// 设备锁定事件
    /// </summary>
    public event EventHandler<DeviceEventArgs> DeviceLocked;
    /// <summary>
    /// 设备解锁事件
    /// </summary>
    public event EventHandler<DeviceEventArgs> DeviceUnlocked;

    /// <summary>
    /// 构造函数，初始化SDK并注册事件
    /// </summary>
    private DeviceManager()
    {
        _sdkCaller = new SDKCaller();
        _sdkCaller.DeviceFound += OnDeviceFound;
        _sdkCaller.DeviceLost += OnDeviceLost;
        _sdkCaller.BatteryChanged += OnBatteryChanged;
        _sdkCaller.FrameReceived += OnFrameReceived;
        
        // 初始化SDK
        int initResult = _sdkCaller.initSDK();
        if (initResult != 0)
        {
            throw new InvalidOperationException($"PICO SDK初始化失败，错误码: {initResult}");
        }
    }

    /// <summary>
    /// 反初始化SDK，释放资源
    /// </summary>
    public void Deinit()
    {
        _sdkCaller.deinitSDK();
    }

    /// <summary>
    /// 检查SDK是否已初始化
    /// </summary>
    /// <returns>是否已初始化</returns>
    public bool IsInitialized()
    {
        return _sdkCaller.IsInitialized();
    }

    /// <summary>
    /// 获取设备电池信息
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int GetBattery(string devID)
    {
        return _sdkCaller.getBattery(devID);
    }

    /// <summary>
    /// 开始监控设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int StartMonitor(string devID)
    {
        return _sdkCaller.startMonitor(devID);
    }

    /// <summary>
    /// 停止监控设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int StopMonitor(string devID)
    {
        return _sdkCaller.stopMonitor(devID);
    }

    /// <summary>
    /// 启动应用
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <param name="packageName">应用包名</param>
    /// <returns>操作结果，0表示成功</returns>
    public int StartPlayApp(string devID, string packageName)
    {
        if (!IsInitialized())
        {
            throw new InvalidOperationException("PICO SDK未初始化");
        }

        if (string.IsNullOrEmpty(devID))
        {
            throw new ArgumentException("设备ID不能为空", nameof(devID));
        }

        if (string.IsNullOrEmpty(packageName))
        {
            throw new ArgumentException("包名不能为空", nameof(packageName));
        }

        return _sdkCaller.startPlayApp(devID, packageName);
    }

    /// <summary>
    /// 停止应用
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <param name="packageName">应用包名</param>
    /// <returns>操作结果，0表示成功</returns>
    public int StopPlayApp(string devID, string packageName)
    {
        if (!IsInitialized())
        {
            throw new InvalidOperationException("PICO SDK未初始化");
        }

        if (string.IsNullOrEmpty(devID))
        {
            throw new ArgumentException("设备ID不能为空", nameof(devID));
        }

        if (string.IsNullOrEmpty(packageName))
        {
            throw new ArgumentException("包名不能为空", nameof(packageName));
        }

        return _sdkCaller.stopPlayApp(devID, packageName);
    }

    /// <summary>
    /// 锁定设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int LockDevice(string devID)
    {
        if (!IsInitialized())
        {
            throw new InvalidOperationException("PICO SDK未初始化");
        }

        if (string.IsNullOrEmpty(devID))
        {
            throw new ArgumentException("设备ID不能为空", nameof(devID));
        }

        return _sdkCaller.lockDevice(devID);
    }

    /// <summary>
    /// 解锁设备
    /// </summary>
    /// <param name="devID">设备ID</param>
    /// <returns>操作结果，0表示成功</returns>
    public int UnlockDevice(string devID)
    {
        if (!IsInitialized())
        {
            throw new InvalidOperationException("PICO SDK未初始化");
        }

        if (string.IsNullOrEmpty(devID))
        {
            throw new ArgumentException("设备ID不能为空", nameof(devID));
        }

        return _sdkCaller.unlockDevice(devID);
    }

    /// <summary>
    /// 获取所有已知设备
    /// </summary>
    public IReadOnlyDictionary<string, DeviceInfo> Devices => _devices;

    // 内部事件处理，更新设备字典并分发到外部
    private void OnDeviceFound(object sender, DeviceEventArgs e)
    {
        if (!_devices.ContainsKey(e.DevID))
        {
            // 创建完整的设备信息
            var info = CreateDeviceInfoFromSerial(e.DevID);
            _devices[e.DevID] = info;
            Log.Write(Level.INFO, $"新设备已添加到设备字典: ID={info.ID}, 序列号={info.SerialNumber}, 名称={info.Name}");
        }
        else
        {
            _devices[e.DevID].IsOnline = true;
            Log.Write(Level.INFO, $"设备重新上线: 序列号={e.DevID}");
        }
        DeviceFound?.Invoke(this, e);
    }

    /// <summary>
    /// 根据PICO设备序列号创建完整的设备信息
    /// </summary>
    /// <param name="serialNumber">PICO设备序列号</param>
    /// <returns>完整的设备信息</returns>
    private DeviceInfo CreateDeviceInfoFromSerial(string serialNumber)
    {
        // 尝试从预定义设备列表中找到匹配的设备
        var predefinedDevice = FindPredefinedDeviceBySerial(serialNumber);

        if (predefinedDevice != null)
        {
            // 使用预定义设备信息，但更新在线状态
            var info = new DeviceInfo
            {
                ID = predefinedDevice.ID,
                SerialNumber = serialNumber, // 使用实际的PICO序列号
                Name = predefinedDevice.Name,
                IpAddress = predefinedDevice.IpAddress,
                DeviceType = predefinedDevice.DeviceType,
                IsOnline = true,
                IsEnabled = true, // 发现的设备默认启用
                BatteryLevel = 0 // 电量稍后通过电量查询更新
            };
            Log.Write(Level.INFO, $"匹配到预定义设备: ID={info.ID}, 名称={info.Name}");
            return info;
        }
        else
        {
            // 如果没有找到预定义设备，创建一个新的设备信息
            var info = new DeviceInfo
            {
                ID = GenerateNewDeviceId(),
                SerialNumber = serialNumber,
                Name = $"PICO设备_{serialNumber.Substring(Math.Max(0, serialNumber.Length - 6))}",
                IpAddress = "未知",
                DeviceType = DeviceType.HMD, // PICO设备默认为HMD类型
                IsOnline = true,
                IsEnabled = true,
                BatteryLevel = 0
            };
            Log.Write(Level.INFO, $"创建新设备: ID={info.ID}, 名称={info.Name}");
            return info;
        }
    }

    /// <summary>
    /// 从预定义设备列表中查找匹配的设备
    /// 这里可以根据序列号模式或其他规则进行匹配
    /// </summary>
    /// <param name="serialNumber">PICO设备序列号</param>
    /// <returns>匹配的预定义设备，如果没有找到返回null</returns>
    private DeviceInfo FindPredefinedDeviceBySerial(string serialNumber)
    {
        try
        {
            // 尝试从设备映射配置中查找
            var mappedDevice = GetMappedDevice(serialNumber);
            if (mappedDevice != null)
            {
                Log.Write(Level.INFO, $"找到映射设备: 序列号={serialNumber} -> ID={mappedDevice.ID}");
                return mappedDevice;
            }

            // 如果没有明确映射，尝试智能匹配
            var smartMatchDevice = SmartMatchDevice(serialNumber);
            if (smartMatchDevice != null)
            {
                Log.Write(Level.INFO, $"智能匹配设备: 序列号={serialNumber} -> ID={smartMatchDevice.ID}");
                return smartMatchDevice;
            }

            Log.Write(Level.INFO, $"未找到匹配的预定义设备: 序列号={serialNumber}");
            return null;
        }
        catch (Exception ex)
        {
            Log.Write(Level.ERROR, $"查找预定义设备时出错: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 从设备映射配置中获取映射的设备
    /// </summary>
    /// <param name="serialNumber">PICO设备序列号</param>
    /// <returns>映射的设备信息，如果没有找到返回null</returns>
    private DeviceInfo GetMappedDevice(string serialNumber)
    {
        // 这里可以从配置文件或数据库中读取设备映射关系
        // 暂时使用硬编码的映射关系作为示例

        var deviceMappings = new Dictionary<string, int>
        {
            // 示例映射关系：PICO序列号 -> 系统设备ID
            // 实际使用时应该从配置文件读取
            // {"PA7N1DMGH3010660W", 8001},
            // {"PA7N1DMGH3010661W", 8002},
            // 可以根据实际的PICO设备序列号来配置
        };

        if (deviceMappings.ContainsKey(serialNumber))
        {
            int deviceId = deviceMappings[serialNumber];
            return GetPredefinedDeviceById(deviceId);
        }

        return null;
    }

    /// <summary>
    /// 智能匹配设备（基于序列号模式）
    /// </summary>
    /// <param name="serialNumber">PICO设备序列号</param>
    /// <returns>匹配的设备信息，如果没有找到返回null</returns>
    private DeviceInfo SmartMatchDevice(string serialNumber)
    {
        // 智能匹配逻辑：为新发现的设备分配一个可用的预定义设备ID
        // 优先分配给离线的预定义设备

        var availableDevices = GetAvailablePredefinedDevices();
        if (availableDevices.Any())
        {
            // 选择第一个可用的设备
            var selectedDevice = availableDevices.First();
            Log.Write(Level.INFO, $"智能分配设备: 序列号={serialNumber} -> 设备ID={selectedDevice.ID}({selectedDevice.Name})");
            return selectedDevice;
        }

        return null;
    }

    /// <summary>
    /// 根据设备ID获取预定义设备信息
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>预定义设备信息，如果没有找到返回null</returns>
    private DeviceInfo GetPredefinedDeviceById(int deviceId)
    {
        // 这里应该从配置文件或其他数据源获取预定义设备列表
        // 暂时返回一个示例设备
        return new DeviceInfo
        {
            ID = deviceId,
            SerialNumber = "", // 序列号将在调用处设置
            Name = $"{deviceId - 8000}号机", // 根据ID生成名称
            IpAddress = $"192.168.1.{100 + (deviceId - 8000)}", // 根据ID生成IP
            DeviceType = DeviceType.HMD,
            IsOnline = false,
            IsEnabled = false,
            BatteryLevel = 0
        };
    }

    /// <summary>
    /// 获取可用的预定义设备列表（离线且未被占用的设备）
    /// </summary>
    /// <returns>可用的预定义设备列表</returns>
    private List<DeviceInfo> GetAvailablePredefinedDevices()
    {
        var availableDevices = new List<DeviceInfo>();

        // 检查8001-8030范围内的设备
        for (int deviceId = 8001; deviceId <= 8030; deviceId++)
        {
            // 检查该设备ID是否已被占用
            bool isOccupied = _devices.Values.Any(d => d.ID == deviceId && d.IsOnline);

            if (!isOccupied)
            {
                var predefinedDevice = GetPredefinedDeviceById(deviceId);
                if (predefinedDevice != null)
                {
                    availableDevices.Add(predefinedDevice);
                }
            }
        }

        return availableDevices;
    }

    /// <summary>
    /// 生成新的设备ID
    /// </summary>
    /// <returns>新的设备ID</returns>
    private int GenerateNewDeviceId()
    {
        // 从9000开始分配动态发现的设备ID，避免与预定义设备冲突
        int baseId = 9000;
        while (_devices.Values.Any(d => d.ID == baseId))
        {
            baseId++;
        }
        return baseId;
    }

    private void OnDeviceLost(object sender, DeviceEventArgs e)
    {
        if (_devices.ContainsKey(e.DevID))
        {
            _devices[e.DevID].IsOnline = false;
        }
        DeviceLost?.Invoke(this, e);
    }

    private void OnBatteryChanged(object sender, DeviceEventArgs e)
    {
        if (_devices.ContainsKey(e.DevID))
        {
            _devices[e.DevID].BatteryLevel = e.Battery;
        }
        BatteryChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 监控帧数据事件处理
    /// </summary>
    /// <param name="sender">事件源</param>
    /// <param name="e">事件参数</param>
    private void OnFrameReceived(object sender, PICOSDK.DeviceEventArgs e)
    {
        FrameReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 设备锁定事件处理
    /// </summary>
    /// <param name="sender">事件源</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceLocked(object sender, PICOSDK.DeviceEventArgs e)
    {
        DeviceLocked?.Invoke(this, e);
    }

    /// <summary>
    /// 设备解锁事件处理
    /// </summary>
    /// <param name="sender">事件源</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceUnlocked(object sender, PICOSDK.DeviceEventArgs e)
    {
        DeviceUnlocked?.Invoke(this, e);
    }
}
} 