package com.gzcec.xrandroidclient.gameprogress;

import android.graphics.PorterDuff;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
import android.content.Context;
import android.widget.RadioGroup;
import android.widget.RadioButton;
import android.content.res.ColorStateList;

// 导入通信相关类
import com.gzcec.xrandroidclient.communication.MqttCommunicationManager;
import com.gzcec.xrandroidclient.communication.data.GameInfo;
import com.gzcec.xrandroidclient.communication.messages.game.GameListResponseMessage;
import com.gzcec.xrandroidclient.data.DeviceDataRepository;
import com.gzcec.xrandroidclient.data.GameDataRepository;
import com.gzcec.xrandroidclient.utils.XRLog;
import androidx.lifecycle.Observer;
import com.gzcec.xrandroidclient.communication.messages.game.*;
import com.gzcec.xrandroidclient.communication.messages.device.*;
import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.data.GameInfo;
import com.gzcec.xrandroidclient.config.NetworkConfig;
import com.gzcec.xrandroidclient.utils.NetworkDiagnostics;
import com.gzcec.xrandroidclient.gameprogress.GameProgressDeviceAdapter;
import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.DeviceIdManager;
import com.gzcec.xrandroidclient.R;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.app.ProgressDialog;
import java.util.List;

public class GameProgressFragment extends Fragment implements
    MqttCommunicationManager.GameControlListener,
    MqttCommunicationManager.DeviceStatusListener,
    MqttCommunicationManager.ConnectionListener {

    private static final String TAG = "GameProgressFragment";



    private View mView; // 添加成员变量保存视图引用
    private final String[] servers = {"80服服务器", "81服服务器", "82服服务器", "83服服务器"};
    private final String[] games = {"VR探险游戏", "VR射击游戏", "VR教育游戏"};
    private int selectedServer = 0;
    private String selectedGame = "";
    private GameProgressDeviceAdapter adapter;
    private final List<DeviceInfo> deviceList = new ArrayList<>();
    private DeviceDataRepository deviceRepository;
    private GameDataRepository gameRepository;
    private Button btnClearSelection;
    private Button btnSortById;
    private Button btnSortByBattery;
    private Button btnSelectTop12;
    private RadioGroup rgGameSelect;

    // MQTT通信管理器
    private MqttCommunicationManager mqttManager;
    private Handler mainHandler;
    private boolean isViewCreated = false;

    // 进度对话框
    private ProgressDialog progressDialog;

    // 游戏服务器状态
    private boolean isServerRunning = false;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Context context = getContext();
        if (context == null) return null;

        // 如果视图已经创建过，直接返回（避免重复初始化）
        if (mView != null && isViewCreated) {
            Log.d(TAG, "视图已存在，跳过重复创建");
            return mView;
        }

        Log.d(TAG, "创建GameProgressFragment视图");
        mView = inflater.inflate(R.layout.fragment_game_progress, container, false);
        mainHandler = new Handler(Looper.getMainLooper());

        // 获取数据仓库
        deviceRepository = DeviceDataRepository.getInstance();
        gameRepository = GameDataRepository.getInstance();

        // 使用MQTT通信管理器
        initMqttManager();

        // 初始化UI组件
        initViews();

        // 设置数据绑定
        setupDataBinding();

        // 连接到MQTT服务器
        connectToMqttServer();

        isViewCreated = true;
        return mView;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mqttManager != null && !mqttManager.isConnected()) {
            connectToMqttServer();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 不在onPause中断开连接，保持连接状态
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "GameProgressFragment销毁");
        // 不在Fragment销毁时断开MQTT连接，因为其他Fragment可能还需要使用
        // MQTT连接由全局管理器管理，在应用退出时才断开

        // 清理监听器
        if (mqttManager != null) {
            mqttManager.removeGameControlListener(this);
            mqttManager.removeDeviceStatusListener(this);
            mqttManager.removeConnectionListener(this);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "GameProgressFragment视图销毁");
        // 不清理mView，保持视图状态
        // isViewCreated = false;
    }

    /**
     * 初始化MQTT通信管理器
     */
    private void initMqttManager() {
        try {
            // 使用统一的MQTT通信管理器
            mqttManager = MqttCommunicationManager.getInstance();

            // 注册监听器
            mqttManager.addGameControlListener(this);
            mqttManager.addDeviceStatusListener(this);
            mqttManager.addConnectionListener(this);

            Log.d(TAG, "使用MQTT通信管理器");
        } catch (Exception e) {
            Log.e(TAG, "初始化MQTT通信管理器失败", e);
        }
    }

    /**
     * 设置数据绑定
     */
    private void setupDataBinding() {
        Log.d(TAG, "设置数据绑定");

        // 根据当前选中的服务器观察对应的设备列表
        Observer<List<DeviceInfo>> deviceListObserver = new Observer<List<DeviceInfo>>() {
            @Override
            public void onChanged(List<DeviceInfo> devices) {
                if (devices != null) {
                    Log.d(TAG, "数据绑定更新: 收到 " + devices.size() + " 台设备");

                    // 更新本地设备列表
                    deviceList.clear();
                    deviceList.addAll(devices);

                    // 通知适配器数据变化
                    if (adapter != null) {
                        adapter.notifyDataSetChanged();
                    }

                    Log.d(TAG, "设备列表UI已更新");
                }
            }
        };

        // 根据当前选中的服务器设置观察者
        updateDataBinding(deviceListObserver);

        // 观察设备统计信息
        deviceRepository.getStatisticsLiveData().observe(getViewLifecycleOwner(), new Observer<DeviceDataRepository.DeviceStatistics>() {
            @Override
            public void onChanged(DeviceDataRepository.DeviceStatistics statistics) {
                if (statistics != null) {
                    Log.d(TAG, "设备统计更新: " + statistics.toString());

                    // 更新UI显示统计信息
                    updateStatisticsDisplay(statistics);
                }
            }
        });

        // 观察游戏列表数据
        gameRepository.getGamesLiveData().observe(getViewLifecycleOwner(), new Observer<List<GameInfo>>() {
            @Override
            public void onChanged(List<GameInfo> games) {
                if (games != null) {
                    XRLog.d(TAG, "游戏列表更新: " + games.size() + " 个游戏");
                    updateGameSelection(games);
                }
            }
        });

        // 观察选中的游戏
        gameRepository.getSelectedGameLiveData().observe(getViewLifecycleOwner(), new Observer<GameInfo>() {
            @Override
            public void onChanged(GameInfo game) {
                if (game != null) {
                    XRLog.user(TAG, "游戏选择变化", game.getName());
                    selectedGame = game.getName(); // 使用游戏名称而不是包名
                }
            }
        });
    }

    /**
     * 根据服务器选择更新数据绑定
     */
    private void updateDataBinding(Observer<List<DeviceInfo>> observer) {
        // 移除之前的观察者
        deviceRepository.getServer80DevicesLiveData().removeObserver(observer);
        deviceRepository.getServer81DevicesLiveData().removeObserver(observer);

        // 根据当前选中的服务器添加新的观察者
        switch (selectedServer) {
            case 0: // 80服
                deviceRepository.getServer80DevicesLiveData().observe(getViewLifecycleOwner(), observer);
                Log.d(TAG, "绑定80服设备列表数据");
                break;
            case 1: // 81服
                deviceRepository.getServer81DevicesLiveData().observe(getViewLifecycleOwner(), observer);
                Log.d(TAG, "绑定81服设备列表数据");
                break;
            default:
                deviceRepository.getHmdDevicesLiveData().observe(getViewLifecycleOwner(), observer);
                Log.d(TAG, "绑定所有HMD设备列表数据");
                break;
        }
    }

    /**
     * 更新统计信息显示
     */
    private void updateStatisticsDisplay(DeviceDataRepository.DeviceStatistics statistics) {
        // 更新UI中的统计信息显示
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // 更新统计信息到UI
                Log.d(TAG, "统计信息: 总设备=" + statistics.getTotalDevices() +
                    ", 在线=" + statistics.getOnlineDevices() +
                    ", 已选=" + statistics.getSelectedDevices());

                // 可以在这里更新UI元素，比如：
                // - 更新标题栏显示在线设备数量
                // - 更新状态栏显示选中设备数量
                // - 更新游戏控制按钮的可用状态

                // 示例：更新游戏控制按钮状态
                updateGameControlButtonsState(statistics);
            });
        }
    }

    /**
     * 根据统计信息更新游戏控制按钮状态
     */
    private void updateGameControlButtonsState(DeviceDataRepository.DeviceStatistics statistics) {
        if (mView != null) {
            Button btnStartGame = mView.findViewById(R.id.btn_start_game);
            Button btnEndGame = mView.findViewById(R.id.btn_end_game);

            // 根据在线设备数量和选中设备数量来控制按钮状态
            boolean hasOnlineDevices = statistics.getOnlineDevices() > 0;
            boolean hasSelectedDevices = statistics.getSelectedDevices() > 0;

            if (btnStartGame != null) {
                btnStartGame.setEnabled(hasOnlineDevices && hasSelectedDevices);
                btnStartGame.setText("启动游戏 (" + statistics.getSelectedDevices() + "/" + statistics.getOnlineDevices() + ")");
            }

            if (btnEndGame != null) {
                btnEndGame.setEnabled(hasSelectedDevices);
                btnEndGame.setText("结束游戏 (" + statistics.getSelectedDevices() + ")");
            }
        }
    }

    /**
     * 连接到MQTT服务器（使用通信管理器）
     */
    private void connectToMqttServer() {
        try {
            // 使用MQTT通信管理器连接
            if (!mqttManager.isConnected()) {
                Log.i(TAG, "MQTT未连接，尝试连接");
                mqttManager.connect();
            } else {
                Log.d(TAG, "MQTT已连接，跳过重复连接");
                // 如果已连接，直接请求设备列表
                onConnected();
            }
        } catch (Exception e) {
            Log.e(TAG, "连接MQTT服务器失败", e);
        }
    }

    // ========== ConnectionListener 接口实现 ==========

    /**
     * MQTT连接成功回调
     */
    @Override
    public void onConnected() {
        mainHandler.post(() -> {
            if (getContext() != null) {
                Toast.makeText(getContext(), "已连接到服务器", Toast.LENGTH_SHORT).show();
            }
            android.util.Log.i(TAG, "MQTT连接成功");
            // 连接成功后自动请求设备列表
            refreshDeviceList();
            // 连接成功后自动请求游戏列表
            requestGameList();
        });
    }

    @Override
    public void onDisconnected() {
        mainHandler.post(() -> {
            if (getContext() != null) {
                Toast.makeText(getContext(), "与服务器断开连接", Toast.LENGTH_SHORT).show();
            }
            Log.w(TAG, "MQTT连接断开");
        });
    }

    @Override
    public void onConnectionError(String error) {
        mainHandler.post(() -> {
            Log.e(TAG, "MQTT连接错误: " + error);

            // 检查是否是网络不可达错误
            if (error.contains("EHOSTUNREACH") || error.contains("No route to host")) {
                Log.w(TAG, "检测到网络路由问题，尝试重新发现MQTT服务器");

                // 显示用户友好的错误信息
                if (getContext() != null) {
                    Toast.makeText(getContext(), "正在重新搜索服务器...", Toast.LENGTH_SHORT).show();
                }

                // 延迟重试，给用户时间看到提示
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    retryMqttConnection();
                }, 1000);

            } else {
                // 其他类型的错误
                if (getContext() != null) {
                    Toast.makeText(getContext(), "连接错误: " + error, Toast.LENGTH_LONG).show();
                } else {
                    Log.e(TAG, "连接错误但Context为null: " + error);
                }
            }
        });
    }

    /**
     * 显示网络错误信息
     */
    private void showNetworkError(String message) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // 这里可以显示Toast或Dialog
                Log.e(TAG, "网络错误: " + message);
            });
        }
    }

    /**
     * 尝试重新连接MQTT服务器
     */
    private void retryMqttConnection() {
        Log.i(TAG, "尝试重新发现和连接MQTT服务器...");

        // 先进行网络诊断
        performNetworkDiagnosis();

        // 网络配置由MqttCommunicationManager内部管理

        // 重新初始化MQTT通信管理器
        initMqttManager();

        // 延迟重连
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            connectToMqttServer();
        }, 3000);
    }

    /**
     * 执行网络诊断
     */
    private void performNetworkDiagnosis() {
        new Thread(() -> {
            try {
                Log.i(TAG, "开始网络诊断...");

                // 获取网络信息
                NetworkDiagnostics.NetworkInfo networkInfo = NetworkDiagnostics.getNetworkInfo(getContext());
                Log.i(TAG, "网络信息: " + networkInfo.toString());

                // 执行综合诊断
                String mqttHost = "*************"; // 默认主机
                int mqttPort = 1883; // 默认端口

                List<NetworkDiagnostics.DiagnosticResult> results =
                    NetworkDiagnostics.comprehensiveDiagnosis(getContext(), mqttHost, mqttPort);

                // 输出诊断结果
                for (NetworkDiagnostics.DiagnosticResult result : results) {
                    Log.i(TAG, "诊断结果: " + result.toString());
                }

                // 如果当前网段无法连接，尝试扫描其他网段
                if (!networkInfo.isConnected || networkInfo.localIp == null) {
                    Log.w(TAG, "网络连接异常，跳过主机扫描");
                    return;
                }

                // 扫描当前网段的活跃主机
                String localIp = networkInfo.localIp;
                String networkPrefix = getNetworkPrefix(localIp);
                if (networkPrefix != null) {
                    Log.i(TAG, "扫描网段: " + networkPrefix + ".1-100");
                    List<String> activeHosts = NetworkDiagnostics.scanActiveHosts(networkPrefix, 1, 100);
                    Log.i(TAG, "发现活跃主机: " + activeHosts.toString());

                    // 测试活跃主机的MQTT端口
                    for (String host : activeHosts) {
                        NetworkDiagnostics.DiagnosticResult mqttTest =
                            NetworkDiagnostics.testMqttConnection(host, 1883, 2000);
                        if (mqttTest.success) {
                            Log.i(TAG, "发现MQTT服务器: " + host);
                            // 配置由MqttCommunicationManager内部管理
                            break;
                        }
                    }
                }

            } catch (Exception e) {
                Log.e(TAG, "网络诊断失败", e);
            }
        }).start();
    }

    /**
     * 从IP地址获取网络前缀
     */
    private String getNetworkPrefix(String ip) {
        if (ip != null) {
            int lastDot = ip.lastIndexOf('.');
            if (lastDot > 0) {
                return ip.substring(0, lastDot);
            }
        }
        return null;
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        Context context = getContext();
        if (context == null) return;

        // 顶部服务器Tab
        LinearLayout serverTab = mView.findViewById(R.id.server_tab_container);
        serverTab.removeAllViews();
        for (int i = 0; i < servers.length; i++) {
            serverTab.addView(createServerTabButton(context, i));
        }

        // 初始化RecyclerView和Adapter
        RecyclerView recycler = mView.findViewById(R.id.recycler_devices);
        recycler.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new GameProgressDeviceAdapter(deviceList);
        recycler.setAdapter(adapter);

        // 监听设备勾选变化
        adapter.setOnSelectionChangedListener(() -> updateClearSelectionButtonText(btnClearSelection));

        // 监听设备游戏控制事件
        adapter.setOnGameControlListener(new GameProgressDeviceAdapter.OnGameControlListener() {
            @Override
            public void onJoinGame(DeviceInfo device) {
                startGameForSingleDevice(device);
            }

            @Override
            public void onExitGame(DeviceInfo device) {
                stopGameForSingleDevice(device);
            }
        });

        // 初始化游戏选择控件
        rgGameSelect = mView.findViewById(R.id.rg_game_select);
        initGameSelection();

        // 初始化操作按钮
        btnClearSelection = mView.findViewById(R.id.btn_clear_selection);
        btnSortById = mView.findViewById(R.id.btn_sort_by_id);
        btnSortByBattery = mView.findViewById(R.id.btn_sort_by_battery);
        btnSelectTop12 = mView.findViewById(R.id.btn_select_top12);

        // 测试MQTT按钮已移除

        // 初始化游戏选择和控制按钮
        initGameControls();

        // 初始化设备列表框架
        initializeDeviceListFramework();

        // 初始化设备列表操作按钮
        initDeviceListButtons();
    }

    /**
     * 初始化设备列表框架
     * 创建基础的设备列表结构，等待PC端传输详细信息
     */
    private void initializeDeviceListFramework() {
        deviceList.clear();

        // 获取初始化设备列表
        List<DeviceInfo> allDevices = DeviceInfo.createInitialDeviceList();

        // 根据选中的服务器筛选HMD设备
        for (DeviceInfo device : allDevices) {
            if (device.getDeviceType() != com.gzcec.xrandroidclient.device.DeviceType.HMD) continue;

            int serverPrefix = DeviceIdManager.getServerPrefix(device.getId());
            boolean shouldInclude = false;

            switch (selectedServer) {
                case 0: // 80服
                    shouldInclude = (serverPrefix == DeviceIdManager.SERVER_80);
                    break;
                case 1: // 81服
                    shouldInclude = (serverPrefix == DeviceIdManager.SERVER_81);
                    break;
                default: // 其他服务器显示全部
                    shouldInclude = true;
                    break;
            }

            if (shouldInclude) {
                deviceList.add(device);
            }
        }

        // 初始化RecyclerView和适配器
        RecyclerView recycler = mView.findViewById(R.id.recycler_devices);
        recycler.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new GameProgressDeviceAdapter(deviceList);
        recycler.setAdapter(adapter);

        // 监听设备勾选变化，刷新按钮文字
        adapter.setOnSelectionChangedListener(() -> updateClearSelectionButtonText(btnClearSelection));

        // 监听设备游戏控制事件
        adapter.setOnGameControlListener(new GameProgressDeviceAdapter.OnGameControlListener() {
            @Override
            public void onJoinGame(DeviceInfo device) {
                startGameForSingleDevice(device);
            }

            @Override
            public void onExitGame(DeviceInfo device) {
                stopGameForSingleDevice(device);
            }
        });

        // 更新UI
        adapter.notifyDataSetChanged();
        updateClearSelectionButtonText(btnClearSelection);

        android.util.Log.d(TAG, "设备列表框架初始化完成: " + deviceList.size() + " 台设备");
    }

    /**
     * 按服务器更新设备列表显示
     * 用于服务器切换时重新筛选设备（使用数据绑定）
     */
    private void updateDeviceListByServer() {
        android.util.Log.d(TAG, "服务器切换，更新数据绑定，当前服务器: " + selectedServer);

        // 如果设备列表为空，说明还没有从服务器获取数据，先初始化框架
        if (deviceList.isEmpty()) {
            android.util.Log.d(TAG, "设备列表为空，初始化框架");
            initializeDeviceListFramework();
        }

        // 重新设置数据绑定，自动更新设备列表
        setupDataBinding();

        android.util.Log.d(TAG, "服务器切换完成，数据绑定已更新");
    }

    /**
     * 初始化设备列表操作按钮
     */
    private void initDeviceListButtons() {
        // 初始化按钮文字和点击事件
        updateClearSelectionButtonText(btnClearSelection);
        btnClearSelection.setOnClickListener(v -> {
            boolean anySelected = false;
            for (DeviceInfo d : deviceList) {
                if (d.isSelected()) {
                    anySelected = true;
                    break;
                }
            }

            if (anySelected) {
                // 有勾选，取消所有勾选
                List<Integer> deviceIds = new ArrayList<>();
                for (DeviceInfo device : deviceList) {
                    deviceIds.add(device.getId());
                }
                deviceRepository.updateDeviceSelections(deviceIds, false);
                Log.d(TAG, "清空所有设备选择");
            } else {
                // 没有勾选，全选
                List<Integer> deviceIds = new ArrayList<>();
                for (DeviceInfo device : deviceList) {
                    deviceIds.add(device.getId());
                }
                deviceRepository.updateDeviceSelections(deviceIds, true);
                Log.d(TAG, "选择所有设备");
            }

            // 立即更新按钮文字
            updateClearSelectionButtonText(btnClearSelection);

            // 刷新UI
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        });

        // 设置排序按钮点击事件
        btnSortById.setOnClickListener(v -> {
            sortDevicesByIdAndRefresh();
        });

        btnSortByBattery.setOnClickListener(v -> {
            sortDevicesByBatteryAndRefresh();
        });

        btnSelectTop12.setOnClickListener(v -> {
            selectTop12DevicesAndRefresh();
        });

        // 声明并初始化服务器状态按钮
        android.widget.TextView tvStatus = mView.findViewById(R.id.tv_server_status);
        Button btnServerAction = mView.findViewById(R.id.btn_server_action);
        btnServerAction.setOnClickListener(v -> {
            // TODO: 实现服务器启动/停止逻辑
            isServerRunning = !isServerRunning;
            updateServerStatus(tvStatus, btnServerAction);
        });
        updateServerStatus(tvStatus, btnServerAction);

        // 声明并初始化底部批量操作按钮（移除开始按钮）
        Button btnStartGame = mView.findViewById(R.id.btn_start_game);
        Button btnEndGame = mView.findViewById(R.id.btn_end_game);

        // 设置游戏控制按钮点击事件
        btnStartGame.setOnClickListener(v -> startSelectedGame());
        btnEndGame.setOnClickListener(v -> endSelectedGame());

        // 统一设置所有按钮样式（移除开始按钮）
        applyButtonStyle(
            btnSortById,
            btnSortByBattery,
            btnSelectTop12,
            btnClearSelection,
            btnServerAction,
            btnStartGame,
            btnEndGame
        );
    }

    /**
     * 初始化游戏控制相关UI
     */
    private void initGameControls() {
        // 初始化游戏选择
        if (games.length > 0) {
            selectedGame = games[0];
        }
    }

    /**
     * 更新服务器状态显示
     */
    private void updateServerStatus(android.widget.TextView tvStatus, Button btnServerAction) {
        tvStatus.setText(isServerRunning ? "服务器已启动" : "服务器未启动");
        tvStatus.setTextColor(isServerRunning ? 0xFF00FF00 : 0xFFFF0000);
        btnServerAction.setText(isServerRunning ? "停止服务器" : "启动服务器");
    }

    /**
     * 启动选中的游戏
     */
    private void startSelectedGame() {
        List<String> selectedDeviceIds = getSelectedDeviceIds();
        if (selectedDeviceIds.isEmpty()) {
            showToast("请选择至少一个设备");
            return;
        }

        if (selectedGame.isEmpty()) {
            showToast("请选择游戏");
            return;
        }

        if (mqttManager == null || !mqttManager.isConnected()) {
            showToast("未连接到服务器");
            return;
        }

        // 构建游戏信息
        GameInfo gameInfo = new GameInfo();
        gameInfo.setId(getGameIdByName(selectedGame));
        gameInfo.setName(selectedGame);
        gameInfo.setPackageName(getGamePackageByName(selectedGame));

        android.util.Log.d(TAG, "批量启动游戏: " + selectedGame + ", 设备数量: " + selectedDeviceIds.size());

        // 立即显示选中设备的"游戏启动中"状态
        updateSelectedDevicesGameStartingStatus(selectedDeviceIds, true);
        showToast("正在为 " + selectedDeviceIds.size() + " 台设备启动游戏，请等待状态更新...");

        // 发送游戏启动请求
        mqttManager.startGame(gameInfo, selectedDeviceIds, new MqttCommunicationManager.ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                mainHandler.post(() -> {
                    android.util.Log.d(TAG, "批量游戏启动请求已发送，等待PC端状态更新");
                    // 不再显示Toast，等待PC端的设备状态更新来反映真实状态
                });
            }

            @Override
            public void onTimeout() {
                mainHandler.post(() -> {
                    // 请求超时，恢复设备状态
                    updateSelectedDevicesGameStartingStatus(selectedDeviceIds, false);
                    showToast("批量游戏启动请求超时");
                });
            }
        });
    }

    // beginSelectedGame方法已移除，因为开始游戏按钮已被删除

    /**
     * 停止选中设备的游戏
     */
    private void stopSelectedGame() {
        // 获取选中的设备
        List<String> selectedDeviceIds = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.isSelected()) {
                selectedDeviceIds.add(String.valueOf(device.getId()));
            }
        }

        if (selectedDeviceIds.isEmpty()) {
            showToast("请先选择要停止游戏的设备");
            return;
        }

        // 检查MQTT连接
        if (mqttManager == null || !mqttManager.isConnected()) {
            showToast("未连接到服务器");
            return;
        }

        // 构建游戏信息（使用当前选择的游戏或默认信息）
        GameInfo gameInfo = new GameInfo();
        if (selectedGame != null && !selectedGame.isEmpty()) {
            gameInfo.setId(getGameIdByName(selectedGame));
            gameInfo.setName(selectedGame);
            gameInfo.setPackageName(getGamePackageByName(selectedGame));
        } else {
            // 如果没有选择游戏，使用默认信息
            gameInfo.setId(0);
            gameInfo.setName("当前游戏");
            gameInfo.setPackageName("");
        }

        android.util.Log.d(TAG, "批量停止游戏，设备数量: " + selectedDeviceIds.size());

        // 立即显示选中设备的"游戏停止中"状态
        updateSelectedDevicesGameStoppingStatus(selectedDeviceIds, true);
        showToast("正在为 " + selectedDeviceIds.size() + " 台设备停止游戏，请等待状态更新...");

        // 发送游戏停止请求
        mqttManager.stopGame(gameInfo, selectedDeviceIds, new MqttCommunicationManager.ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                mainHandler.post(() -> {
                    android.util.Log.d(TAG, "批量游戏停止请求已发送，等待PC端状态更新");
                    // 不再显示Toast，等待PC端的设备状态更新来反映真实状态
                });
            }

            @Override
            public void onTimeout() {
                mainHandler.post(() -> {
                    // 请求超时，恢复设备状态
                    updateSelectedDevicesGameStoppingStatus(selectedDeviceIds, false);
                    showToast("批量游戏停止请求超时");
                });
            }
        });
    }

    /**
     * 结束游戏
     */
    private void endSelectedGame() {
        stopSelectedGame();
    }

    /**
     * 为单个设备启动游戏
     */
    private void startGameForSingleDevice(DeviceInfo device) {
        // 检查游戏是否已选择
        if (selectedGame.isEmpty()) {
            showToast("请先选择游戏");
            return;
        }

        // 检查MQTT连接
        if (mqttManager == null || !mqttManager.isConnected()) {
            showToast("未连接到服务器");
            return;
        }

        // 检查设备状态
        if (!device.isOnline()) {
            showToast("设备 " + device.getName() + " 未在线");
            return;
        }

        if (device.isInGame()) {
            showToast("设备 " + device.getName() + " 已在游戏中");
            return;
        }

        // 构建游戏信息
        GameInfo gameInfo = new GameInfo();
        gameInfo.setId(getGameIdByName(selectedGame));
        gameInfo.setName(selectedGame);
        gameInfo.setPackageName(getGamePackageByName(selectedGame));

        // 单个设备ID列表
        List<String> deviceIds = new ArrayList<>();
        deviceIds.add(String.valueOf(device.getId()));

        android.util.Log.d(TAG, "为单个设备启动游戏: " + device.getName() + " (ID: " + device.getId() + "), 游戏: " + selectedGame);

        // 立即显示"游戏启动中"状态，不使用阻塞式对话框
        updateDeviceGameStartingStatus(device, true);
        showToast("正在为设备 " + device.getName() + " 启动游戏，请等待状态更新...");

        // 发送游戏启动请求
        mqttManager.startGame(gameInfo, deviceIds, new MqttCommunicationManager.ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                mainHandler.post(() -> {
                    android.util.Log.d(TAG, "设备 " + device.getName() + " 游戏启动请求已发送，等待PC端状态更新");
                    // 不再显示Toast，等待PC端的设备状态更新来反映真实状态
                });
            }

            @Override
            public void onTimeout() {
                mainHandler.post(() -> {
                    // 请求超时，恢复设备状态
                    updateDeviceGameStartingStatus(device, false);
                    showToast("设备 " + device.getName() + " 游戏启动请求超时");
                });
            }
        });
    }

    /**
     * 更新设备游戏启动中状态
     */
    private void updateDeviceGameStartingStatus(DeviceInfo device, boolean isStarting) {
        try {
            // 设置设备的临时状态，表示正在启动游戏
            device.setGameStarting(isStarting);

            // 刷新设备列表显示
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

            android.util.Log.d(TAG, "设备 " + device.getName() + " 游戏启动状态更新: " + (isStarting ? "启动中" : "已完成"));
        } catch (Exception e) {
            android.util.Log.e(TAG, "更新设备游戏启动状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新设备游戏停止中状态
     */
    private void updateDeviceGameStoppingStatus(DeviceInfo device, boolean isStopping) {
        try {
            // 设置设备的临时状态，表示正在停止游戏
            device.setGameStopping(isStopping);

            // 刷新设备列表显示
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

            android.util.Log.d(TAG, "设备 " + device.getName() + " 游戏停止状态更新: " + (isStopping ? "停止中" : "已完成"));
        } catch (Exception e) {
            android.util.Log.e(TAG, "更新设备游戏停止状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新选中设备的游戏启动中状态
     */
    private void updateSelectedDevicesGameStartingStatus(List<String> deviceIds, boolean isStarting) {
        try {
            DeviceDataRepository repository = DeviceDataRepository.getInstance();
            for (String deviceIdStr : deviceIds) {
                int deviceId = Integer.parseInt(deviceIdStr);
                DeviceInfo device = repository.getDeviceById(deviceId);
                if (device != null) {
                    device.setGameStarting(isStarting);
                    android.util.Log.d(TAG, "设备 " + device.getName() + " 批量游戏启动状态更新: " + (isStarting ? "启动中" : "已完成"));
                }
            }

            // 刷新设备列表显示
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "批量更新设备游戏启动状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新选中设备的游戏停止中状态
     */
    private void updateSelectedDevicesGameStoppingStatus(List<String> deviceIds, boolean isStopping) {
        try {
            DeviceDataRepository repository = DeviceDataRepository.getInstance();
            for (String deviceIdStr : deviceIds) {
                int deviceId = Integer.parseInt(deviceIdStr);
                DeviceInfo device = repository.getDeviceById(deviceId);
                if (device != null) {
                    device.setGameStopping(isStopping);
                    android.util.Log.d(TAG, "设备 " + device.getName() + " 批量游戏停止状态更新: " + (isStopping ? "停止中" : "已完成"));
                }
            }

            // 刷新设备列表显示
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "批量更新设备游戏停止状态失败: " + e.getMessage());
        }
    }

    /**
     * 清除所有设备的游戏启动中状态
     */
    private void clearAllGameStartingStatus() {
        try {
            DeviceDataRepository repository = DeviceDataRepository.getInstance();
            List<DeviceInfo> devices = repository.getAllDevices();

            boolean hasChanges = false;
            for (DeviceInfo device : devices) {
                if (device.isGameStarting()) {
                    device.setGameStarting(false);
                    hasChanges = true;
                    android.util.Log.d(TAG, "清除设备 " + device.getName() + " 的启动中状态");
                }
                if (device.isGameStopping()) {
                    device.setGameStopping(false);
                    hasChanges = true;
                    android.util.Log.d(TAG, "清除设备 " + device.getName() + " 的停止中状态");
                }
            }

            // 如果有变化，刷新UI
            if (hasChanges && adapter != null) {
                adapter.notifyDataSetChanged();
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "清除游戏启动状态失败: " + e.getMessage());
        }
    }

    /**
     * 为单个设备停止游戏
     */
    private void stopGameForSingleDevice(DeviceInfo device) {
        // 检查MQTT连接
        if (mqttManager == null || !mqttManager.isConnected()) {
            showToast("未连接到服务器");
            return;
        }

        // 检查设备状态
        if (!device.isOnline()) {
            showToast("设备 " + device.getName() + " 未在线");
            return;
        }

        if (!device.isInGame()) {
            showToast("设备 " + device.getName() + " 未在游戏中");
            return;
        }

        // 构建游戏信息（使用设备当前运行的游戏信息）
        GameInfo gameInfo = new GameInfo();
        if (selectedGame != null && !selectedGame.isEmpty()) {
            gameInfo.setId(getGameIdByName(selectedGame));
            gameInfo.setName(selectedGame);
            gameInfo.setPackageName(getGamePackageByName(selectedGame));
        } else {
            // 如果没有选择游戏，使用设备当前游戏信息
            gameInfo.setId(0); // 使用默认ID
            gameInfo.setName("当前游戏");
            gameInfo.setPackageName(device.getCurrentGamePackage());
        }

        // 单个设备ID列表
        List<String> deviceIds = new ArrayList<>();
        deviceIds.add(String.valueOf(device.getId()));

        android.util.Log.d(TAG, "为单个设备停止游戏: " + device.getName() + " (ID: " + device.getId() + ")");

        // 立即显示"游戏停止中"状态，不使用阻塞式对话框
        updateDeviceGameStoppingStatus(device, true);
        showToast("正在为设备 " + device.getName() + " 停止游戏，请等待状态更新...");

        // 发送游戏停止请求
        mqttManager.stopGame(gameInfo, deviceIds, new MqttCommunicationManager.ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                mainHandler.post(() -> {
                    android.util.Log.d(TAG, "设备 " + device.getName() + " 游戏停止请求已发送，等待PC端状态更新");
                    // 不再显示Toast，等待PC端的设备状态更新来反映真实状态
                });
            }

            @Override
            public void onTimeout() {
                mainHandler.post(() -> {
                    // 请求超时，恢复设备状态
                    updateDeviceGameStoppingStatus(device, false);
                    showToast("设备 " + device.getName() + " 游戏停止请求超时");
                });
            }
        });
    }

    /**
     * 获取选中的设备ID列表
     */
    private List<String> getSelectedDeviceIds() {
        List<String> selectedIds = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.isSelected()) {
                selectedIds.add(String.valueOf(device.getId()));
            }
        }
        return selectedIds;
    }

    /**
     * 根据游戏名称获取游戏ID
     */
    private int getGameIdByName(String gameName) {
        android.util.Log.d(TAG, "查找游戏ID，游戏名称: " + gameName);

        // 首先尝试从游戏仓库中查找
        GameInfo selectedGameInfo = gameRepository.getSelectedGame();
        if (selectedGameInfo != null && selectedGameInfo.getName().equals(gameName)) {
            android.util.Log.d(TAG, "从选中游戏找到ID: " + selectedGameInfo.getId());
            return selectedGameInfo.getId();
        }

        // 如果没有找到，尝试从游戏列表中查找
        List<GameInfo> allGames = gameRepository.getGames();
        if (allGames != null) {
            android.util.Log.d(TAG, "从游戏列表查找，总游戏数: " + allGames.size());
            for (GameInfo game : allGames) {
                android.util.Log.d(TAG, "检查游戏: ID=" + game.getId() + ", 名称=" + game.getName());
                if (game.getName().equals(gameName)) {
                    android.util.Log.d(TAG, "找到匹配游戏，ID: " + game.getId());
                    return game.getId();
                }
            }
        }

        // 兼容旧的硬编码游戏（使用10000+的ID避免冲突）
        for (int i = 0; i < games.length; i++) {
            if (games[i].equals(gameName)) {
                int legacyId = 10000 + i + 1; // 10001, 10002, 10003...
                android.util.Log.d(TAG, "使用兼容ID: " + legacyId + " 对应游戏: " + gameName);
                return legacyId;
            }
        }

        // 如果都没找到，返回默认ID
        android.util.Log.w(TAG, "未找到游戏ID，游戏名称: " + gameName + "，使用默认ID: 10001");
        return 10001; // 使用PC端实际存在的游戏ID
    }

    /**
     * 根据游戏名称获取游戏包名
     */
    private String getGamePackageByName(String gameName) {
        android.util.Log.d(TAG, "查找游戏包名，游戏名称: " + gameName);

        // 首先尝试从游戏仓库中查找
        GameInfo selectedGameInfo = gameRepository.getSelectedGame();
        if (selectedGameInfo != null && selectedGameInfo.getName().equals(gameName)) {
            android.util.Log.d(TAG, "从选中游戏找到包名: " + selectedGameInfo.getPackageName());
            return selectedGameInfo.getPackageName();
        }

        // 如果没有找到，尝试从游戏列表中查找
        List<GameInfo> allGames = gameRepository.getGames();
        if (allGames != null) {
            android.util.Log.d(TAG, "从游戏列表查找包名，总游戏数: " + allGames.size());
            for (GameInfo game : allGames) {
                android.util.Log.d(TAG, "检查游戏包名: 名称=" + game.getName() + ", 包名=" + game.getPackageName());
                if (game.getName().equals(gameName)) {
                    android.util.Log.d(TAG, "找到匹配游戏包名: " + game.getPackageName());
                    return game.getPackageName();
                }
            }
        }

        // 兼容旧的硬编码游戏（映射到PC端实际游戏）
        switch (gameName) {
            case "VR探险游戏":
                android.util.Log.d(TAG, "使用兼容包名: com.fantawildgames.ratitT (唐诡)");
                return "com.fantawildgames.ratitT"; // 映射到唐诡
            case "VR射击游戏":
                android.util.Log.d(TAG, "使用兼容包名: com.fantawildgames.ZombieApocalypse (秦陵)");
                return "com.fantawildgames.ZombieApocalypse"; // 映射到秦陵
            case "VR教育游戏":
                android.util.Log.d(TAG, "使用兼容包名: com.fantawildgames.slimeT (长征)");
                return "com.fantawildgames.slimeT"; // 映射到长征
            default:
                // 如果都没找到，返回默认包名（使用PC端实际存在的游戏包名）
                android.util.Log.w(TAG, "未找到游戏包名，游戏名称: " + gameName + "，使用默认包名: com.fantawildgames.ratitT");
                return "com.fantawildgames.ratitT"; // 唐诡的包名
        }
    }

    private void onServerTabChanged() { // 这里只做UI刷新，实际可根据服务器切换加载不同数据
        if (getView() == null) return;
        LinearLayout serverTab = getView().findViewById(R.id.server_tab_container);
        for (int i = 0; i < serverTab.getChildCount(); i++) {
            Button btn = (Button) serverTab.getChildAt(i);
            btn.setTextColor(i == selectedServer ? 0xFF00FFF0 : 0xFFFFFFFF);
            btn.setSelected(i == selectedServer); // 新增：同步selected状态
        }

    }

    private int extractDeviceNumber(String name) {
        try {
            return Integer.parseInt(name.replaceAll("[^0-9]", ""));
        } catch (Exception e) {
            return 0;
        }
    }

    private void updateClearSelectionButtonText(Button btn) {
        if (btn == null) return;

        // 使用主线程Handler确保UI更新在主线程执行
        if (mainHandler != null) {
            mainHandler.post(() -> {
                boolean anySelected = false;
                for (DeviceInfo d : deviceList) {
                    if (d.isSelected()) {
                        anySelected = true;
                        break;
                    }
                }
                btn.setText(anySelected ? "取消所有勾选" : "全选");
            });
        } else {
            // 如果mainHandler为null，直接在当前线程更新
            boolean anySelected = false;
            for (DeviceInfo d : deviceList) {
                if (d.isSelected()) {
                    anySelected = true;
                    break;
                }
            }
            btn.setText(anySelected ? "取消所有勾选" : "全选");
        }
    }

    // 提取创建服务器Tab按钮的方法
    private Button createServerTabButton(Context context, int index) {
        Button btn = new Button(context);
        btn.setText(servers[index]);
        btn.setTextColor(index == selectedServer ? 0xFF00FFF0 : 0xFFFFFFFF);
        btn.setBackgroundResource(R.drawable.bg_button_selector);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1f);
        lp.setMargins(4, 0, 4, 0);
        btn.setLayoutParams(lp);
        btn.setSelected(index == selectedServer); // 新增：初始selected状态
        btn.setOnClickListener(v -> {
            selectedServer = index;
            onServerTabChanged();
            updateDeviceListByServer();
        });
        return btn;
    }

    // 统一按钮样式方法
    private void applyButtonStyle(Button... buttons) {
        for (Button btn : buttons) {
            if (btn != null) {
                btn.setBackgroundResource(R.drawable.bg_button_selector);
                btn.setForeground(null);
                btn.setBackgroundTintList(null);
            }
        }
    }

    // ========== MQTT监听器实现 ==========

    @Override
    public void onGameListResponse(GameListResponseMessage response) {
        XRLog.enter(TAG, "onGameListResponse");

        mainHandler.post(() -> {
            if (response.isSuccess() && response.getGames() != null) {
                XRLog.i(TAG, "收到游戏列表响应: " + response.getTotalCount() + " 个游戏");
                gameRepository.updateGames(response.getGames());
                gameRepository.setLoading(false);
                gameRepository.clearError();
            } else {
                String error = response.getErrorMessage();
                XRLog.e(TAG, "游戏列表响应失败: " + error);
                gameRepository.setError(error != null ? error : "获取游戏列表失败");
                gameRepository.setLoading(false);
            }
        });

        XRLog.exit(TAG, "onGameListResponse");
    }

    @Override
    public void onGameStartResponse(GameStartResponseMessage response) {
        mainHandler.post(() -> {
            if (response.isSuccess()) {
                Toast.makeText(getContext(), "游戏启动成功", Toast.LENGTH_SHORT).show();
                android.util.Log.d(TAG, "游戏启动成功，成功设备数: " + response.getResults().size());

                // 刷新设备列表以更新游戏状态
                refreshDeviceList();
            } else {
                Toast.makeText(getContext(), "游戏启动失败: " + response.getErrorMessage(), Toast.LENGTH_LONG).show();
                android.util.Log.e(TAG, "游戏启动失败: " + response.getErrorMessage());
            }
        });
    }

    @Override
    public void onGameStopResponse(GameStopResponseMessage response) {
        mainHandler.post(() -> {
            if (response.isSuccess()) {
                Toast.makeText(getContext(), "游戏停止成功", Toast.LENGTH_SHORT).show();
                android.util.Log.d(TAG, "游戏停止成功，成功设备数: " + response.getResults().size());

                // 刷新设备列表以更新游戏状态
                refreshDeviceList();
            } else {
                Toast.makeText(getContext(), "游戏停止失败: " + response.getErrorMessage(), Toast.LENGTH_LONG).show();
                android.util.Log.e(TAG, "游戏停止失败: " + response.getErrorMessage());
            }
        });
    }

    @Override
    public void onGameProgressUpdate(GameProgressUpdateMessage update) {
        mainHandler.post(() -> {
            android.util.Log.d(TAG, "收到游戏进度更新");
            // TODO: 更新游戏进度显示
        });
    }

    // ========== DeviceStatusListener 接口实现 ==========

    @Override
    public void onDeviceListResponse(DeviceListResponseMessage response) {
        mainHandler.post(() -> {
            android.util.Log.d(TAG, "收到设备列表: " + response.getDevices().size() + " 台设备");
            android.util.Log.d(TAG, "总设备数: " + response.getTotalCount() + ", 在线设备数: " + response.getOnlineCount());

            // 打印前几个设备的详细信息
            for (int i = 0; i < Math.min(3, response.getDevices().size()); i++) {
                DeviceInfo device = response.getDevices().get(i);
                android.util.Log.d(TAG, "设备 " + i + ": ID=" + device.getId() +
                    ", 类型=" + device.getDeviceType() +
                    ", 在线=" + device.isOnline() +
                    ", 名称=" + device.getName());
            }

            // 更新设备列表
            updateDeviceListFromServer(response.getDevices());

            // 移除弹窗提示，仅记录日志
            android.util.Log.d(TAG, "设备列表已更新: " + response.getTotalCount() + " 台设备，" + response.getOnlineCount() + " 台在线");
        });
    }

    @Override
    public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update) {
        mainHandler.post(() -> {
            android.util.Log.d(TAG, "收到设备状态更新");

            // 更新设备状态
            updateDeviceListFromServer(update.getDevices());

            // 清除所有设备的"启动中"状态，因为PC端已经发送了最新状态
            clearAllGameStartingStatus();
        });
    }



    /**
     * 刷新设备列表
     */
    private void refreshDeviceList() {
        if (mqttManager != null && mqttManager.isConnected()) {
            Log.i(TAG, "手动刷新设备列表");
            mqttManager.requestDeviceList(new MqttCommunicationManager.ResponseCallback() {
                @Override
                public void onResponse(BaseMessage response) {
                    Log.d(TAG, "设备列表请求回调: " + (response != null ? "收到响应" : "null"));
                }

                @Override
                public void onTimeout() {
                    Log.w(TAG, "设备列表请求超时");
                }
            });
        } else {
            Log.w(TAG, "MQTT通信管理器未连接，无法刷新设备列表");
            Toast.makeText(getContext(), "MQTT未连接，无法刷新设备列表", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 从服务器响应更新设备列表
     */
    private void updateDeviceListFromServer(List<DeviceInfo> serverDevices) {
        if (serverDevices == null) return;

        // 如果设备列表为空，先初始化框架
        if (deviceList.isEmpty()) {
            initializeDeviceListFramework();
        }

        // 数据已通过DeviceDataRepository自动更新，无需手动缓存

        // 使用DeviceInfo的统一更新方法
        int updatedCount = DeviceInfo.updateDeviceListFromServer(deviceList, serverDevices);

        // 根据当前选中的服务器重新筛选设备列表
        List<DeviceInfo> filteredDevices = new ArrayList<>();
        android.util.Log.d(TAG, "开始筛选设备，当前选中服务器: " + selectedServer + ", 原始设备数量: " + deviceList.size());

        for (DeviceInfo device : deviceList) {
            android.util.Log.d(TAG, "检查设备: ID=" + device.getId() + ", 类型=" + device.getDeviceType() + ", 名称=" + device.getName());

            if (device.getDeviceType() != com.gzcec.xrandroidclient.device.DeviceType.HMD) {
                android.util.Log.d(TAG, "跳过非头显设备: " + device.getId());
                continue;
            }

            int serverPrefix = DeviceIdManager.getServerPrefix(device.getId());
            boolean shouldInclude = false;
            android.util.Log.d(TAG, "设备 " + device.getId() + " 的服务器前缀: " + serverPrefix);

            switch (selectedServer) {
                case 0: // 80服
                    shouldInclude = (serverPrefix == DeviceIdManager.SERVER_80);
                    android.util.Log.d(TAG, "80服筛选: " + serverPrefix + " == " + DeviceIdManager.SERVER_80 + " = " + shouldInclude);
                    break;
                case 1: // 81服
                    shouldInclude = (serverPrefix == DeviceIdManager.SERVER_81);
                    android.util.Log.d(TAG, "81服筛选: " + serverPrefix + " == " + DeviceIdManager.SERVER_81 + " = " + shouldInclude);
                    break;
                default: // 其他服务器显示全部
                    shouldInclude = true;
                    android.util.Log.d(TAG, "显示全部设备");
                    break;
            }

            if (shouldInclude) {
                filteredDevices.add(device);
                android.util.Log.d(TAG, "添加设备到筛选列表: " + device.getId());
            } else {
                android.util.Log.d(TAG, "设备不符合筛选条件: " + device.getId());
            }
        }

        android.util.Log.d(TAG, "筛选完成，筛选后设备数量: " + filteredDevices.size());

        // 更新显示的设备列表
        deviceList.clear();
        deviceList.addAll(filteredDevices);

        // 通知适配器数据变化
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }

        // 更新按钮状态
        updateClearSelectionButtonText(btnClearSelection);

        android.util.Log.d(TAG, "设备列表已更新: " + updatedCount + " 台设备信息已更新");
    }

    // testMqttCommunication方法已移除，因为测试MQTT按钮已被删除

    /**
     * 初始化游戏选择功能
     */
    private void initGameSelection() {
        XRLog.enter(TAG, "initGameSelection");

        if (rgGameSelect == null) {
            XRLog.w(TAG, "游戏选择控件未找到");
            return;
        }

        // 设置游戏选择监听器
        rgGameSelect.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                RadioButton selectedRadioButton = group.findViewById(checkedId);
                if (selectedRadioButton != null) {
                    GameInfo game = (GameInfo) selectedRadioButton.getTag();
                    if (game != null) {
                        gameRepository.setSelectedGame(game);
                        XRLog.user(TAG, "选择游戏", game.getName());
                    }
                }
            }
        });

        // 请求游戏列表
        requestGameList();

        XRLog.exit(TAG, "initGameSelection");
    }

    /**
     * 请求游戏列表
     */
    private void requestGameList() {
        XRLog.enter(TAG, "requestGameList");

        if (mqttManager == null || !mqttManager.isConnected()) {
            XRLog.w(TAG, "MQTT未连接，无法请求游戏列表");
            return;
        }

        gameRepository.setLoading(true);

        mqttManager.requestGameList(true, new MqttCommunicationManager.ResponseCallback() {
            @Override
            public void onResponse(com.gzcec.xrandroidclient.communication.messages.base.BaseMessage response) {
                if (response instanceof GameListResponseMessage) {
                    GameListResponseMessage gameListResponse = (GameListResponseMessage) response;

                    if (gameListResponse.isSuccess() && gameListResponse.getGames() != null) {
                        XRLog.i(TAG, "游戏列表请求成功: " + gameListResponse.getTotalCount() + " 个游戏");
                        gameRepository.updateGames(gameListResponse.getGames());
                    } else {
                        String error = gameListResponse.getErrorMessage();
                        XRLog.e(TAG, "游戏列表请求失败: " + error);
                        gameRepository.setError(error != null ? error : "获取游戏列表失败");
                    }
                } else {
                    XRLog.e(TAG, "收到错误的游戏列表响应类型");
                    gameRepository.setError("响应格式错误");
                }

                gameRepository.setLoading(false);
            }

            @Override
            public void onTimeout() {
                XRLog.w(TAG, "游戏列表请求超时");
                gameRepository.setError("请求超时");
                gameRepository.setLoading(false);
            }
        });

        XRLog.exit(TAG, "requestGameList");
    }

    /**
     * 更新游戏选择UI
     */
    private void updateGameSelection(List<GameInfo> games) {
        XRLog.enter(TAG, "updateGameSelection");

        if (rgGameSelect == null || getActivity() == null) {
            XRLog.w(TAG, "无法更新游戏选择UI：控件或Activity为空");
            return;
        }

        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    // 清除现有的选项
                    rgGameSelect.removeAllViews();

                    if (games == null || games.isEmpty()) {
                        // 添加"无可用游戏"选项
                        RadioButton noGameButton = new RadioButton(getContext());
                        noGameButton.setText("无可用游戏");
                        noGameButton.setEnabled(false);

                        // 设置样式
                        noGameButton.setTextColor(getResources().getColor(R.color.white, null));
                        noGameButton.setTextSize(14);
                        noGameButton.setPadding(8, 8, 8, 8);

                        rgGameSelect.addView(noGameButton);
                        XRLog.d(TAG, "显示无可用游戏提示");
                        return;
                    }

                    // 添加游戏选项
                    boolean hasSelectedGame = false;
                    for (GameInfo game : games) {
                        if (game.isAvailable() && game.isEnabled()) {
                            RadioButton radioButton = new RadioButton(getContext());
                            radioButton.setText(game.getName() + " (" + game.getVersion() + ")");
                            radioButton.setTag(game);
                            radioButton.setId(View.generateViewId());

                            // 设置样式
                            radioButton.setTextColor(getResources().getColor(R.color.white, null));
                            radioButton.setTextSize(14);
                            radioButton.setPadding(8, 8, 8, 8);

                            // 确保RadioButton圆点可见
                            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                                radioButton.setButtonTintList(ColorStateList.valueOf(
                                    getResources().getColor(R.color.white, null)));
                            }

                            // 使用自定义的小尺寸圆点
                            radioButton.setButtonDrawable(R.drawable.radio_button_small);

                            rgGameSelect.addView(radioButton);

                            // 如果是当前选中的游戏，设置为选中状态
                            if (game.getPackageName().equals(selectedGame)) {
                                rgGameSelect.check(radioButton.getId());
                                hasSelectedGame = true;
                            }

                            XRLog.d(TAG, "添加游戏选项: " + game.getName());
                        }
                    }

                    // 如果没有选中的游戏，默认选中第一个
                    if (!hasSelectedGame && rgGameSelect.getChildCount() > 0) {
                        RadioButton firstButton = (RadioButton) rgGameSelect.getChildAt(0);
                        rgGameSelect.check(firstButton.getId());
                        GameInfo firstGame = (GameInfo) firstButton.getTag();
                        if (firstGame != null) {
                            gameRepository.setSelectedGame(firstGame);
                        }
                    }

                    XRLog.i(TAG, "游戏选择UI更新完成: " + rgGameSelect.getChildCount() + " 个选项");

                } catch (Exception e) {
                    XRLog.e(TAG, "更新游戏选择UI失败", e);
                }
            }
        });

        XRLog.exit(TAG, "updateGameSelection");
    }

    // ========== 工具方法 ==========

    /**
     * 安全显示Toast，避免Context为null的崩溃
     */
    private void showToast(String message, int duration) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, duration).show();
        } else {
            Log.w(TAG, "Context为null，无法显示Toast: " + message);
        }
    }

    private void showToast(String message) {
        showToast(message, Toast.LENGTH_SHORT);
    }

    /**
     * 显示进度对话框
     */
    private void showProgressDialog(String message) {
        if (getContext() != null && progressDialog == null) {
            progressDialog = new ProgressDialog(getContext());
            progressDialog.setMessage(message);
            progressDialog.setCancelable(false);
            progressDialog.show();
        }
    }

    /**
     * 隐藏进度对话框
     */
    private void hideProgressDialog() {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
            progressDialog = null;
        }
    }

    // ========== 排序功能实现 ==========

    /**
     * 按设备编号排序并刷新列表
     */
    private void sortDevicesByIdAndRefresh() {
        try {
            Log.d(TAG, "开始按设备编号排序，设备数量: " + deviceList.size());

            // 按设备ID升序排序
            deviceList.sort((device1, device2) -> {
                return Integer.compare(device1.getId(), device2.getId());
            });

            // 刷新UI
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

            Log.d(TAG, "设备编号排序完成");
        } catch (Exception e) {
            Log.e(TAG, "按设备编号排序失败", e);
        }
    }

    /**
     * 按剩余电量排序并刷新列表
     */
    private void sortDevicesByBatteryAndRefresh() {
        try {
            Log.d(TAG, "开始按剩余电量排序，设备数量: " + deviceList.size());

            // 按电量降序排序（电量高的在前）
            deviceList.sort((device1, device2) -> {
                return Double.compare(device2.getBatteryLevel(), device1.getBatteryLevel());
            });

            // 刷新UI
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

            Log.d(TAG, "剩余电量排序完成");
        } catch (Exception e) {
            Log.e(TAG, "按剩余电量排序失败", e);
        }
    }

    /**
     * 选择排前12个设备并刷新列表
     */
    private void selectTop12DevicesAndRefresh() {
        try {
            Log.d(TAG, "开始选择排前12个设备，当前设备数量: " + deviceList.size());

            // 先清空所有选择
            List<Integer> allDeviceIds = new ArrayList<>();
            for (DeviceInfo device : deviceList) {
                allDeviceIds.add(device.getId());
            }
            deviceRepository.updateDeviceSelections(allDeviceIds, false);

            // 选择前12个设备（按当前列表顺序）
            List<Integer> top12DeviceIds = new ArrayList<>();
            int count = Math.min(12, deviceList.size());
            for (int i = 0; i < count; i++) {
                DeviceInfo device = deviceList.get(i);
                top12DeviceIds.add(device.getId());
            }

            if (!top12DeviceIds.isEmpty()) {
                deviceRepository.updateDeviceSelections(top12DeviceIds, true);
                Log.d(TAG, "选择排前12个设备完成，实际选择: " + count + " 个");
            } else {
                Log.w(TAG, "没有可选择的设备");
            }

            // 立即更新按钮文字
            updateClearSelectionButtonText(btnClearSelection);

            // 刷新UI
            if (adapter != null) {
                adapter.notifyDataSetChanged();
            }

        } catch (Exception e) {
            Log.e(TAG, "选择排前12个设备失败", e);
        }
    }
}