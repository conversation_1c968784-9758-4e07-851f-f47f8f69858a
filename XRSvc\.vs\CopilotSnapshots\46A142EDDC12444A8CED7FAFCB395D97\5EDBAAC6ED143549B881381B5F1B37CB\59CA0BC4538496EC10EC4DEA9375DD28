﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using XRSvc.CustomControl;
using XRSvc.DataPack;

namespace XRSvc.DataSource
{
    public class DeviceSource : DependencyObject
    {
        public static readonly DependencyProperty IDProperty = DependencyProperty.Register("ID", typeof(int), typeof(DeviceSource));
        public static readonly DependencyProperty SerialNumberProperty = DependencyProperty.Register("SerialNumber", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty IPProperty = DependencyProperty.Register("IP", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty NameProperty = DependencyProperty.Register("Name", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty TypeProperty = DependencyProperty.Register("Type", typeof(DeviceType), typeof(DeviceSource));
        public static readonly DependencyProperty IsEnabledProperty = DependencyProperty.Register("IsEnabled", typeof(bool), typeof(DeviceSource));
        public static readonly DependencyProperty IsOnlineProperty = DependencyProperty.Register("IsOnline", typeof(bool), typeof(DeviceSource));
        public static readonly DependencyProperty BatteryLevelProperty = DependencyProperty.Register("BatteryLevel", typeof(double), typeof(DeviceSource));
        public static readonly DependencyProperty DeviceTypeProperty = DependencyProperty.Register("DeviceType", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty ConnectionStatusProperty = DependencyProperty.Register("ConnectionStatus", typeof(string), typeof(DeviceSource));

        public int ID
        {
            get { return (int)GetValue(IDProperty); }
            set { SetValue(IDProperty, value); }
        }

        public string SerialNumber
        {
            get { return (string)GetValue(SerialNumberProperty); }
            set { SetValue(SerialNumberProperty, value); }
        }

        public string IP
        {
            get { return (string)GetValue(IPProperty); }
            set { SetValue(IPProperty, value); }
        }

        public string Name
        {
            get { return (string)GetValue(NameProperty); }
            set { SetValue(NameProperty, value); }
        }

        public DeviceType Type
        {
            get { return (DeviceType)GetValue(TypeProperty); }
            set { SetValue(TypeProperty, value); }
        }

        public bool IsEnabled
        {
            get { return (bool)GetValue(IsEnabledProperty); }
            set { SetValue(IsEnabledProperty, value); }
        }

        public bool IsOnline
        {
            get { return (bool)GetValue(IsOnlineProperty); }
            set { SetValue(IsOnlineProperty, value); }
        }

        public double BatteryLevel
        {
            get { return (double)GetValue(BatteryLevelProperty); }
            set { SetValue(BatteryLevelProperty, value); }
        }

        public string DeviceType
        {
            get { return (string)GetValue(DeviceTypeProperty); }
            set { SetValue(DeviceTypeProperty, value); }
        }

        public string ConnectionStatus
        {
            get { return (string)GetValue(ConnectionStatusProperty); }
            set { SetValue(ConnectionStatusProperty, value); }
        }

        public DeviceSource() { }

        public DeviceSource(DeviceInfo info)
        {
            ID = info.ID;
            SerialNumber = info.SerialNumber;
            IP = info.IP;
            Name = info.Name;
            Type = info.Type;
            IsEnabled = info.IsEnabled;
            IsOnline = info.IsOnline;
            BatteryLevel = info.BatteryLevel;
        }

        public DeviceInfo ToDeviceInfo()
        {
            return new DeviceInfo
            {
                ID = this.ID,
                SerialNumber = this.SerialNumber,
                IP = this.IP,
                Name = this.Name,
                Type = this.Type,
                IsEnabled = this.IsEnabled,
                IsOnline = this.IsOnline,
                BatteryLevel = this.BatteryLevel
            };
        }
    }
}