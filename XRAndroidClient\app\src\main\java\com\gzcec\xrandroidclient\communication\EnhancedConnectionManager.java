package com.gzcec.xrandroidclient.communication;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.gzcec.xrandroidclient.config.NetworkConfig;
import com.gzcec.xrandroidclient.utils.NetworkDiagnostics;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 增强的连接管理器
 * 解决Android先启动、PC后启动的连接问题
 * 处理端口切换和配置同步问题
 */
public class EnhancedConnectionManager {
    private static final String TAG = "EnhancedConnectionManager";
    
    // 单例实例
    private static volatile EnhancedConnectionManager instance;
    
    // 配置常量
    private static final long SERVER_DISCOVERY_INTERVAL = 30000; // 30秒
    private static final long HEARTBEAT_INTERVAL = 15000; // 15秒
    private static final long RECONNECT_DELAY = 5000; // 5秒
    private static final int[] COMMON_PORTS = {1883, 8081, 80, 81}; // 常见端口
    
    // 核心组件
    private Context context;
    private NetworkConfig networkConfig;
    private MqttCommunicationManager mqttManager;
    private NetworkConfigSyncManager configSyncManager;
    private Handler mainHandler;
    private ScheduledExecutorService scheduler;
    
    // 网络监听
    private ConnectivityManager connectivityManager;
    private NetworkCallback networkCallback;
    private BroadcastReceiver networkReceiver;
    
    // 定时任务
    private ScheduledFuture<?> discoveryTask;
    private ScheduledFuture<?> heartbeatTask;
    
    // 状态管理
    private boolean isEnabled = false;
    private boolean isDiscovering = false;
    private String lastKnownServer = null;
    private int lastKnownPort = 1883;
    
    // 监听器
    public interface ConnectionStateListener {
        void onServerDiscovered(String host, int port);
        void onServerLost();
        void onConnectionRestored();
        void onPortChanged(int oldPort, int newPort);
    }

    private ConnectionStateListener stateListener;
    private boolean isRegisteredAsMqttListener = false;
    
    private EnhancedConnectionManager() {
        mainHandler = new Handler(Looper.getMainLooper());
        scheduler = Executors.newScheduledThreadPool(2);
    }
    
    public static synchronized EnhancedConnectionManager getInstance() {
        if (instance == null) {
            instance = new EnhancedConnectionManager();
        }
        return instance;
    }
    
    /**
     * 初始化增强连接管理器
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        this.networkConfig = new NetworkConfig(this.context);
        this.mqttManager = MqttCommunicationManager.getInstance();
        this.configSyncManager = new NetworkConfigSyncManager(this.context, this.networkConfig);
        this.connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        setupNetworkMonitoring();
        Log.i(TAG, "增强连接管理器初始化完成");
    }
    
    /**
     * 启用增强连接管理
     */
    public void enable() {
        if (isEnabled) {
            Log.d(TAG, "增强连接管理已启用");
            return;
        }

        isEnabled = true;
        startServerDiscovery();
        startHeartbeat();
        registerNetworkReceiver();
        registerAsMqttListener();

        Log.i(TAG, "增强连接管理已启用");
    }
    
    /**
     * 禁用增强连接管理
     */
    public void disable() {
        if (!isEnabled) {
            return;
        }

        isEnabled = false;
        stopServerDiscovery();
        stopHeartbeat();
        unregisterNetworkReceiver();
        unregisterAsMqttListener();

        Log.i(TAG, "增强连接管理已禁用");
    }
    
    /**
     * 设置状态监听器
     */
    public void setStateListener(ConnectionStateListener listener) {
        this.stateListener = listener;
    }

    /**
     * 注册为MQTT连接监听器
     */
    private void registerAsMqttListener() {
        if (!isRegisteredAsMqttListener && mqttManager != null) {
            mqttManager.addConnectionListener(new MqttCommunicationManager.ConnectionListener() {
                @Override
                public void onConnected() {
                    Log.d(TAG, "增强连接管理器收到MQTT连接成功通知");

                    // 不再记录推荐配置，只使用用户手动设置的配置

                    // 通知状态监听器
                    if (stateListener != null) {
                        stateListener.onConnectionRestored();
                    }
                }

                @Override
                public void onDisconnected() {
                    Log.d(TAG, "增强连接管理器收到MQTT连接断开通知");
                    // 连接断开时，触发重新发现
                    if (isEnabled) {
                        Log.i(TAG, "连接断开，触发服务器重新发现");
                        forceRediscover();
                    }

                    // 通知状态监听器
                    if (stateListener != null) {
                        stateListener.onServerLost();
                    }
                }

                @Override
                public void onConnectionError(String error) {
                    Log.d(TAG, "增强连接管理器收到MQTT连接错误通知: " + error);

                    // 不再标记配置为无效，因为只使用用户配置

                    // 连接错误时，触发重新发现（但只会尝试用户配置）
                    if (isEnabled) {
                        Log.i(TAG, "连接错误，触发服务器重新发现");
                        forceRediscover();
                    }
                }
            });
            isRegisteredAsMqttListener = true;
            Log.d(TAG, "已注册为MQTT连接监听器");
        }
    }

    /**
     * 注销MQTT连接监听器
     */
    private void unregisterAsMqttListener() {
        // 注意：由于MqttCommunicationManager的监听器是通过List管理的，
        // 这里我们不能直接移除，但在disable时会停止所有活动
        isRegisteredAsMqttListener = false;
        Log.d(TAG, "已注销MQTT连接监听器");
    }
    
    /**
     * 强制重新发现服务器
     */
    public void forceRediscover() {
        Log.i(TAG, "强制重新发现服务器");
        scheduler.execute(this::performServerDiscovery);
    }
    
    /**
     * 设置网络监听
     */
    private void setupNetworkMonitoring() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            networkCallback = new NetworkCallback();
            NetworkRequest.Builder builder = new NetworkRequest.Builder()
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
            connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
        }
    }
    
    /**
     * 网络状态回调
     */
    private class NetworkCallback extends ConnectivityManager.NetworkCallback {
        @Override
        public void onAvailable(Network network) {
            Log.i(TAG, "网络连接可用，开始重新发现服务器");
            mainHandler.postDelayed(() -> {
                if (isEnabled) {
                    forceRediscover();
                }
            }, 2000); // 延迟2秒等待网络稳定
        }
        
        @Override
        public void onLost(Network network) {
            Log.w(TAG, "网络连接丢失");
            if (stateListener != null) {
                stateListener.onServerLost();
            }
        }
    }
    
    /**
     * 注册网络状态广播接收器
     */
    private void registerNetworkReceiver() {
        if (networkReceiver == null) {
            networkReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                        Log.d(TAG, "网络状态变化");
                        if (isEnabled && networkConfig.isNetworkConnected()) {
                            mainHandler.postDelayed(() -> forceRediscover(), 3000);
                        }
                    }
                }
            };
            
            IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
            context.registerReceiver(networkReceiver, filter);
        }
    }
    
    /**
     * 注销网络状态广播接收器
     */
    private void unregisterNetworkReceiver() {
        if (networkReceiver != null) {
            try {
                context.unregisterReceiver(networkReceiver);
            } catch (Exception e) {
                Log.w(TAG, "注销网络接收器失败", e);
            }
            networkReceiver = null;
        }
    }
    
    /**
     * 启动服务器发现
     */
    private void startServerDiscovery() {
        if (discoveryTask != null && !discoveryTask.isCancelled()) {
            return;
        }
        
        discoveryTask = scheduler.scheduleWithFixedDelay(
            this::performServerDiscovery,
            5000, // 初始延迟5秒
            SERVER_DISCOVERY_INTERVAL,
            TimeUnit.MILLISECONDS
        );
        
        Log.i(TAG, "服务器发现任务已启动");
    }
    
    /**
     * 停止服务器发现
     */
    private void stopServerDiscovery() {
        if (discoveryTask != null) {
            discoveryTask.cancel(false);
            discoveryTask = null;
        }
    }
    
    /**
     * 启动心跳检测
     */
    private void startHeartbeat() {
        if (heartbeatTask != null && !heartbeatTask.isCancelled()) {
            return;
        }
        
        heartbeatTask = scheduler.scheduleWithFixedDelay(
            this::performHeartbeat,
            HEARTBEAT_INTERVAL,
            HEARTBEAT_INTERVAL,
            TimeUnit.MILLISECONDS
        );
        
        Log.i(TAG, "心跳检测已启动");
    }
    
    /**
     * 停止心跳检测
     */
    private void stopHeartbeat() {
        if (heartbeatTask != null) {
            heartbeatTask.cancel(false);
            heartbeatTask = null;
        }
    }
    
    /**
     * 执行服务器发现
     */
    private void performServerDiscovery() {
        if (isDiscovering) {
            Log.d(TAG, "服务器发现正在进行中，跳过");
            return;
        }
        
        if (!networkConfig.isNetworkConnected()) {
            Log.w(TAG, "网络未连接，跳过服务器发现");
            return;
        }
        
        // 如果已连接且服务器正常，跳过发现
        if (mqttManager.isConnected() && isServerHealthy()) {
            Log.d(TAG, "MQTT已连接且服务器健康，跳过发现");
            return;
        }
        
        isDiscovering = true;
        Log.i(TAG, "开始服务器发现...");
        
        try {
            String discoveredServer = discoverMqttServer();
            if (discoveredServer != null) {
                String[] parts = discoveredServer.split(":");
                String host = parts[0];
                int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 1883;
                
                handleServerDiscovered(host, port);
            } else {
                Log.w(TAG, "未发现可用的MQTT服务器");
            }
        } catch (Exception e) {
            Log.e(TAG, "服务器发现失败", e);
        } finally {
            isDiscovering = false;
        }
    }
    
    /**
     * 发现MQTT服务器
     * 只使用用户手动设置的配置，不使用推荐配置或自动扫描
     */
    private String discoverMqttServer() {
        // 只尝试用户当前配置
        String currentHost = networkConfig.getMqttHost();
        int currentPort = networkConfig.getMqttPort();
        String currentConfig = currentHost + ":" + currentPort;

        Log.i(TAG, "尝试用户配置: " + currentConfig);

        if (testMqttConnection(currentHost, currentPort)) {
            Log.i(TAG, "使用用户配置连接成功: " + currentConfig);
            return currentConfig;
        } else {
            Log.w(TAG, "用户配置连接失败: " + currentConfig);
            return null;
        }
    }
    
    /**
     * 测试MQTT连接
     */
    private boolean testMqttConnection(String host, int port) {
        try {
            NetworkDiagnostics.DiagnosticResult result = 
                NetworkDiagnostics.testMqttConnection(host, port, 3000);
            return result.success;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 处理服务器发现
     */
    private void handleServerDiscovered(String host, int port) {
        String currentHost = networkConfig.getMqttHost();
        int currentPort = networkConfig.getMqttPort();

        boolean hostChanged = !host.equals(currentHost);
        boolean portChanged = port != currentPort;

        if (hostChanged || portChanged) {
            Log.i(TAG, String.format("发现新服务器: %s:%d (当前: %s:%d)",
                host, port, currentHost, currentPort));

            // 记录端口变化
            if (portChanged) {
                configSyncManager.recordPortChange(currentPort, port);
            }

            // 更新配置
            networkConfig.setMqttHost(host);
            networkConfig.setMqttPort(port);

            // 重新连接
            reconnectToNewServer(host, port);

            // 通知监听器
            if (stateListener != null) {
                mainHandler.post(() -> {
                    stateListener.onServerDiscovered(host, port);
                    if (portChanged) {
                        stateListener.onPortChanged(currentPort, port);
                    }
                });
            }

            // 更新已知服务器信息
            lastKnownServer = host;
            lastKnownPort = port;
        }
    }
    
    /**
     * 重新连接到新服务器
     */
    private void reconnectToNewServer(String host, int port) {
        Log.i(TAG, "重新连接到新服务器: " + host + ":" + port);
        
        // 断开当前连接
        mqttManager.disconnect();
        
        // 延迟重连
        mainHandler.postDelayed(() -> {
            try {
                // 重新初始化通信管理器
                mqttManager.initialize(context);
                mqttManager.connect();

                // 不在这里记录成功配置，等待onConnected回调中的延迟检查

                if (stateListener != null) {
                    stateListener.onConnectionRestored();
                }
            } catch (Exception e) {
                Log.e(TAG, "重新连接失败", e);
            }
        }, RECONNECT_DELAY);
    }
    
    /**
     * 执行心跳检测
     */
    private void performHeartbeat() {
        if (!mqttManager.isConnected()) {
            return;
        }
        
        if (!isServerHealthy()) {
            Log.w(TAG, "服务器健康检查失败，触发重新发现");
            forceRediscover();
        }
    }
    
    /**
     * 检查服务器健康状态
     */
    private boolean isServerHealthy() {
        String currentHost = networkConfig.getMqttHost();
        int currentPort = networkConfig.getMqttPort();
        return testMqttConnection(currentHost, currentPort);
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        disable();
        
        if (networkCallback != null && android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            } catch (Exception e) {
                Log.w(TAG, "注销网络回调失败", e);
            }
        }
        
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
        
        Log.i(TAG, "增强连接管理器资源清理完成");
    }
}
