using System;
using System.Globalization;
using System.Windows.Data;
using XRSvc.DataSource;

namespace XRSvc.Utils
{
    /// <summary>
    /// 游戏选中状态转换器，用于判断游戏是否被选中
    /// </summary>
    public class GameSelectionConverter : IValueConverter
    {
        /// <summary>
        /// 正向转换：根据选中的游戏和当前游戏ID判断是否选中
        /// </summary>
        /// <param name="value">选中的游戏对象</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">当前游戏ID</param>
        /// <param name="culture">文化信息</param>
        /// <returns>是否选中</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is GameSource selectedGame && parameter is int currentGameId)
            {
                return selectedGame?.ID == currentGameId;
            }
            return false;
        }

        /// <summary>
        /// 反向转换：不支持
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>转换结果</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException("GameSelectionConverter不支持反向转换");
        }
    }
} 