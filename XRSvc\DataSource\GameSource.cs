﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using XRSvc.DataPack; // Added namespace for GameType

namespace XRSvc.DataSource
{
    public class GameSource : DependencyObject
    {
        #region Fields DependencyProperty

        public static readonly DependencyProperty IDProperty = DependencyProperty.Register("ID", typeof(int), typeof(GameSource));
        public static readonly DependencyProperty NameProperty = DependencyProperty.Register("Name", typeof(string), typeof(GameSource));
        public static readonly DependencyProperty GameCategoryIndexProperty = DependencyProperty.Register("GameCategoryIndex", typeof(int), typeof(GameSource));
        public static readonly DependencyProperty PackageNameProperty = DependencyProperty.Register("PackageName", typeof(string), typeof(GameSource));
        public static readonly DependencyProperty VideoFilePathProperty = DependencyProperty.Register("VideoFilePath", typeof(string), typeof(GameSource));
        public static readonly DependencyProperty GameTypeProperty = DependencyProperty.Register("GameType", typeof(GameType), typeof(GameSource));
        public static readonly DependencyProperty VideoFormatProperty = DependencyProperty.Register("VideoFormat", typeof(VideoFormat), typeof(GameSource));
        public static readonly DependencyProperty DurationProperty = DependencyProperty.Register("Duration", typeof(int), typeof(GameSource));
        public static readonly DependencyProperty ShowOrderProperty = DependencyProperty.Register("ShowOrder", typeof(int), typeof(GameSource));
        public static readonly DependencyProperty IsShowProperty = DependencyProperty.Register("IsShow", typeof(bool), typeof(GameSource));
        public static readonly DependencyProperty IsPlayActionProperty = DependencyProperty.Register("IsPlayAction", typeof(bool), typeof(GameSource));
        public static readonly DependencyProperty ActionFilePathProperty = DependencyProperty.Register("ActionFilePath", typeof(string), typeof(GameSource));

        #endregion

        #region Properties

        // 游戏ID
        public int ID
        {
            get { return (int)GetValue(IDProperty); }
            set { SetValue(IDProperty, value); }
        }

        // 游戏名称
        public string Name
        {
            get { return (string)GetValue(NameProperty); }
            set { SetValue(NameProperty, value); }
        }

        // 游戏类别索引
        public int GameCategoryIndex
        {
            get { return (int)GetValue(GameCategoryIndexProperty); }
            set { SetValue(GameCategoryIndexProperty, value); }
        }

        // APK包名
        public string PackageName
        {
            get { return (string)GetValue(PackageNameProperty); }
            set { SetValue(PackageNameProperty, value); }
        }

        // 视频文件路径
        public string VideoFilePath
        {
            get { return (string)GetValue(VideoFilePathProperty); }
            set { SetValue(VideoFilePathProperty, value); }
        }

        // 游戏类型
        [JsonConverter(typeof(StringEnumConverter))]
        public GameType GameType
        {
            get { return (GameType)GetValue(GameTypeProperty); }
            set { SetValue(GameTypeProperty, value); }
        }

        // 视频格式
        [JsonConverter(typeof(StringEnumConverter))]
        public VideoFormat VideoFormat
        {
            get { return (VideoFormat)GetValue(VideoFormatProperty); }
            set { SetValue(VideoFormatProperty, value); }
        }

        // 视频时长
        public int Duration
        {
            get { return (int)GetValue(DurationProperty); }
            set { SetValue(DurationProperty, value); }
        }

        // 显示顺序
        public int ShowOrder
        {
            get { return (int)GetValue(ShowOrderProperty); }
            set { SetValue(ShowOrderProperty, value); }
        }

        // 是否显示
        public bool IsShow
        {
            get { return (bool)GetValue(IsShowProperty); }
            set { SetValue(IsShowProperty, value); }
        }

        // 是否播放动作
        public bool IsPlayAction
        {
            get { return (bool)GetValue(IsPlayActionProperty); }
            set { SetValue(IsPlayActionProperty, value); }
        }

        // 动作文件路径
        public string ActionFilePath
        {
            get { return (string)GetValue(ActionFilePathProperty); }
            set { SetValue(ActionFilePathProperty, value); }
        }

        #endregion
    }
}