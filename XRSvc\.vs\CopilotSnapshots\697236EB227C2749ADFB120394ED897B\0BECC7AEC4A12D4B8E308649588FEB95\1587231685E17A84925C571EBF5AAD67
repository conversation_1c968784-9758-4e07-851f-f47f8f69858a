﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Threading;
using XRSvc.DataPack;
using XRSvc.DataSource;

namespace XRSvc.ViewModels
{
    public class DeviceViewModel : INotifyPropertyChanged
    {
        #region Fields

        private ObservableCollection<DeviceSource> _devices;

        #endregion

        #region Properties

        public ObservableCollection<DeviceSource> Devices
        {
            get => _devices;
            set
            {
                _devices = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Events

        public event PropertyChangedEventHandler PropertyChanged;

        #endregion

        #region Constructor

        public DeviceViewModel()
        {
            _devices = new ObservableCollection<DeviceSource>();
        }

        #endregion

        #region Methods

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void ResetDevices()
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;

            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(ResetDevices);
                return;
            }

            foreach (var device in _devices)
            {
                device.SerialNumber = string.Empty;
                device.IP = string.Empty;
                device.Name = string.Empty;
                device.Type = DeviceType.SERVER;
                device.IsEnabled = false;
                device.IsOnline = false;
            }

            OnPropertyChanged(nameof(Devices));
        }

        public void UpdateDevices(DeviceInfo[] deviceInfos)
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;

            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(() => UpdateDevices(deviceInfos));
                return;
            }

            _devices.Clear();

            foreach (var info in deviceInfos)
            {
                _devices.Add(new DeviceSource(info));
            }

            OnPropertyChanged(nameof(Devices));
        }

        public DeviceInfo[] GetDeviceInfos()
        {
            var infos = new DeviceInfo[_devices.Count];
            for (int i = 0; i < _devices.Count; i++)
            {
                infos[i] = _devices[i].ToDeviceInfo();
            }
            return infos;
        }

        // 修改 LoadDefaultDevices 方法，使每个设备的随机值独立生成
        public void LoadDefaultDevices()
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;
            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(() => LoadDefaultDevices());
                return;
            }
            _devices.Clear();

            var random = new Random(); // 确保每次调用时随机值不同

            // Add devices for server1
            for (int i = 1; i <= 12; i++)
            {
                var device = new DeviceSource(new DeviceInfo
                {
                    ID = 8000 + i,
                    SerialNumber = $"SN-{i}",
                    Name = $"{i}号机",
                    Type = DeviceType.HMD,
                    BatteryLevel = random.Next(0, 100), // 独立随机电池电量
                    IsEnabled = random.Next(0, 2) == 1, // 独立随机启用状态
                    IsOnline = random.Next(0, 2) == 1  // 独立随机在线状态
                });
                _devices.Add(device);
            }

            OnPropertyChanged(nameof(Devices));
        }

        #endregion
    }
}