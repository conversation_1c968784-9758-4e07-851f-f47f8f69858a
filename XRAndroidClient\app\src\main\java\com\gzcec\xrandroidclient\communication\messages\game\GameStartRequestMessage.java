package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.communication.data.GameInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 游戏启动请求消息
 */
public class GameStartRequestMessage extends BaseMessage {
    private GameInfo selectedGame;
    private List<String> selectedDeviceIds;
    private Map<String, Object> gameParameters;

    public GameStartRequestMessage() {
        setType(MessageType.GAME_START_REQUEST);
        this.selectedDeviceIds = new ArrayList<>();
        this.gameParameters = new HashMap<>();
    }

    // Getter和Setter方法
    public GameInfo getSelectedGame() { return selectedGame; }
    public void setSelectedGame(GameInfo selectedGame) { this.selectedGame = selectedGame; }
    
    public List<String> getSelectedDeviceIds() { return selectedDeviceIds; }
    public void setSelectedDeviceIds(List<String> selectedDeviceIds) { this.selectedDeviceIds = selectedDeviceIds; }
    
    public Map<String, Object> getGameParameters() { return gameParameters; }
    public void setGameParameters(Map<String, Object> gameParameters) { this.gameParameters = gameParameters; }
}
