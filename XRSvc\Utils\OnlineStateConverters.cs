using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace XRSvc.Utils
{
    // 在线状态文本转换器
    public class OnlineStateTextConverter : IValueConverter
    {
        // 将布尔值转换为"在线"或"离线"文本
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isOnline)
                return isOnline ? "在线" : "离线";
            return "未知";
        }

        // 不支持的反向转换
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 在线状态颜色转换器
    public class OnlineStateColorConverter : IValueConverter
    {
        // 将布尔值转换为绿色/红色画刷
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isOnline)
                return isOnline ? Brushes.Green : Brushes.Red;
            return Brushes.Gray;
        }

        // 不支持的反向转换
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 