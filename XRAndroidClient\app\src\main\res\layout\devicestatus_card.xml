<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    card_view:cardCornerRadius="16dp"
    card_view:cardElevation="6dp"
    android:layout_margin="8dp"
    android:background="@android:color/transparent"
    android:foreground="@drawable/card_border">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#23262F"
        android:padding="16dp">

        <!-- 主信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/iv_logo"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/icon_vr"
                app:tint="@color/colorAccent"
                android:contentDescription="@string/device_status_icon"
                android:layout_marginEnd="12dp" />


            <TextView
                android:id="@+id/tv_device_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="设备ID"
                android:textColor="#FFFFFF"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/iv_status"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:contentDescription="@string/device_status_icon"
                android:layout_marginEnd="3dp" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="在线"
                android:textColor="#4CAF50"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginStart="8dp"/>


        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#333"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"/>

        <!-- 次要信息行（电量、IP） -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_battery"
                app:tint="#B0B3B8"
                android:layout_marginEnd="4dp"/>
            <TextView
                android:id="@+id/tv_battery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80%"
                android:textColor="#FFD600"
                android:textSize="15sp"
                android:layout_marginEnd="16dp"/>

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_ip"
                app:tint="#B0B3B8"
                android:layout_marginEnd="4dp"/>
            <TextView
                android:id="@+id/tv_server_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="***********"
                android:textColor="#B0B3B8"
                android:textSize="15sp"
                android:layout_marginEnd="16dp"/>
        </LinearLayout>

        <!-- SN码单独一行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="8dp">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_sn"
                app:tint="#B0B3B8"
                android:layout_marginEnd="4dp"/>
            <TextView
                android:id="@+id/tv_sn_code"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="SN1234567890ABCDEF"
                android:textColor="#B0B3B8"
                android:textSize="15sp"
                android:maxLines="1"
                android:ellipsize="end"/>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>