package com.gzcec.xrandroidclient.communication.data;

import java.util.Date;

/**
 * 设备停止结果数据类
 */
public class DeviceStopResult {
    private String deviceId;
    private String serialNumber;
    private boolean success;
    private String errorMessage;
    private Date stopTime;

    // Getter和Setter方法
    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    
    public String getSerialNumber() { return serialNumber; }
    public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public Date getStopTime() { return stopTime; }
    public void setStopTime(Date stopTime) { this.stopTime = stopTime; }
}
