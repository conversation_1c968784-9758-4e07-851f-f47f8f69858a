# XR系统 Android端开发文档

## 📱 项目概述

### 1.1 项目简介
XR系统Android端是一个基于MQTT协议的游戏管理客户端，用于控制和监控多台PICO头显设备。该应用作为移动端控制界面，通过MQTT与PC服务端进行实时通信，实现游戏启动、设备管理、状态监控等功能。

### 1.2 核心功能
- **🎮 游戏控制**: 远程启动/停止VR游戏，实时监控游戏进度
- **📱 设备管理**: 查看设备列表、在线状态、电池电量
- **🔧 硬件检测**: 检测设备硬件状态和连接情况
- **⚙️ 游戏设置**: 配置游戏参数和系统设置
- **📊 实时监控**: 通过MQTT实时接收设备状态更新

### 1.3 技术架构
- **开发语言**: Java 11
- **开发框架**: Android SDK (API 25+)
- **UI框架**: Material Design + Fragment架构
- **网络通信**: Eclipse Paho MQTT Client
- **数据序列化**: Gson
- **架构模式**: MVP/Fragment模式

## 🏗️ 项目结构

### 2.1 目录结构
```
XRAndroidClient/
├── app/
│   ├── src/main/
│   │   ├── java/com/gzcec/xrandroidclient/
│   │   │   ├── MainActivity.java                    # 主Activity
│   │   │   ├── BatteryView.java                     # 电池显示组件
│   │   │   ├── DeviceIdManager.java                 # 设备ID管理器
│   │   │   ├── communication/                       # 通信模块
│   │   │   │   ├── MqttGameClient.java             # MQTT客户端
│   │   │   │   ├── MqttTopics.java                 # MQTT主题定义
│   │   │   │   └── BaseMessage.java                # 消息基类
│   │   │   ├── device/                             # 设备模块
│   │   │   │   ├── DeviceInfo.java                 # 设备信息类
│   │   │   │   └── DeviceType.java                 # 设备类型枚举
│   │   │   ├── gameprogress/                       # 游戏进度模块
│   │   │   │   ├── GameProgressFragment.java       # 游戏进度Fragment
│   │   │   │   └── GameProgressDeviceAdapter.java  # 设备适配器
│   │   │   ├── devicestatus/                       # 设备状态模块
│   │   │   │   ├── DeviceStatusFragment.java       # 设备状态Fragment
│   │   │   │   └── StatusDeviceAdapter.java        # 状态适配器
│   │   │   ├── hardwarecheck/                      # 硬件检测模块
│   │   │   │   ├── HardwareCheckFragment.java      # 硬件检测Fragment
│   │   │   │   └── HardwareCheckDeviceAdapter.java # 检测适配器
│   │   │   └── GameSetting/                        # 游戏设置模块
│   │   │       └── GameSettingsFragment.java       # 设置Fragment
│   │   ├── res/                                    # 资源文件
│   │   │   ├── layout/                             # 布局文件
│   │   │   ├── drawable/                           # 图标资源
│   │   │   ├── values/                             # 值资源
│   │   │   └── menu/                               # 菜单资源
│   │   └── AndroidManifest.xml                     # 应用清单
│   ├── build.gradle                                # 应用构建配置
│   └── proguard-rules.pro                          # 混淆规则
├── gradle/                                         # Gradle配置
├── build.gradle                                    # 项目构建配置
└── settings.gradle                                 # 项目设置
```

### 2.2 核心模块说明

#### 📡 通信模块 (communication)
- **MqttGameClient**: MQTT客户端核心类，负责与PC服务端通信
- **MqttTopics**: MQTT主题定义，保持与PC端一致
- **BaseMessage**: 消息基类，定义通用消息格式

#### 📱 设备模块 (device)
- **DeviceInfo**: 设备信息数据类，包含设备状态、电量等信息
- **DeviceType**: 设备类型枚举，支持HMD、动感平台、风扇等

#### 🎮 功能模块
- **gameprogress**: 游戏进度管理和显示
- **devicestatus**: 设备状态监控和管理
- **hardwarecheck**: 硬件状态检测
- **GameSetting**: 游戏参数配置

## ⚙️ 开发环境配置

### 3.1 环境要求
- **Android Studio**: Arctic Fox 2020.3.1+
- **JDK**: Java 11+
- **Android SDK**: API 25+ (Android 7.1)
- **Gradle**: 8.11.1
- **目标设备**: Android 7.1+ (API 25+)

### 3.2 依赖配置

#### build.gradle (Project)
```gradle
plugins {
    alias(libs.plugins.android.application)
}
```

#### build.gradle (Module: app)
```gradle
android {
    namespace 'com.gzcec.xrandroidclient'
    compileSdk 36
    
    defaultConfig {
        applicationId "com.gzcec.xrandroidclient"
        minSdk 25
        targetSdk 36
        versionCode 1
        versionName "1.0"
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation libs.appcompat
    implementation libs.material
    // MQTT依赖需要添加
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'
    implementation 'com.google.code.gson:gson:2.8.9'
}
```

### 3.3 权限配置

#### AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 🔧 核心功能实现

### 4.1 MQTT通信实现

#### 连接配置
```java
// MQTT连接参数
private static final int CONNECTION_TIMEOUT = 10;
private static final int KEEP_ALIVE_INTERVAL = 30;
private static final int QOS_LEVEL = 1;

// 客户端ID生成
this.clientId = "AndroidClient_" + android.os.Build.MODEL + "_" + System.currentTimeMillis();
```

#### 主题订阅
```java
public static String[] getAndroidSubscriptionTopics() {
    return new String[]{
        GAME_START_RESPONSE,
        GAME_STOP_RESPONSE,
        GAME_PROGRESS_UPDATE,
        DEVICE_LIST_RESPONSE,
        DEVICE_STATUS_UPDATE,
        SYSTEM_ERROR,
        HEARTBEAT
    };
}
```

### 4.2 设备管理实现

#### 设备信息结构
```java
public class DeviceInfo {
    public int id;              // 设备ID
    public String sn;           // 设备序列号
    public String name;         // 设备名称
    public boolean isOnline;    // 在线状态
    public String battery;      // 电池电量
    public boolean isSelected;  // 选中状态
    public String gameStatus;   // 游戏状态
    public boolean inGame;      // 游戏中状态
    public String ip;           // IP地址
    public DeviceType type;     // 设备类型
}
```

#### 设备类型支持
```java
public enum DeviceType {
    HMD,                // VR头显
    MotionPlatform,     // 动感平台
    Fan,                // 风扇
    WaterSpray,         // 喷水设备
    DoorManual,         // 可推拉门
    ThermalSensor,      // 热感设备
    CommandDoor         // 指令控制门
}
```

### 4.3 UI架构实现

#### Fragment导航
```java
// 底部导航栏切换
bottomNav.setOnNavigationItemSelectedListener(item -> {
    Fragment selectedFragment = null;
    int id = item.getItemId();
    if (id == R.id.nav_game_progress) {
        selectedFragment = new GameProgressFragment();
    } else if (id == R.id.nav_device_status) {
        selectedFragment = new DeviceStatusFragment();
    }
    // ... 其他Fragment
    
    if (selectedFragment != null) {
        getSupportFragmentManager().beginTransaction()
            .replace(R.id.fragment_container, selectedFragment)
            .commit();
    }
    return true;
});
```

## 📋 开发规范

### 5.1 代码规范
- **命名规范**: 使用驼峰命名法，类名首字母大写
- **包结构**: 按功能模块划分包结构
- **注释规范**: 使用JavaDoc格式注释
- **异常处理**: 统一异常处理机制

### 5.2 UI设计规范
- **Material Design**: 遵循Google Material Design设计规范
- **响应式布局**: 支持不同屏幕尺寸适配
- **主题色彩**: 使用统一的主题色彩方案
- **图标规范**: 使用矢量图标，支持多分辨率

### 5.3 性能优化
- **内存管理**: 及时释放不用的资源
- **网络优化**: 合理使用MQTT QoS等级
- **UI优化**: 使用RecyclerView优化列表性能
- **电量优化**: 合理使用后台服务

## 🚀 构建和部署

### 6.1 构建配置
```bash
# 清理项目
./gradlew clean

# 构建Debug版本
./gradlew assembleDebug

# 构建Release版本
./gradlew assembleRelease
```

### 6.2 签名配置
```gradle
android {
    signingConfigs {
        release {
            storeFile file('keystore/release.keystore')
            storePassword 'your_store_password'
            keyAlias 'your_key_alias'
            keyPassword 'your_key_password'
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 6.3 部署方式
- **开发测试**: 通过ADB直接安装到设备
- **内部分发**: 使用Firebase App Distribution
- **正式发布**: 通过Google Play Store或企业应用商店

## 🔍 调试和测试

### 7.1 日志调试

#### 日志级别定义
```java
public class LogUtils {
    private static final String TAG_PREFIX = "XRSystem_";

    public static void d(String tag, String message) {
        Log.d(TAG_PREFIX + tag, message);
    }

    public static void i(String tag, String message) {
        Log.i(TAG_PREFIX + tag, message);
    }

    public static void w(String tag, String message) {
        Log.w(TAG_PREFIX + tag, message);
    }

    public static void e(String tag, String message, Throwable throwable) {
        Log.e(TAG_PREFIX + tag, message, throwable);
    }
}
```

#### 关键日志点
```java
// MQTT连接日志
LogUtils.i("MQTT", "正在连接到Broker: " + brokerUrl);
LogUtils.i("MQTT", "MQTT客户端已连接: " + clientId);
LogUtils.w("MQTT", "MQTT连接丢失: " + cause.getMessage());

// 消息收发日志
LogUtils.d("MQTT", "发送消息到主题: " + topic + ", 内容长度: " + payload.length());
LogUtils.d("MQTT", "收到消息: 主题=" + topic + ", 内容=" + payload);

// 设备状态日志
LogUtils.i("Device", "设备列表更新: 在线设备数=" + onlineCount + ", 总设备数=" + totalCount);
LogUtils.d("Device", "设备状态变更: " + deviceId + " -> " + newStatus);

// 游戏控制日志
LogUtils.i("Game", "请求启动游戏: " + gameInfo.gameName + ", 设备数=" + selectedDeviceIds.size());
LogUtils.i("Game", "游戏启动响应: 成功=" + response.success + ", 消息=" + response.message);
```

### 7.2 网络调试

#### MQTT Broker测试配置
```java
// 开发环境配置
public class MqttConfig {
    // 本地测试Broker
    public static final String DEV_BROKER_HOST = "127.0.0.1";
    public static final int DEV_BROKER_PORT = 1883;

    // 生产环境Broker
    public static final String PROD_BROKER_HOST = "*************";
    public static final int PROD_BROKER_PORT = 1883;

    public static String getBrokerUrl(boolean isProduction) {
        if (isProduction) {
            return "tcp://" + PROD_BROKER_HOST + ":" + PROD_BROKER_PORT;
        } else {
            return "tcp://" + DEV_BROKER_HOST + ":" + DEV_BROKER_PORT;
        }
    }
}
```

#### 网络状态监控
```java
public class NetworkMonitor {
    private ConnectivityManager connectivityManager;
    private NetworkCallback networkCallback;

    public void startMonitoring(Context context) {
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        networkCallback = new NetworkCallback() {
            @Override
            public void onAvailable(Network network) {
                LogUtils.i("Network", "网络连接可用");
                // 重新连接MQTT
                reconnectMqtt();
            }

            @Override
            public void onLost(Network network) {
                LogUtils.w("Network", "网络连接丢失");
                // 处理网络断开
                handleNetworkLoss();
            }
        };

        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
    }
}
```

#### 调试工具使用
```bash
# 使用ADB查看应用日志
adb logcat | grep "XRSystem"

# 过滤特定标签的日志
adb logcat -s "XRSystem_MQTT"

# 清除日志缓冲区
adb logcat -c

# 保存日志到文件
adb logcat | grep "XRSystem" > xr_debug.log
```

### 7.3 单元测试

#### 测试环境配置
```gradle
// app/build.gradle
dependencies {
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.robolectric:robolectric:4.8.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}
```

#### 设备信息测试
```java
@RunWith(JUnit4.class)
public class DeviceInfoTest {

    @Test
    public void testHMDDeviceCreation() {
        DeviceInfo device = new DeviceInfo(1, "PICO-001", "SN001",
            true, "80%", false, "Ready", false, "*************", DeviceType.HMD);

        assertEquals(1, device.getId());
        assertEquals("PICO-001", device.name);
        assertEquals("SN001", device.sn);
        assertTrue(device.isOnline);
        assertEquals("80%", device.battery);
        assertFalse(device.isSelected);
        assertEquals("Ready", device.gameStatus);
        assertFalse(device.inGame);
        assertEquals("*************", device.ip);
        assertEquals(DeviceType.HMD, device.type);
    }

    @Test
    public void testSpecialDeviceCreation() {
        DeviceInfo device = new DeviceInfo(9001, "动感平台", true, false,
            "************", DeviceType.MotionPlatform);

        assertEquals(9001, device.getId());
        assertEquals("动感平台", device.name);
        assertTrue(device.isOnline);
        assertFalse(device.inGame);
        assertEquals("************", device.ip);
        assertEquals(DeviceType.MotionPlatform, device.type);
        assertEquals("N/A", device.battery);
        assertEquals("未运行", device.gameStatus);
    }

    @Test
    public void testGenerateAllDevices() {
        List<DeviceInfo> devices = DeviceInfo.generateAllDevices();

        assertNotNull(devices);
        assertTrue(devices.size() > 0);

        // 验证设备类型分布
        long hmdCount = devices.stream().filter(d -> d.type == DeviceType.HMD).count();
        long specialCount = devices.stream().filter(d -> d.type != DeviceType.HMD).count();

        assertTrue(hmdCount > 0);
        assertTrue(specialCount > 0);
    }
}
```

#### MQTT客户端测试
```java
@RunWith(RobolectricTestRunner.class)
public class MqttGameClientTest {

    private MqttGameClient mqttClient;
    private Context mockContext;

    @Before
    public void setUp() {
        mockContext = RuntimeEnvironment.getApplication();
        mqttClient = new MqttGameClient(mockContext, "127.0.0.1", 1883);
    }

    @Test
    public void testClientIdGeneration() {
        String clientId = mqttClient.getClientId();
        assertNotNull(clientId);
        assertTrue(clientId.startsWith("AndroidClient_"));
        assertTrue(clientId.contains(Build.MODEL));
    }

    @Test
    public void testConnectionStatus() {
        assertFalse(mqttClient.isConnected());

        // 模拟连接成功
        mqttClient.connect();
        // 注意：实际测试需要Mock MQTT连接
    }

    @Test
    public void testTopicSubscription() {
        String[] topics = MqttTopics.getAndroidSubscriptionTopics();

        assertNotNull(topics);
        assertTrue(topics.length > 0);

        // 验证关键主题存在
        List<String> topicList = Arrays.asList(topics);
        assertTrue(topicList.contains(MqttTopics.GAME_START_RESPONSE));
        assertTrue(topicList.contains(MqttTopics.DEVICE_LIST_RESPONSE));
        assertTrue(topicList.contains(MqttTopics.DEVICE_STATUS_UPDATE));
    }
}
```

#### UI测试
```java
@RunWith(AndroidJUnit4.class)
public class MainActivityTest {

    @Rule
    public ActivityTestRule<MainActivity> activityRule =
        new ActivityTestRule<>(MainActivity.class);

    @Test
    public void testBottomNavigationSwitching() {
        // 点击游戏进度标签
        onView(withId(R.id.nav_game_progress)).perform(click());
        onView(withId(R.id.fragment_container)).check(matches(isDisplayed()));

        // 点击设备状态标签
        onView(withId(R.id.nav_device_status)).perform(click());
        onView(withId(R.id.fragment_container)).check(matches(isDisplayed()));

        // 点击硬件检测标签
        onView(withId(R.id.nav_hardware_check)).perform(click());
        onView(withId(R.id.fragment_container)).check(matches(isDisplayed()));

        // 点击游戏设置标签
        onView(withId(R.id.nav_game_settings)).perform(click());
        onView(withId(R.id.fragment_container)).check(matches(isDisplayed()));
    }

    @Test
    public void testDeviceListDisplay() {
        // 切换到设备状态页面
        onView(withId(R.id.nav_device_status)).perform(click());

        // 验证RecyclerView存在
        onView(withId(R.id.recycler_view_devices)).check(matches(isDisplayed()));

        // 验证至少有一个设备项
        onView(withId(R.id.recycler_view_devices))
            .check(matches(hasMinimumChildCount(1)));
    }
}
```

### 7.4 集成测试

#### 端到端测试流程
```java
@RunWith(AndroidJUnit4.class)
public class E2ETest {

    private MqttGameClient mqttClient;
    private CountDownLatch connectionLatch;
    private CountDownLatch responseLatch;

    @Before
    public void setUp() {
        connectionLatch = new CountDownLatch(1);
        responseLatch = new CountDownLatch(1);

        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        mqttClient = new MqttGameClient(context, "*************", 1883);

        mqttClient.setConnectionListener(new MqttGameClient.ConnectionListener() {
            @Override
            public void onConnected() {
                connectionLatch.countDown();
            }

            @Override
            public void onDisconnected(String reason) {}

            @Override
            public void onError(String error) {}
        });
    }

    @Test
    public void testCompleteGameFlow() throws InterruptedException {
        // 1. 连接MQTT
        mqttClient.connect();
        assertTrue(connectionLatch.await(10, TimeUnit.SECONDS));

        // 2. 请求设备列表
        mqttClient.requestDeviceList(true, response -> {
            assertNotNull(response);
            responseLatch.countDown();
        });
        assertTrue(responseLatch.await(5, TimeUnit.SECONDS));

        // 3. 启动游戏
        GameInfo gameInfo = new GameInfo("test_game", "测试游戏");
        List<String> deviceIds = Arrays.asList("device_001", "device_002");

        CountDownLatch gameLatch = new CountDownLatch(1);
        mqttClient.requestStartGame(gameInfo, deviceIds, response -> {
            assertNotNull(response);
            gameLatch.countDown();
        });
        assertTrue(gameLatch.await(10, TimeUnit.SECONDS));

        // 4. 断开连接
        mqttClient.disconnect();
    }
}
```

### 7.5 性能测试

#### 内存泄漏检测
```java
@Test
public void testMemoryLeak() {
    // 使用LeakCanary进行内存泄漏检测
    for (int i = 0; i < 100; i++) {
        DeviceInfo device = new DeviceInfo(i, "Device" + i, "SN" + i,
            true, "80%", false, "Ready", false, "192.168.1." + (100 + i), DeviceType.HMD);
        // 模拟设备创建和销毁
        device = null;
    }

    // 强制垃圾回收
    System.gc();

    // 验证内存使用情况
    Runtime runtime = Runtime.getRuntime();
    long usedMemory = runtime.totalMemory() - runtime.freeMemory();
    assertTrue("内存使用过高", usedMemory < 50 * 1024 * 1024); // 50MB
}
```

#### 网络性能测试
```java
@Test
public void testMqttPerformance() {
    long startTime = System.currentTimeMillis();

    // 发送100条消息
    for (int i = 0; i < 100; i++) {
        mqttClient.sendHeartbeat();
    }

    long endTime = System.currentTimeMillis();
    long duration = endTime - startTime;

    assertTrue("消息发送耗时过长", duration < 5000); // 5秒内完成
}
```

## 📚 API文档

### 8.1 MQTT主题结构

#### 主题层次结构
```
xr_system/
├── game_control/
│   ├── start_request          # 游戏启动请求
│   ├── start_response         # 游戏启动响应
│   ├── stop_request           # 游戏停止请求
│   ├── stop_response          # 游戏停止响应
│   ├── progress_update        # 游戏进度更新
│   └── server_status          # 游戏服务器状态
├── device_management/
│   ├── list_request           # 设备列表请求
│   ├── list_response          # 设备列表响应
│   ├── status_update          # 设备状态更新
│   ├── selection_changed      # 设备选择变更
│   └── battery_status         # 设备电池状态
└── system_status/
    ├── heartbeat              # 心跳消息
    ├── error                  # 系统错误
    ├── connection             # 连接状态
    └── log                    # 系统日志
```

### 8.2 MQTT消息格式

#### 游戏启动请求 (game_control/start_request)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440000",
    "senderId": "AndroidClient_PICO_1642567890123",
    "timestamp": "2024-07-24T10:30:00.000Z",
    "selectedGame": {
        "gameId": "vr_adventure_001",
        "gameName": "VR冒险世界",
        "gameVersion": "1.2.0",
        "gameType": "Adventure",
        "maxPlayers": 8
    },
    "selectedDeviceIds": [
        "device_001", "device_002", "device_003"
    ],
    "gameSettings": {
        "difficulty": "Normal",
        "duration": 1800,
        "enableSpecialEffects": true
    }
}
```

#### 游戏启动响应 (game_control/start_response)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440001",
    "requestId": "550e8400-e29b-41d4-a716-446655440000",
    "senderId": "PCServer_Main",
    "timestamp": "2024-07-24T10:30:05.000Z",
    "success": true,
    "message": "游戏启动成功",
    "gameSessionId": "session_20240724_103000",
    "startedDevices": [
        {
            "deviceId": "device_001",
            "status": "Started",
            "gamePort": 7777
        },
        {
            "deviceId": "device_002",
            "status": "Started",
            "gamePort": 7778
        }
    ],
    "failedDevices": [
        {
            "deviceId": "device_003",
            "status": "Failed",
            "errorMessage": "设备离线"
        }
    ]
}
```

#### 设备列表请求 (device_management/list_request)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440002",
    "senderId": "AndroidClient_PICO_1642567890123",
    "timestamp": "2024-07-24T10:25:00.000Z",
    "onlineOnly": true,
    "deviceTypes": ["HMD", "MotionPlatform", "Fan"],
    "includeDetails": true
}
```

#### 设备列表响应 (device_management/list_response)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440003",
    "requestId": "550e8400-e29b-41d4-a716-446655440002",
    "senderId": "PCServer_Main",
    "timestamp": "2024-07-24T10:25:02.000Z",
    "totalCount": 25,
    "onlineCount": 18,
    "devices": [
        {
            "deviceId": "device_001",
            "deviceName": "PICO-001",
            "serialNumber": "PX001234567",
            "deviceType": "HMD",
            "isOnline": true,
            "ipAddress": "************1",
            "batteryLevel": 85,
            "gameStatus": "Idle",
            "inGame": false,
            "lastSeen": "2024-07-24T10:24:58.000Z",
            "firmwareVersion": "4.2.1",
            "hardwareInfo": {
                "cpuUsage": 15.2,
                "memoryUsage": 45.8,
                "temperature": 42.5
            }
        }
    ]
}
```

#### 设备状态更新 (device_management/status_update)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440004",
    "senderId": "PCServer_Main",
    "timestamp": "2024-07-24T10:35:00.000Z",
    "deviceId": "device_001",
    "updates": {
        "batteryLevel": 82,
        "gameStatus": "Playing",
        "inGame": true,
        "currentGameSession": "session_20240724_103000",
        "gameProgress": 35.5,
        "playerCount": 1,
        "hardwareInfo": {
            "cpuUsage": 68.3,
            "memoryUsage": 78.2,
            "temperature": 55.8
        }
    }
}
```

#### 心跳消息 (system_status/heartbeat)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440005",
    "senderId": "AndroidClient_PICO_1642567890123",
    "timestamp": "2024-07-24T10:40:00.000Z",
    "status": "OK",
    "clientInfo": {
        "appVersion": "1.0.0",
        "androidVersion": "11",
        "deviceModel": "PICO 4",
        "networkType": "WiFi",
        "signalStrength": -45
    }
}
```

### 8.3 主要接口说明

#### MqttGameClient核心方法

##### 连接管理
```java
/**
 * 连接到MQTT Broker
 * @throws MqttException 连接异常
 */
public void connect() throws MqttException

/**
 * 断开MQTT连接
 */
public void disconnect()

/**
 * 检查连接状态
 * @return 是否已连接
 */
public boolean isConnected()
```

##### 游戏控制
```java
/**
 * 请求启动游戏
 * @param gameInfo 游戏信息
 * @param selectedDeviceIds 选中的设备ID列表
 * @param callback 响应回调
 */
public void requestStartGame(GameInfo gameInfo, List<String> selectedDeviceIds, ResponseCallback callback)

/**
 * 请求停止游戏
 * @param gameSessionId 游戏会话ID
 * @param callback 响应回调
 */
public void requestStopGame(String gameSessionId, ResponseCallback callback)
```

##### 设备管理
```java
/**
 * 请求设备列表
 * @param onlineOnly 是否只获取在线设备
 * @param callback 响应回调
 */
public void requestDeviceList(boolean onlineOnly, ResponseCallback callback)

/**
 * 发送设备选择变更
 * @param selectedDeviceIds 选中的设备ID列表
 */
public void sendDeviceSelectionChanged(List<String> selectedDeviceIds)
```

##### 系统功能
```java
/**
 * 发送心跳消息
 */
public void sendHeartbeat()

/**
 * 发布连接状态
 * @param connected 连接状态
 */
private void publishConnectionStatus(boolean connected)
```

#### DeviceInfo数据模型

##### 构造方法
```java
/**
 * HMD设备构造函数
 * @param id 设备ID
 * @param name 设备名称
 * @param sn 设备序列号
 * @param isOnline 在线状态
 * @param battery 电池电量
 * @param isSelected 是否被选中
 * @param gameStatus 游戏状态
 * @param inGame 是否在游戏中
 * @param ip 设备IP地址
 * @param type 设备类型
 */
public DeviceInfo(int id, String name, String sn, boolean isOnline,
                 String battery, boolean isSelected, String gameStatus,
                 boolean inGame, String ip, DeviceType type)

/**
 * 特殊设备构造函数（动感平台、风扇等）
 * @param id 设备ID
 * @param name 设备名称
 * @param isOnline 在线状态
 * @param inGame 是否运行中
 * @param ip 设备IP地址
 * @param type 设备类型
 */
public DeviceInfo(int id, String name, boolean isOnline,
                 boolean inGame, String ip, DeviceType type)
```

##### 静态方法
```java
/**
 * 生成所有设备的测试数据
 * @return 设备信息列表
 */
public static List<DeviceInfo> generateAllDevices()
```

#### Fragment生命周期管理

##### GameProgressFragment
```java
/**
 * 游戏进度Fragment主要方法
 */
public class GameProgressFragment extends Fragment {
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState)

    @Override
    public void onResume()

    @Override
    public void onPause()

    /**
     * 更新设备游戏进度
     * @param devices 设备列表
     */
    public void updateGameProgress(List<DeviceInfo> devices)

    /**
     * 启动选中设备的游戏
     */
    private void startSelectedGames()

    /**
     * 停止所有游戏
     */
    private void stopAllGames()
}
```

### 8.4 回调接口定义

#### MQTT事件监听器
```java
/**
 * 游戏控制监听器
 */
public interface GameControlListener {
    /**
     * 游戏启动响应
     * @param response 启动响应消息
     */
    void onGameStartResponse(GameStartResponseMessage response);

    /**
     * 游戏停止响应
     * @param response 停止响应消息
     */
    void onGameStopResponse(GameStopResponseMessage response);

    /**
     * 游戏进度更新
     * @param update 进度更新消息
     */
    void onGameProgressUpdate(GameProgressUpdateMessage update);
}

/**
 * 设备状态监听器
 */
public interface DeviceStatusListener {
    /**
     * 接收到设备列表
     * @param response 设备列表响应
     */
    void onDeviceListReceived(DeviceListResponseMessage response);

    /**
     * 设备状态更新
     * @param update 状态更新消息
     */
    void onDeviceStatusUpdate(DeviceStatusUpdateMessage update);
}

/**
 * 连接状态监听器
 */
public interface ConnectionListener {
    /**
     * 连接成功
     */
    void onConnected();

    /**
     * 连接断开
     * @param reason 断开原因
     */
    void onDisconnected(String reason);

    /**
     * 连接错误
     * @param error 错误信息
     */
    void onError(String error);
}

/**
 * 响应回调接口
 */
public interface ResponseCallback {
    /**
     * 接收到响应
     * @param response 响应内容
     */
    void onResponse(String response);

    /**
     * 请求超时
     */
    default void onTimeout() {}

    /**
     * 请求失败
     * @param error 错误信息
     */
    default void onError(String error) {}
}
```

## 🔧 故障排除

### 9.1 常见问题及解决方案

#### 🌐 网络连接问题

**问题1: MQTT连接失败**
```
错误信息: Connection refused: Not authorized
```
**解决方案:**
```java
// 检查Broker地址和端口配置
private void validateBrokerConfig() {
    String brokerUrl = "tcp://*************:1883";

    // 测试网络连通性
    new Thread(() -> {
        try {
            Socket socket = new Socket();
            socket.connect(new InetSocketAddress("*************", 1883), 5000);
            socket.close();
            LogUtils.i("Network", "Broker网络连通正常");
        } catch (IOException e) {
            LogUtils.e("Network", "无法连接到Broker", e);
        }
    }).start();
}

// 添加重连机制
private void setupAutoReconnect() {
    MqttConnectOptions options = new MqttConnectOptions();
    options.setAutomaticReconnect(true);
    options.setMaxReconnectDelay(30000); // 最大重连间隔30秒
    options.setCleanSession(false); // 保持会话状态
}
```

**问题2: 消息发送失败**
```
错误信息: Client is not connected
```
**解决方案:**
```java
private void publishMessageSafely(String topic, String payload) {
    if (!mqttClient.isConnected()) {
        LogUtils.w("MQTT", "客户端未连接，尝试重连");
        reconnectAndRetry(topic, payload);
        return;
    }

    try {
        MqttMessage message = new MqttMessage(payload.getBytes());
        message.setQos(1);
        message.setRetained(false);
        mqttClient.publish(topic, message);
    } catch (MqttException e) {
        LogUtils.e("MQTT", "消息发送失败", e);
        handlePublishError(topic, payload, e);
    }
}

private void reconnectAndRetry(String topic, String payload) {
    new Thread(() -> {
        try {
            connect();
            Thread.sleep(1000); // 等待连接稳定
            publishMessageSafely(topic, payload);
        } catch (Exception e) {
            LogUtils.e("MQTT", "重连后发送失败", e);
        }
    }).start();
}
```

#### 📱 设备管理问题

**问题3: 设备列表为空或不更新**
```java
// 检查设备列表请求
private void debugDeviceListRequest() {
    LogUtils.d("Device", "发送设备列表请求");

    // 设置超时处理
    Handler timeoutHandler = new Handler(Looper.getMainLooper());
    Runnable timeoutRunnable = () -> {
        LogUtils.w("Device", "设备列表请求超时");
        showErrorMessage("获取设备列表超时，请检查网络连接");
    };

    mqttClient.requestDeviceList(true, response -> {
        timeoutHandler.removeCallbacks(timeoutRunnable);
        if (response != null && !response.isEmpty()) {
            LogUtils.i("Device", "收到设备列表响应: " + response.length() + " 字符");
            parseDeviceListResponse(response);
        } else {
            LogUtils.w("Device", "设备列表响应为空");
        }
    });

    // 10秒超时
    timeoutHandler.postDelayed(timeoutRunnable, 10000);
}
```

**问题4: 设备状态不同步**
```java
// 实现设备状态缓存和对比
private Map<String, DeviceInfo> deviceCache = new ConcurrentHashMap<>();

private void updateDeviceStatus(DeviceStatusUpdateMessage update) {
    String deviceId = update.getDeviceId();
    DeviceInfo cachedDevice = deviceCache.get(deviceId);

    if (cachedDevice == null) {
        LogUtils.w("Device", "收到未知设备的状态更新: " + deviceId);
        requestDeviceList(false, null); // 重新获取设备列表
        return;
    }

    // 检查状态变化
    boolean statusChanged = false;
    if (cachedDevice.isOnline != update.isOnline()) {
        LogUtils.i("Device", deviceId + " 在线状态变更: " +
            cachedDevice.isOnline + " -> " + update.isOnline());
        cachedDevice.isOnline = update.isOnline();
        statusChanged = true;
    }

    if (!cachedDevice.battery.equals(update.getBatteryLevel())) {
        LogUtils.d("Device", deviceId + " 电量变更: " +
            cachedDevice.battery + " -> " + update.getBatteryLevel());
        cachedDevice.battery = update.getBatteryLevel();
        statusChanged = true;
    }

    if (statusChanged) {
        notifyDeviceStatusChanged(cachedDevice);
    }
}
```

#### 🎮 游戏控制问题

**问题5: 游戏启动失败**
```java
private void handleGameStartFailure(GameStartResponseMessage response) {
    LogUtils.e("Game", "游戏启动失败: " + response.getMessage());

    // 分析失败原因
    if (response.getFailedDevices() != null) {
        for (FailedDevice failed : response.getFailedDevices()) {
            String reason = failed.getErrorMessage();
            LogUtils.e("Game", "设备 " + failed.getDeviceId() + " 启动失败: " + reason);

            // 根据失败原因提供解决建议
            String suggestion = getFailureSuggestion(reason);
            showDeviceError(failed.getDeviceId(), reason, suggestion);
        }
    }

    // 回滚已启动的设备
    if (response.getStartedDevices() != null && !response.getStartedDevices().isEmpty()) {
        LogUtils.i("Game", "回滚已启动的设备");
        rollbackStartedDevices(response.getStartedDevices());
    }
}

private String getFailureSuggestion(String errorMessage) {
    if (errorMessage.contains("设备离线")) {
        return "请检查设备网络连接和电源状态";
    } else if (errorMessage.contains("电量不足")) {
        return "请为设备充电后重试";
    } else if (errorMessage.contains("游戏已在运行")) {
        return "请先停止当前游戏再启动新游戏";
    } else {
        return "请联系技术支持";
    }
}
```

#### 🖥️ UI性能问题

**问题6: RecyclerView卡顿**
```java
// 优化RecyclerView性能
public class OptimizedDeviceAdapter extends RecyclerView.Adapter<DeviceViewHolder> {

    private List<DeviceInfo> devices = new ArrayList<>();
    private DiffUtil.ItemCallback<DeviceInfo> diffCallback = new DiffUtil.ItemCallback<DeviceInfo>() {
        @Override
        public boolean areItemsTheSame(@NonNull DeviceInfo oldItem, @NonNull DeviceInfo newItem) {
            return oldItem.id == newItem.id;
        }

        @Override
        public boolean areContentsTheSame(@NonNull DeviceInfo oldItem, @NonNull DeviceInfo newItem) {
            return oldItem.isOnline == newItem.isOnline &&
                   oldItem.battery.equals(newItem.battery) &&
                   oldItem.gameStatus.equals(newItem.gameStatus) &&
                   oldItem.inGame == newItem.inGame;
        }
    };

    private AsyncListDiffer<DeviceInfo> differ = new AsyncListDiffer<>(this, diffCallback);

    public void updateDevices(List<DeviceInfo> newDevices) {
        // 使用DiffUtil异步计算差异，避免UI卡顿
        differ.submitList(new ArrayList<>(newDevices));
    }

    @Override
    public int getItemCount() {
        return differ.getCurrentList().size();
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        DeviceInfo device = differ.getCurrentList().get(position);
        holder.bind(device);
    }
}
```

### 9.2 性能优化最佳实践

#### 🚀 内存优化
```java
// 避免内存泄漏的Fragment实现
public class BaseFragment extends Fragment {

    private CompositeDisposable disposables = new CompositeDisposable();
    private Handler handler;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        handler = new Handler(Looper.getMainLooper());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // 清理异步任务
        if (disposables != null) {
            disposables.clear();
        }

        // 清理Handler回调
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }

        // 清理MQTT监听器
        if (mqttClient != null) {
            mqttClient.setGameControlListener(null);
            mqttClient.setDeviceStatusListener(null);
        }
    }

    protected void runOnUiThread(Runnable action) {
        if (isAdded() && !isDetached()) {
            handler.post(action);
        }
    }
}
```

#### ⚡ 网络优化
```java
// MQTT连接池管理
public class MqttConnectionManager {
    private static final int MAX_RETRY_COUNT = 3;
    private static final long RETRY_DELAY_MS = 2000;

    private MqttClient mqttClient;
    private int retryCount = 0;
    private boolean isReconnecting = false;

    public void connectWithRetry() {
        if (isReconnecting) {
            return;
        }

        isReconnecting = true;
        new Thread(() -> {
            while (retryCount < MAX_RETRY_COUNT && !mqttClient.isConnected()) {
                try {
                    LogUtils.i("MQTT", "尝试连接 (第" + (retryCount + 1) + "次)");
                    mqttClient.connect();

                    if (mqttClient.isConnected()) {
                        LogUtils.i("MQTT", "连接成功");
                        retryCount = 0;
                        break;
                    }
                } catch (Exception e) {
                    LogUtils.w("MQTT", "连接失败: " + e.getMessage());
                    retryCount++;

                    if (retryCount < MAX_RETRY_COUNT) {
                        try {
                            Thread.sleep(RETRY_DELAY_MS * retryCount); // 指数退避
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }

            isReconnecting = false;

            if (!mqttClient.isConnected()) {
                LogUtils.e("MQTT", "连接失败，已达到最大重试次数");
                notifyConnectionFailed();
            }
        }).start();
    }
}
```

### 9.3 调试工具和技巧

#### 🔍 日志分析工具
```bash
# 实时查看应用日志
adb logcat | grep "XRSystem"

# 过滤MQTT相关日志
adb logcat | grep "XRSystem_MQTT"

# 查看崩溃日志
adb logcat | grep -E "(FATAL|AndroidRuntime)"

# 保存日志到文件并分析
adb logcat -v time | grep "XRSystem" > debug_$(date +%Y%m%d_%H%M%S).log
```

#### 📊 性能监控
```java
// 性能监控工具类
public class PerformanceMonitor {
    private static final String TAG = "Performance";
    private Map<String, Long> startTimes = new HashMap<>();

    public void startTiming(String operation) {
        startTimes.put(operation, System.currentTimeMillis());
        LogUtils.d(TAG, "开始计时: " + operation);
    }

    public void endTiming(String operation) {
        Long startTime = startTimes.remove(operation);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            LogUtils.i(TAG, operation + " 耗时: " + duration + "ms");

            // 性能警告
            if (duration > 1000) {
                LogUtils.w(TAG, operation + " 执行时间过长: " + duration + "ms");
            }
        }
    }

    public void logMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        LogUtils.i(TAG, String.format("内存使用: %d MB / %d MB (%.1f%%)",
            usedMemory / 1024 / 1024,
            totalMemory / 1024 / 1024,
            (double) usedMemory / totalMemory * 100));
    }
}
```

#### 🐛 崩溃处理
```java
// 全局异常处理器
public class CrashHandler implements Thread.UncaughtExceptionHandler {
    private static final String TAG = "CrashHandler";
    private Thread.UncaughtExceptionHandler defaultHandler;
    private Context context;

    public CrashHandler(Context context) {
        this.context = context;
        this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        LogUtils.e(TAG, "应用崩溃", ex);

        // 保存崩溃日志
        saveCrashLog(ex);

        // 发送崩溃报告
        sendCrashReport(ex);

        // 调用默认处理器
        if (defaultHandler != null) {
            defaultHandler.uncaughtException(thread, ex);
        }
    }

    private void saveCrashLog(Throwable ex) {
        try {
            String fileName = "crash_" + System.currentTimeMillis() + ".log";
            File crashFile = new File(context.getExternalFilesDir("crashes"), fileName);

            PrintWriter writer = new PrintWriter(new FileWriter(crashFile));
            ex.printStackTrace(writer);
            writer.close();

            LogUtils.i(TAG, "崩溃日志已保存: " + crashFile.getAbsolutePath());
        } catch (IOException e) {
            LogUtils.e(TAG, "保存崩溃日志失败", e);
        }
    }
}

## 📈 后续开发计划

### 10.1 短期目标 (1-2个月)

#### 🔧 功能完善
- [ ] **MQTT安全认证**: 实现用户名/密码认证和SSL/TLS加密
```java
// SSL连接配置
MqttConnectOptions options = new MqttConnectOptions();
options.setSocketFactory(createSSLSocketFactory());
options.setUserName("xr_android_client");
options.setPassword("secure_password".toCharArray());
```

- [ ] **离线模式支持**: 实现本地数据缓存和离线操作队列
```java
public class OfflineManager {
    private SQLiteDatabase database;
    private Queue<PendingOperation> operationQueue;

    public void cacheDeviceList(List<DeviceInfo> devices) {
        // 缓存设备列表到本地数据库
    }

    public void queueOperation(PendingOperation operation) {
        // 网络断开时将操作加入队列
    }
}
```

- [ ] **设备分组管理**: 支持设备按区域、类型、状态分组
- [ ] **批量操作**: 支持批量启动/停止游戏，批量设备配置

#### 🎨 UI/UX优化
- [ ] **Material Design 3**: 升级到最新的Material Design规范
- [ ] **深色模式**: 完整支持系统深色模式
- [ ] **平板适配**: 优化平板设备的布局和交互
- [ ] **无障碍支持**: 添加TalkBack和其他无障碍功能

### 10.2 中期目标 (3-6个月)

#### 📊 数据分析功能
- [ ] **游戏统计报表**: 游戏时长、设备使用率、故障统计
```java
public class GameAnalytics {
    public GameStatistics getGameStatistics(String gameId, DateRange dateRange) {
        // 返回游戏统计数据
    }

    public DeviceUsageReport getDeviceUsageReport(DateRange dateRange) {
        // 返回设备使用报告
    }
}
```

- [ ] **实时监控面板**: 设备状态实时图表和告警
- [ ] **性能监控**: CPU、内存、网络使用情况监控
- [ ] **用户行为分析**: 操作习惯和使用模式分析

#### 🌐 多语言支持
- [ ] **国际化框架**: 支持中文、英文、日文、韩文
```xml
<!-- strings.xml -->
<string name="app_name">XR System</string>
<string name="device_online">设备在线</string>
<string name="device_offline">设备离线</string>

<!-- strings-en.xml -->
<string name="app_name">XR System</string>
<string name="device_online">Device Online</string>
<string name="device_offline">Device Offline</string>
```

- [ ] **本地化适配**: 时间格式、数字格式、文化习惯适配

#### 🔐 安全增强
- [ ] **设备认证**: 设备指纹识别和白名单管理
- [ ] **操作审计**: 记录所有关键操作和变更
- [ ] **权限管理**: 基于角色的访问控制(RBAC)

### 10.3 长期目标 (6-12个月)

#### 🤖 智能化功能
- [ ] **AI设备调度**: 基于历史数据智能分配设备
- [ ] **故障预测**: 通过机器学习预测设备故障
- [ ] **自动化运维**: 自动重启、自动更新、自动备份

#### 🔄 系统集成
- [ ] **第三方系统集成**: 与现有管理系统对接
- [ ] **API开放平台**: 提供RESTful API供第三方调用
- [ ] **Webhook支持**: 支持事件推送到外部系统

#### 📱 跨平台扩展
- [ ] **iOS版本**: 开发iOS客户端
- [ ] **Web管理端**: 基于React的Web管理界面
- [ ] **桌面客户端**: Electron桌面应用

### 10.4 技术债务清理

#### 🏗️ 架构重构
- [ ] **模块化重构**: 按功能拆分独立模块
```
app/
├── core/           # 核心功能模块
├── feature-game/   # 游戏控制功能
├── feature-device/ # 设备管理功能
├── feature-monitor/# 监控功能
└── shared/         # 共享组件
```

- [ ] **依赖注入**: 使用Dagger/Hilt进行依赖管理
- [ ] **响应式编程**: 引入RxJava处理异步操作
- [ ] **单元测试覆盖**: 提升测试覆盖率到80%以上

#### 🚀 性能优化
- [ ] **启动速度优化**: 减少应用启动时间
- [ ] **内存优化**: 降低内存占用和泄漏风险
- [ ] **网络优化**: 实现智能重连和流量控制
- [ ] **电池优化**: 减少后台耗电

## 🎯 开发最佳实践总结

### 11.1 代码质量保证

#### 📝 编码规范
```java
// 良好的命名规范
public class MqttCommunicationManager {
    private static final String TAG = "MqttCommunicationManager";
    private static final int DEFAULT_TIMEOUT_MS = 5000;

    private final MqttGameClient mqttClient;
    private final DeviceCache deviceCache;

    public MqttCommunicationManager(@NonNull MqttGameClient mqttClient,
                                   @NonNull DeviceCache deviceCache) {
        this.mqttClient = requireNonNull(mqttClient, "mqttClient cannot be null");
        this.deviceCache = requireNonNull(deviceCache, "deviceCache cannot be null");
    }

    /**
     * 更新设备状态
     * @param deviceId 设备ID，不能为空
     * @param status 新状态，不能为空
     * @throws IllegalArgumentException 如果参数无效
     */
    public void updateDeviceStatus(@NonNull String deviceId, @NonNull DeviceStatus status) {
        validateDeviceId(deviceId);
        validateStatus(status);

        LogUtils.d(TAG, "更新设备状态: " + deviceId + " -> " + status);
        deviceCache.updateStatus(deviceId, status);
        notifyStatusChanged(deviceId, status);
    }
}
```

#### 🧪 测试策略
```java
// 测试金字塔：单元测试 > 集成测试 > UI测试
@RunWith(MockitoJUnitRunner.class)
public class MqttCommunicationManagerTest {

    @Mock private MqttGameClient mockMqttClient;
    @Mock private DeviceCache mockDeviceCache;

    private MqttCommunicationManager communicationManager;

    @Before
    public void setUp() {
        communicationManager = new MqttCommunicationManager(mockMqttClient, mockDeviceCache);
    }

    @Test
    public void updateDeviceStatus_validInput_shouldUpdateCache() {
        // Given
        String deviceId = "device_001";
        DeviceStatus status = DeviceStatus.ONLINE;

        // When
        communicationManager.updateDeviceStatus(deviceId, status);

        // Then
        verify(mockDeviceCache).updateStatus(deviceId, status);
    }

    @Test(expected = IllegalArgumentException.class)
    public void updateDeviceStatus_nullDeviceId_shouldThrowException() {
        deviceStatusManager.updateDeviceStatus(null, DeviceStatus.ONLINE);
    }
}
```

### 11.2 安全开发指南

#### 🔒 数据安全
```java
// 敏感数据加密存储
public class SecurePreferences {
    private static final String PREFS_NAME = "secure_prefs";
    private static final String KEY_ALIAS = "xr_system_key";

    public void saveSecureData(String key, String value) {
        try {
            String encryptedValue = encrypt(value);
            getSharedPreferences().edit()
                .putString(key, encryptedValue)
                .apply();
        } catch (Exception e) {
            LogUtils.e("Security", "数据加密失败", e);
        }
    }

    private String encrypt(String plainText) throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, "AndroidKeyStore");
        KeyGenParameterSpec keyGenParameterSpec = new KeyGenParameterSpec.Builder(KEY_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT | KeyProperties.PURPOSE_DECRYPT)
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .build();

        keyGenerator.init(keyGenParameterSpec);
        SecretKey secretKey = keyGenerator.generateKey();

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        byte[] encryptedData = cipher.doFinal(plainText.getBytes());
        return Base64.encodeToString(encryptedData, Base64.DEFAULT);
    }
}
```

#### 🛡️ 网络安全
```java
// HTTPS证书校验
public class SecureHttpClient {
    private OkHttpClient createSecureClient() {
        return new OkHttpClient.Builder()
            .certificatePinner(new CertificatePinner.Builder()
                .add("api.xrsystem.com", "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
                .build())
            .hostnameVerifier((hostname, session) -> {
                return "api.xrsystem.com".equals(hostname);
            })
            .build();
    }
}
```

### 11.3 用户体验优化

#### 🎨 界面设计原则
- **一致性**: 保持整个应用的视觉和交互一致性
- **简洁性**: 避免不必要的复杂性，突出核心功能
- **反馈性**: 及时给用户操作反馈，包括加载状态、成功/失败提示
- **容错性**: 优雅处理错误情况，提供恢复建议

#### 📱 响应式设计
```xml
<!-- 使用ConstraintLayout实现响应式布局 -->
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 设备列表在小屏幕上占满宽度，大屏幕上限制最大宽度 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_devices"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintWidth_max="800dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
```

## 📚 学习资源和参考文档

### 12.1 官方文档
- [Android开发者指南](https://developer.android.com/guide)
- [Material Design规范](https://material.io/design)
- [Eclipse Paho MQTT文档](https://www.eclipse.org/paho/index.php?page=clients/android/index.php)
- [Gson用户指南](https://github.com/google/gson/blob/master/UserGuide.md)

### 12.2 推荐书籍
- 《Android开发艺术探索》- 任玉刚
- 《Effective Java》- Joshua Bloch
- 《Clean Code》- Robert C. Martin
- 《Android性能优化典型案例与代码分析》

### 12.3 在线资源
- [Android官方培训课程](https://developer.android.com/courses)
- [Google Codelabs](https://codelabs.developers.google.com/?cat=Android)
- [MQTT协议规范](https://mqtt.org/mqtt-specification/)

## 🤝 贡献指南

### 13.1 开发流程
1. **需求分析**: 明确功能需求和技术方案
2. **设计评审**: 进行架构设计和接口设计评审
3. **编码实现**: 按照编码规范进行开发
4. **代码审查**: 提交Pull Request进行代码审查
5. **测试验证**: 完成单元测试和集成测试
6. **部署发布**: 通过CI/CD流程部署到测试/生产环境

### 13.2 代码提交规范
```bash
# 提交消息格式
<type>(<scope>): <subject>

<body>

<footer>

# 示例
feat(device): 添加设备分组管理功能

- 实现设备按类型分组显示
- 添加分组筛选和搜索功能
- 优化设备列表性能

Closes #123
```

### 13.3 版本管理
- **主分支**: `main` - 生产环境代码
- **开发分支**: `develop` - 开发环境代码
- **功能分支**: `feature/xxx` - 新功能开发
- **修复分支**: `hotfix/xxx` - 紧急修复
- **发布分支**: `release/xxx` - 版本发布准备

---

## 📞 技术支持

### 联系方式
- **开发团队**: XR系统开发组
- **技术负责人**: [技术负责人姓名]
- **邮箱**: <EMAIL>
- **内部文档**: [内部文档链接]
- **问题反馈**: [问题跟踪系统链接]

### 紧急联系
- **7x24小时技术支持**: [支持电话]
- **紧急故障处理**: [故障处理流程]

---

**文档版本**: v2.0
**最后更新**: 2024-07-24
**维护人员**: XR系统开发团队
**审核状态**: ✅ 已审核
**适用版本**: Android App v1.0+
