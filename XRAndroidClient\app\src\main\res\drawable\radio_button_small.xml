<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 -->
    <item android:state_checked="true">
        <shape android:shape="oval">
            <size android:width="12dp" android:height="12dp"/>
            <stroke android:width="1.5dp" android:color="@color/white"/>
            <solid android:color="@color/white"/>
        </shape>
    </item>

    <!-- 未选中状态 -->
    <item android:state_checked="false">
        <shape android:shape="oval">
            <size android:width="12dp" android:height="12dp"/>
            <stroke android:width="1.5dp" android:color="@color/white"/>
            <solid android:color="@android:color/transparent"/>
        </shape>
    </item>

    <!-- 默认状态 -->
    <item>
        <shape android:shape="oval">
            <size android:width="12dp" android:height="12dp"/>
            <stroke android:width="1.5dp" android:color="@color/white"/>
            <solid android:color="@android:color/transparent"/>
        </shape>
    </item>
</selector>
