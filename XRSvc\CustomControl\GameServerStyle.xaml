﻿<UserControl x:Class="XRSvc.CustomControl.GameServerStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="50" d:DesignWidth="350">
    <Grid>
        <StackPanel Orientation="Horizontal">
            <TextBlock x:Name="TextBlock_ConnectState" Text="离线" Foreground="Red"  VerticalAlignment="Center" Margin="10,0,0,0" />
            <TextBlock x:Name="TextBlock_GameConnectState" Text="游戏未启动" Margin="40,0,0,0" Foreground="Red" VerticalAlignment="Center" />
            <Button x:Name="Button_Launch" Content="启动游戏服务端" Width="115" Height="26" Margin="40,0,0,0" Background="#FDFDFD" />
        </StackPanel>
    </Grid>
</UserControl>
