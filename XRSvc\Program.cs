﻿using Jskj.AppLog;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XRSvc.ViewModels;

namespace XRSvc
{
    /// <summary>
    /// 程序入口
    /// </summary>
    public class Program
    {

        #region Field

        /// <summary>
        /// 日志保存目录
        /// </summary>
        private static readonly string LogSaveDirPath = new DirectoryInfo(System.AppDomain.CurrentDomain.BaseDirectory).FullName;

        private static IServiceProvider _serviceProvider;

        #endregion

        #region Method

        /// <summary>
        /// 配置依赖注入服务
        /// </summary>
        public static void ConfigureServices()
        {
            var serviceCollection = new ServiceCollection();

            // Register ViewModels as singletons
            serviceCollection.AddSingleton<DeviceViewModel>();
            serviceCollection.AddSingleton<GameViewModel>();

            _serviceProvider = serviceCollection.BuildServiceProvider();
        }

        /// <summary>
        /// 程序入口
        /// </summary>
        /// <param name="Args">命令行参数</param>
        [System.STAThreadAttribute()]
        public static void Main(string[] Args)
        {
            try
            {
                ConfigureServices();

                Log.SetLogFileSaveDirPath(LogSaveDirPath);
                HBAPP app = new HBAPP();

                // Pass the DI container to the application if needed
                app.RunMain(Args);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                Environment.Exit(-2);
            }
        }

        #endregion

    }
}
