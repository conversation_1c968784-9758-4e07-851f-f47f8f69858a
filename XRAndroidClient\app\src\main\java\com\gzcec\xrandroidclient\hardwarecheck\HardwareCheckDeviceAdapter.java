package com.gzcec.xrandroidclient.hardwarecheck;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.device.DeviceType;
import com.gzcec.xrandroidclient.R;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;
import java.util.ArrayList;

public class HardwareCheckDeviceAdapter extends RecyclerView.Adapter<HardwareCheckDeviceAdapter.DeviceViewHolder> {
    private List<DeviceInfo> deviceList;

    public HardwareCheckDeviceAdapter(List<DeviceInfo> deviceList) {
        this.deviceList = deviceList;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.hardwarecheck_device_card, parent, false);
        return new DeviceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        onBindViewHolder(holder, position, new ArrayList<>());
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position, @NonNull List<Object> payloads) {
        DeviceInfo device = deviceList.get(position);
        Context context = holder.itemView.getContext();

        if (payloads != null && !payloads.isEmpty()) {
            // 只刷新运行状态
            updateRunningStatus(holder, device, context);
        } else {
            // 绑定设备数据
            bindDeviceData(holder, device, context);

            // 设置点击事件
            holder.btnStart.setOnClickListener(v -> {
                if (!device.isInGame()) {
                    device.setInGame(true);
                    notifyItemChanged(holder.getAdapterPosition(), "inGame");
                }
            });
            holder.btnStop.setOnClickListener(v -> {
                if (device.isInGame()) {
                    device.setInGame(false);
                    notifyItemChanged(holder.getAdapterPosition(), "inGame");
                }
            });
        }
    }

    /**
     * 绑定设备数据到视图
     */
    private void bindDeviceData(DeviceViewHolder holder, DeviceInfo device, Context context) {
        // 设备名称
        holder.tvDeviceName.setText(device.getName());

        // IP地址
        holder.tvDeviceIp.setText(device.getIpAddress());

        // 设备图标
        updateDeviceIcon(holder, device, context);

        // 在线状态
        updateOnlineStatus(holder, device, context);

        // 运行状态
        updateRunningStatus(holder, device, context);
    }

    /**
     * 更新设备图标
     */
    private void updateDeviceIcon(DeviceViewHolder holder, DeviceInfo device, Context context) {
        int iconRes = getDeviceIcon(device.getDeviceType());
        holder.ivDeviceIcon.setImageResource(iconRes);
    }

    /**
     * 更新在线状态
     */
    private void updateOnlineStatus(DeviceViewHolder holder, DeviceInfo device, Context context) {
        if (device.isOnline()) {
            holder.ivOnlineStatus.setBackgroundResource(R.drawable.ic_green_dot_alt);
            holder.tvOnlineStatus.setText("在线");
            holder.tvOnlineStatus.setTextColor(ContextCompat.getColor(context, R.color.status_online));
        } else {
            holder.ivOnlineStatus.setBackgroundResource(R.drawable.ic_red_dot_alt);
            holder.tvOnlineStatus.setText("离线");
            holder.tvOnlineStatus.setTextColor(ContextCompat.getColor(context, R.color.status_offline));
        }
    }

    /**
     * 更新运行状态
     */
    private void updateRunningStatus(DeviceViewHolder holder, DeviceInfo device, Context context) {
        if (device.isInGame()) {
            holder.ivRunningStatus.setBackgroundResource(R.drawable.ic_green_dot_alt);
            holder.tvRunningStatus.setText("运行");
            holder.tvRunningStatus.setTextColor(ContextCompat.getColor(context, R.color.status_running));
        } else {
            holder.ivRunningStatus.setBackgroundResource(R.drawable.ic_red_dot_alt);
            holder.tvRunningStatus.setText("停止");
            holder.tvRunningStatus.setTextColor(ContextCompat.getColor(context, R.color.status_stopped));
        }
    }

    /**
     * 根据设备类型获取对应图标
     */
    private int getDeviceIcon(DeviceType deviceType) {
        switch (deviceType) {
            case MotionPlatform:
                return R.drawable.ic_motion_platform;
            case Fan:
                return R.drawable.ic_fan_device;
            case WaterSpray:
                return R.drawable.ic_water_spray;
            case Hotness:
                return R.drawable.ic_hotness;
            case DoorManual:
                return R.drawable.ic_door_manual;
            case DoorCmdControl:
                return R.drawable.ic_door_cmd;
            default:
                return R.drawable.ic_hardware_default;
        }
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    static class DeviceViewHolder extends RecyclerView.ViewHolder {
        ImageView ivDeviceIcon;
        TextView tvDeviceName;
        TextView tvDeviceIp;
        View ivOnlineStatus;
        TextView tvOnlineStatus;
        View ivRunningStatus;
        TextView tvRunningStatus;
        Button btnStart, btnStop;

        DeviceViewHolder(@NonNull View itemView) {
            super(itemView);
            ivDeviceIcon = itemView.findViewById(R.id.iv_device_icon);
            tvDeviceName = itemView.findViewById(R.id.tv_device_name);
            tvDeviceIp = itemView.findViewById(R.id.tv_device_ip);
            ivOnlineStatus = itemView.findViewById(R.id.iv_online_status);
            tvOnlineStatus = itemView.findViewById(R.id.tv_online_status);
            ivRunningStatus = itemView.findViewById(R.id.iv_running_status);
            tvRunningStatus = itemView.findViewById(R.id.tv_running_status);
            btnStart = itemView.findViewById(R.id.btn_start);
            btnStop = itemView.findViewById(R.id.btn_stop);
        }
    }
} 