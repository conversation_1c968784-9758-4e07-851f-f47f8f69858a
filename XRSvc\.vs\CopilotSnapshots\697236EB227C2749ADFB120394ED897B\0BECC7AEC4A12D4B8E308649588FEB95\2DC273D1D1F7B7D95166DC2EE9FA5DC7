﻿using System.Collections.ObjectModel;
using XRSvc.DataPack;
using XRSvc.DataSource;

namespace XRSvc.ViewModels
{
    public class DeviceViewModel : BaseViewModel
    {
        #region Fields

        private ObservableCollection<DeviceSource> _devices;

        #endregion

        #region Properties

        public ObservableCollection<DeviceSource> Devices
        {
            get => _devices;
            set
            {
                _devices = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Constructor

        public DeviceViewModel()
        {
            _devices = new ObservableCollection<DeviceSource>();
        }

        #endregion

        #region Methods

        public void ResetDevices()
        {
            RunOnUIThread(() =>
            {
                foreach (var device in _devices)
                {
                    device.SerialNumber = string.Empty;
                    device.IP = string.Empty;
                    device.Name = string.Empty;
                    device.Type = DeviceType.SERVER;
                    device.IsEnabled = false;
                    device.IsOnline = false;
                }

                OnPropertyChanged(nameof(Devices));
            });
        }

        public void UpdateDevices(DeviceInfo[] deviceInfos)
        {
            RunOnUIThread(() =>
            {
                _devices.Clear();

                foreach (var info in deviceInfos)
                {
                    _devices.Add(new DeviceSource(info));
                }

                OnPropertyChanged(nameof(Devices));
            });
        }

        public DeviceInfo[] GetDeviceInfos()
        {
            var infos = new DeviceInfo[_devices.Count];
            for (int i = 0; i < _devices.Count; i++)
            {
                infos[i] = _devices[i].ToDeviceInfo();
            }
            return infos;
        }

        public void LoadDefaultDevices()
        {
            RunOnUIThread(() =>
            {
                _devices.Clear();

                var random = new Random();

                for (int i = 1; i <= 12; i++)
                {
                    var device = new DeviceSource(new DeviceInfo
                    {
                        ID = 8000 + i,
                        SerialNumber = $"SN-{i}",
                        Name = $"{i}号机",
                        Type = DeviceType.HMD,
                        BatteryLevel = random.Next(0, 100),
                        IsEnabled = random.Next(0, 2) == 1,
                        IsOnline = random.Next(0, 2) == 1
                    });
                    _devices.Add(device);
                }

                OnPropertyChanged(nameof(Devices));
            });
        }

        #endregion
    }
}