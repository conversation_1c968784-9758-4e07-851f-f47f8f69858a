package com.gzcec.xrandroidclient.sync;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.gzcec.xrandroidclient.communication.messages.device.DeviceListResponseMessage;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceStatusUpdateMessage;
import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.client.MqttGameClient;
import com.gzcec.xrandroidclient.device.DeviceInfo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 设备同步管理器
 * 负责管理Android端和PC端的设备信息同步
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2.0
 */
public class DeviceSyncManager implements MqttGameClient.DeviceStatusListener {
    
    private static final String TAG = "DeviceSyncManager";
    
    // ==================== 同步配置 ====================
    
    /**
     * 自动同步间隔（毫秒）
     */
    private static final long AUTO_SYNC_INTERVAL = 30000; // 30秒
    
    /**
     * 同步超时时间（毫秒）
     */
    private static final long SYNC_TIMEOUT = 10000; // 10秒
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;
    
    // ==================== 核心组件 ====================
    
    /**
     * MQTT客户端
     */
    private final MqttGameClient mqttClient;
    
    /**
     * 主线程Handler
     */
    private final Handler mainHandler;
    
    /**
     * 设备缓存 - 使用ConcurrentHashMap保证线程安全
     */
    private final Map<Integer, DeviceInfo> deviceCache = new ConcurrentHashMap<>();
    
    /**
     * 同步监听器列表
     */
    private final List<DeviceSyncListener> syncListeners = new CopyOnWriteArrayList<>();
    
    // ==================== 同步状态 ====================
    
    /**
     * 是否正在同步
     */
    private volatile boolean isSyncing = false;
    
    /**
     * 最后同步时间
     */
    private volatile Date lastSyncTime;
    
    /**
     * 同步重试次数
     */
    private volatile int retryCount = 0;
    
    /**
     * 自动同步任务
     */
    private Runnable autoSyncTask;
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数
     * @param mqttClient MQTT客户端实例
     */
    public DeviceSyncManager(MqttGameClient mqttClient) {
        this.mqttClient = mqttClient;
        this.mainHandler = new Handler(Looper.getMainLooper());

        // 注册为设备状态监听器
        if (mqttClient != null) {
            mqttClient.setDeviceStatusListener(this);
        }

        // 初始化设备缓存
        initializeDeviceCache();

        // 初始化自动同步任务
        initAutoSyncTask();
    }
    
    // ==================== 初始化方法 ====================

    /**
     * 初始化设备缓存
     * 使用默认设备列表预填充缓存，等待PC端更新
     */
    private void initializeDeviceCache() {
        Log.i(TAG, "初始化设备缓存");

        // 获取初始设备列表
        List<DeviceInfo> initialDevices = DeviceInfo.createInitialDeviceList();

        // 填充缓存
        for (DeviceInfo device : initialDevices) {
            deviceCache.put(device.getId(), device);
        }

        Log.i(TAG, "设备缓存初始化完成，设备数量: " + deviceCache.size());
    }

    // ==================== 公共接口 ====================

    /**
     * 开始同步服务
     */
    public void startSync() {
        Log.i(TAG, "启动设备同步服务");

        // 立即执行一次同步
        requestDeviceListSync(true);

        // 启动自动同步
        startAutoSync();
    }
    
    /**
     * 停止同步服务
     */
    public void stopSync() {
        Log.i(TAG, "停止设备同步服务");
        
        // 停止自动同步
        stopAutoSync();
        
        // 清理状态
        isSyncing = false;
        retryCount = 0;
    }
    
    /**
     * 手动请求设备列表同步
     * @param onlineOnly 是否只获取在线设备
     */
    public void requestDeviceListSync(boolean onlineOnly) {
        if (isSyncing) {
            Log.w(TAG, "正在同步中，忽略重复请求");
            return;
        }
        
        if (mqttClient == null || !mqttClient.isConnected()) {
            Log.w(TAG, "MQTT客户端未连接，无法同步设备列表");
            notifySyncFailed("MQTT客户端未连接");
            return;
        }
        
        Log.i(TAG, "请求设备列表同步，onlineOnly=" + onlineOnly);
        isSyncing = true;
        retryCount = 0;
        
        // 设置超时处理
        mainHandler.postDelayed(() -> {
            if (isSyncing) {
                handleSyncTimeout();
            }
        }, SYNC_TIMEOUT);
        
        // 发送同步请求
        mqttClient.requestDeviceList(onlineOnly, new MqttGameClient.ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                Log.d(TAG, "收到设备列表同步响应");
                // 响应处理在onDeviceListResponse中进行
            }

            @Override
            public void onTimeout() {
                Log.w(TAG, "设备列表同步请求超时");
                handleSyncTimeout();
            }
        });
        
        notifySyncStarted();
    }
    
    /**
     * 获取缓存的设备列表
     * @return 设备信息列表
     */
    public List<DeviceInfo> getCachedDevices() {
        return new ArrayList<>(deviceCache.values());
    }
    
    /**
     * 根据ID获取设备信息
     * @param deviceId 设备ID
     * @return 设备信息，如果不存在返回null
     */
    public DeviceInfo getDevice(int deviceId) {
        return deviceCache.get(deviceId);
    }
    
    /**
     * 获取在线设备列表
     * @return 在线设备列表
     */
    public List<DeviceInfo> getOnlineDevices() {
        List<DeviceInfo> onlineDevices = new ArrayList<>();
        for (DeviceInfo device : deviceCache.values()) {
            if (device.isOnline()) {
                onlineDevices.add(device);
            }
        }
        return onlineDevices;
    }
    
    /**
     * 获取选中的设备列表
     * @return 选中设备列表
     */
    public List<DeviceInfo> getSelectedDevices() {
        List<DeviceInfo> selectedDevices = new ArrayList<>();
        for (DeviceInfo device : deviceCache.values()) {
            if (device.isSelected()) {
                selectedDevices.add(device);
            }
        }
        return selectedDevices;
    }
    
    /**
     * 更新设备选择状态
     * @param deviceId 设备ID
     * @param selected 是否选中
     */
    public void updateDeviceSelection(int deviceId, boolean selected) {
        DeviceInfo device = deviceCache.get(deviceId);
        if (device != null) {
            device.setSelected(selected);
            notifyDeviceSelectionChanged(device);
        }
    }
    
    /**
     * 添加同步监听器
     * @param listener 监听器
     */
    public void addSyncListener(DeviceSyncListener listener) {
        if (listener != null && !syncListeners.contains(listener)) {
            syncListeners.add(listener);
        }
    }
    
    /**
     * 移除同步监听器
     * @param listener 监听器
     */
    public void removeSyncListener(DeviceSyncListener listener) {
        syncListeners.remove(listener);
    }
    
    /**
     * 获取同步状态信息
     * @return 同步状态信息
     */
    public SyncStatus getSyncStatus() {
        return new SyncStatus(
            isSyncing,
            lastSyncTime,
            deviceCache.size(),
            getOnlineDevices().size(),
            retryCount
        );
    }
    
    // ==================== MQTT监听器实现 ====================
    
    @Override
    public void onDeviceListResponse(DeviceListResponseMessage response) {
        Log.i(TAG, "收到设备列表响应，设备数量: " + response.getTotalCount());
        
        try {
            // 更新设备缓存
            updateDeviceCache(response.getDevices());
            
            // 更新同步状态
            isSyncing = false;
            lastSyncTime = new Date();
            retryCount = 0;
            
            // 通知同步完成
            notifySyncCompleted(response.getTotalCount(), response.getOnlineCount());
            
        } catch (Exception e) {
            Log.e(TAG, "处理设备列表响应失败", e);
            handleSyncError("处理设备列表响应失败: " + e.getMessage());
        }
    }
    
    @Override
    public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update) {
        Log.d(TAG, "收到设备状态更新，设备数量: " + update.getDevices().size());
        
        try {
            // 更新设备状态
            updateDeviceStatus(update.getDevices());
            
            // 通知状态更新
            notifyDeviceStatusUpdated(update.getDevices());
            
        } catch (Exception e) {
            Log.e(TAG, "处理设备状态更新失败", e);
        }
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 初始化自动同步任务
     */
    private void initAutoSyncTask() {
        autoSyncTask = new Runnable() {
            @Override
            public void run() {
                if (!isSyncing) {
                    requestDeviceListSync(false);
                }
                // 安排下次执行
                mainHandler.postDelayed(this, AUTO_SYNC_INTERVAL);
            }
        };
    }
    
    /**
     * 启动自动同步
     */
    private void startAutoSync() {
        stopAutoSync(); // 先停止之前的任务
        mainHandler.postDelayed(autoSyncTask, AUTO_SYNC_INTERVAL);
        Log.i(TAG, "启动自动同步，间隔: " + AUTO_SYNC_INTERVAL + "ms");
    }
    
    /**
     * 停止自动同步
     */
    private void stopAutoSync() {
        if (autoSyncTask != null) {
            mainHandler.removeCallbacks(autoSyncTask);
        }
        Log.i(TAG, "停止自动同步");
    }
    
    /**
     * 更新设备缓存
     * 以PC端设备信息为主，根据设备ID匹配更新缓存中的设备信息
     * @param devices 从PC端接收的设备状态信息列表
     */
    private void updateDeviceCache(List<DeviceInfo> devices) {
        if (devices == null) {
            return;
        }

        Log.i(TAG, "开始更新设备缓存，收到PC端设备数量: " + devices.size());

        // 将缓存转换为列表，使用DeviceInfo的更新方法
        List<DeviceInfo> cachedDeviceList = new ArrayList<>(deviceCache.values());

        // 使用DeviceInfo的静态方法进行更新，以PC端为主
        int updatedCount = DeviceInfo.updateDeviceListFromServer(cachedDeviceList, devices);

        // 重新构建缓存Map
        deviceCache.clear();
        for (DeviceInfo device : cachedDeviceList) {
            deviceCache.put(device.getId(), device);
        }

        Log.i(TAG, "设备缓存更新完成，更新设备数量: " + updatedCount + "，当前缓存设备总数: " + deviceCache.size());

        // 记录在线设备数量
        long onlineCount = deviceCache.values().stream()
                .filter(DeviceInfo::isOnline)
                .count();
        Log.i(TAG, "当前在线设备数量: " + onlineCount);
    }
    
    /**
     * 更新设备状态
     * @param devices 设备状态信息列表
     */
    private void updateDeviceStatus(List<DeviceInfo> devices) {
        if (devices == null) {
            return;
        }

        for (DeviceInfo device : devices) {
            try {
                int deviceId = device.getId();
                DeviceInfo cachedDevice = deviceCache.get(deviceId);

                if (cachedDevice != null) {
                    // 保持选择状态
                    device.setSelected(cachedDevice.isSelected());
                    deviceCache.put(deviceId, device);
                } else {
                    // 添加新设备
                    deviceCache.put(deviceId, device);
                }
            } catch (Exception e) {
                Log.w(TAG, "更新设备状态失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理同步超时
     */
    private void handleSyncTimeout() {
        Log.w(TAG, "设备同步超时");
        handleSyncError("同步超时");
    }
    
    /**
     * 处理同步错误
     * @param errorMessage 错误消息
     */
    private void handleSyncError(String errorMessage) {
        isSyncing = false;
        retryCount++;
        
        if (retryCount < MAX_RETRY_COUNT) {
            Log.i(TAG, "同步失败，准备重试 (" + retryCount + "/" + MAX_RETRY_COUNT + ")");
            mainHandler.postDelayed(() -> requestDeviceListSync(false), 2000 * retryCount);
        } else {
            Log.e(TAG, "同步失败，已达到最大重试次数: " + errorMessage);
            retryCount = 0;
            notifySyncFailed(errorMessage);
        }
    }
    
    // ==================== 通知方法 ====================
    
    private void notifySyncStarted() {
        mainHandler.post(() -> {
            for (DeviceSyncListener listener : syncListeners) {
                try {
                    listener.onSyncStarted();
                } catch (Exception e) {
                    Log.e(TAG, "通知同步开始失败", e);
                }
            }
        });
    }
    
    private void notifySyncCompleted(int totalCount, int onlineCount) {
        mainHandler.post(() -> {
            for (DeviceSyncListener listener : syncListeners) {
                try {
                    listener.onSyncCompleted(getCachedDevices(), totalCount, onlineCount);
                } catch (Exception e) {
                    Log.e(TAG, "通知同步完成失败", e);
                }
            }
        });
    }
    
    private void notifySyncFailed(String errorMessage) {
        mainHandler.post(() -> {
            for (DeviceSyncListener listener : syncListeners) {
                try {
                    listener.onSyncFailed(errorMessage);
                } catch (Exception e) {
                    Log.e(TAG, "通知同步失败失败", e);
                }
            }
        });
    }
    
    private void notifyDeviceStatusUpdated(List<DeviceInfo> updatedDevices) {
        mainHandler.post(() -> {
            for (DeviceSyncListener listener : syncListeners) {
                try {
                    listener.onDeviceStatusUpdated(updatedDevices);
                } catch (Exception e) {
                    Log.e(TAG, "通知设备状态更新失败", e);
                }
            }
        });
    }
    
    private void notifyDeviceSelectionChanged(DeviceInfo device) {
        mainHandler.post(() -> {
            for (DeviceSyncListener listener : syncListeners) {
                try {
                    listener.onDeviceSelectionChanged(device);
                } catch (Exception e) {
                    Log.e(TAG, "通知设备选择变更失败", e);
                }
            }
        });
    }
    
    // ==================== 内部类 ====================
    
    /**
     * 同步状态信息
     */
    public static class SyncStatus {
        public final boolean isSyncing;
        public final Date lastSyncTime;
        public final int totalDevices;
        public final int onlineDevices;
        public final int retryCount;
        
        public SyncStatus(boolean isSyncing, Date lastSyncTime, int totalDevices, int onlineDevices, int retryCount) {
            this.isSyncing = isSyncing;
            this.lastSyncTime = lastSyncTime;
            this.totalDevices = totalDevices;
            this.onlineDevices = onlineDevices;
            this.retryCount = retryCount;
        }
        
        @Override
        public String toString() {
            return String.format("SyncStatus{syncing=%s, lastSync=%s, total=%d, online=%d, retry=%d}",
                               isSyncing, lastSyncTime, totalDevices, onlineDevices, retryCount);
        }
    }
    
    /**
     * 设备同步监听器接口
     */
    public interface DeviceSyncListener {
        /**
         * 同步开始
         */
        void onSyncStarted();
        
        /**
         * 同步完成
         * @param devices 设备列表
         * @param totalCount 总设备数
         * @param onlineCount 在线设备数
         */
        void onSyncCompleted(List<DeviceInfo> devices, int totalCount, int onlineCount);
        
        /**
         * 同步失败
         * @param errorMessage 错误消息
         */
        void onSyncFailed(String errorMessage);
        
        /**
         * 设备状态更新
         * @param updatedDevices 更新的设备列表
         */
        void onDeviceStatusUpdated(List<DeviceInfo> updatedDevices);
        
        /**
         * 设备选择状态变更
         * @param device 变更的设备
         */
        void onDeviceSelectionChanged(DeviceInfo device);
    }
}
