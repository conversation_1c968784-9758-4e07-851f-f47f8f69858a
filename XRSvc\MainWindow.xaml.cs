﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using XRSvc.DataPack;
using XRSvc.DataSource;
using XRSvc.ViewModels;
using XRSvc.Utils;
using XRSvc.Services;
using Jskj.AppLog;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        #region Fields

        public ICommand SaveGameNameCommand { get; }

        private GameViewModel _gameViewModel;
        private DeviceViewModel _deviceViewModel;
        private DeviceServerViewModel _deviceServerViewModel;

        /// <summary>
        /// 游戏启动服务实例
        /// </summary>
        private readonly GameLaunchService _gameLaunchService;

        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        public ObservableCollection<string> LogMessages { get; } = new ObservableCollection<string>();

        private long lastPosition = 0;

        private string logPath = System.IO.Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "Logs", "XRSvc.log");

        #endregion

        #region Properties

        public GameViewModel GameViewModel => _gameViewModel;
        public DeviceViewModel DeviceViewModel => _deviceViewModel;
        public DeviceServerViewModel DeviceServerViewModel => _deviceServerViewModel;

        private int _selectedGameId;
        /// <summary>
        /// 当前选中的游戏ID
        /// </summary>
        public int SelectedGameId
        {
            get => _selectedGameId;
            set
            {
                if (_selectedGameId != value)
                {
                    _selectedGameId = value;
                    OnPropertyChanged(nameof(SelectedGameId));
                    SaveSelectedToConfig();
                }
            }
        }

        private string _selectedDeviceServerTag;
        /// <summary>
        /// 当前选中的设备管理服务器Tag
        /// </summary>
        public string SelectedDeviceServerTag
        {
            get => _selectedDeviceServerTag;
            set
            {
                if (_selectedDeviceServerTag != value)
                {
                    _selectedDeviceServerTag = value;
                    OnPropertyChanged(nameof(SelectedDeviceServerTag));
                    SaveSelectedToConfig();
                }
            }
        }

        /// <summary>
        /// 获取游戏启动服务的公共属性
        /// </summary>
        public GameLaunchService GameLaunchService => _gameLaunchService;

        #endregion

        #region Constructor

        /// <summary>
        /// 主窗口构造函数，初始化各ViewModel并加载数据
        /// </summary>
        /// <param name="gameInfos">游戏信息列表</param>
        /// <param name="deviceInfos">设备信息列表</param>
        /// <param name="deviceServers">设备服务器信息列表</param>
        public MainWindow(List<GameInfo> gameInfos, List<DeviceInfo> deviceInfos, List<DeviceServerInfo> deviceServers)
        {
            InitializeComponent(); // 初始化XAML组件
            _gameViewModel = new GameViewModel();
            _deviceViewModel = new DeviceViewModel();
            _deviceServerViewModel = new DeviceServerViewModel();

            // 初始化游戏启动服务
            _gameLaunchService = new GameLaunchService();
            _gameLaunchService.LaunchProgress += OnGameLaunchProgress;
            _gameLaunchService.LaunchCompleted += OnGameLaunchCompleted;

            // 初始化保存游戏名称命令
            _gameViewModel.SaveGameNameCommand = new RelayCommand<XRSvc.DataSource.GameSource>(SaveGameName);

            // 更新ViewModel中的数据
            _gameViewModel.UpdateSource(gameInfos.ToArray());
            _deviceViewModel.UpdateSource(deviceInfos.ToArray());
            _deviceServerViewModel.UpdateSource(deviceServers.ToArray());

            // 设置默认游戏选择（选择第一个游戏）
            if (_gameViewModel.Source.Count > 0)
            {
                _gameViewModel.SetDefaultSelection(_gameViewModel.Source[0].ID);
                this.SelectedGameId = _gameViewModel.Source[0].ID;
            }

            this.DataContext = this; // 设置数据上下文，便于数据绑定
            StartLogFileWatcher();   // 启动日志文件监听

            // 可选：运行UI优化测试（仅在调试模式下）
            #if DEBUG
            Task.Run(async () =>
            {
                await Task.Delay(3000); // 等待3秒让界面完全加载
                try
                {
                    await TestUIOptimization.TestBasicFunctionality();
                }
                catch (Exception ex)
                {
                    Log.Write(Level.ERROR, $"UI优化测试失败: {ex.Message}");
                }
            });
            #endif

            // 注册窗口拖动事件
            this.MouseLeftButtonDown += MainWindow_MouseLeftButtonDown;
        }

        /// <summary>
        /// 支持初始化选中项的主窗口构造函数
        /// </summary>
        /// <param name="gameInfos">游戏信息列表</param>
        /// <param name="deviceInfos">设备信息列表</param>
        /// <param name="deviceServers">设备服务器信息列表</param>
        /// <param name="lastSelectedGameId">上次选择的游戏ID</param>
        /// <param name="lastSelectedDeviceServerTag">上次选择的设备服务器Tag</param>
        public MainWindow(List<GameInfo> gameInfos, List<DeviceInfo> deviceInfos, List<DeviceServerInfo> deviceServers, int lastSelectedGameId, string lastSelectedDeviceServerTag)
            : this(gameInfos, deviceInfos, deviceServers)
        {
            this.SelectedGameId = lastSelectedGameId;
            this.SelectedDeviceServerTag = lastSelectedDeviceServerTag;
            
            // 设置GameViewModel的选中游戏
            if (lastSelectedGameId != 0)
            {
                _gameViewModel.SetDefaultSelection(lastSelectedGameId);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// 保存游戏名称的方法，弹出提示框显示修改结果
        /// </summary>
        /// <param name="gameSource">要保存的游戏源对象</param>
        private void SaveGameName(XRSvc.DataSource.GameSource gameSource)
        {
            if (gameSource != null)
            {
                MessageBox.Show($"游戏名称已修改为: {gameSource.Name}", "修改成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 窗口关闭时释放资源
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnClosed(EventArgs e)
        {
            _cancellationTokenSource.Cancel(); // 取消日志监听任务
            _cancellationTokenSource.Dispose();
            base.OnClosed(e);
        }

        /// <summary>
        /// 鼠标左键按下时实现窗口拖动
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">鼠标事件参数</param>
        private void MainWindow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 判断是否为左键按下
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        #endregion

        #region Game Launch Event Handlers

        /// <summary>
        /// 游戏启动进度事件处理（优化版）
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">进度事件参数</param>
        private void OnGameLaunchProgress(object sender, GameLaunchProgressEventArgs e)
        {
            var uiScheduler = UIThreadScheduler.Instance;

            // 使用批量处理，避免频繁的UI线程切换
            uiScheduler.BatchInvokeOnUIThread(() =>
            {
                // 日志显示
                LogMessages.Add($"[{DateTime.Now:HH:mm:ss}] 设备 {e.SerialNumber} 启动游戏 {e.GameName}: {e.Status}");
                // 分步刷新设备UI（IsGameRunning属性已由GameLaunchService设置，WPF依赖属性会自动通知UI，无需手动触发）
                // var device = _deviceViewModel?.Source?.FirstOrDefault(d => d.SerialNumber == e.SerialNumber);
                // if (device != null)
                // {
                //     device.OnPropertyChanged(nameof(device.IsGameRunning));
                // }
            }, $"game_progress_{e.SerialNumber}");
        }

        /// <summary>
        /// 游戏启动完成事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">完成事件参数</param>
        private void OnGameLaunchCompleted(object sender, GameLaunchResultEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                var result = e.Result;
                string message = $"游戏启动完成 - 游戏: {result.GameName}\n" +
                               $"总设备数: {result.TotalDevices}, 成功: {result.SuccessCount}, 失败: {result.FailedCount}";

                if (result.FailedDevices.Any())
                {
                    message += "\n\n失败设备:\n";
                    foreach (var device in result.FailedDevices)
                    {
                        message += $"  {device.SerialNumber}: {device.ErrorMessage}\n";
                    }
                }

                Log.Write(result.Success ? Level.INFO : Level.ERROR, message);
            });
        }

        #endregion

        #region Methods

        /// <summary>
        /// 启动日志文件监听，将新增日志内容实时添加到LogMessages集合（优化版）
        /// </summary>
        private void StartLogFileWatcher()
        {
            var uiScheduler = UIThreadScheduler.Instance;

            Task.Run(() =>
            {
                var logBuffer = new List<string>();

                while (true)
                {
                    try
                    {
                        if (File.Exists(logPath))
                        {
                            using (var fs = new FileStream(logPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                            {
                                fs.Seek(lastPosition, SeekOrigin.Begin); // 从上次读取位置继续
                                using (var sr = new StreamReader(fs))
                                {
                                    string line;
                                    logBuffer.Clear();

                                    // 批量读取日志行
                                    while ((line = sr.ReadLine()) != null)
                                    {
                                        logBuffer.Add(line);
                                    }

                                    // 批量添加到UI集合，减少UI线程切换
                                    if (logBuffer.Count > 0)
                                    {
                                        var logsToAdd = logBuffer.ToList(); // 复制避免并发问题
                                        uiScheduler.BatchInvokeOnUIThread(() =>
                                        {
                                            foreach (var logLine in logsToAdd)
                                            {
                                                LogMessages.Add(logLine);
                                            }
                                        }, "log_batch_update");
                                    }

                                    lastPosition = fs.Position; // 更新已读取位置
                                }
                            }
                        }
                    }
                    catch { }

                    Thread.Sleep(1000); // 每秒检查一次
                }
            }, _cancellationTokenSource.Token);
        }

        #endregion

        #region Button Event Handlers

        /// <summary>
        /// "全选"按钮点击事件，将所有在线设备设为启用
        /// </summary>
        private void Btn_SelectAll_Click(object sender, RoutedEventArgs e)
        {
            if (DeviceViewModel?.Source != null)
            {
                foreach (var device in DeviceViewModel.Source)
                {
                    // 只对在线设备进行勾选
                    if (device.IsOnline)
                    {
                        device.IsEnabled = true;
                    }
                }
            }
        }

        /// <summary>
        /// "全不选"按钮点击事件，将所有设备设为禁用
        /// </summary>
        private void Btn_UnselectAll_Click(object sender, RoutedEventArgs e)
        {
            if (DeviceViewModel?.Source != null)
            {
                foreach (var device in DeviceViewModel.Source)
                {
                    device.IsEnabled = false;
                }
            }
        }

        /// <summary>
        /// "启动游戏"按钮点击事件
        /// </summary>
        private async void Btn_StartGame_Click(object sender, RoutedEventArgs e)
        {
            var startTime = DateTime.Now;
            Log.Write(Level.INFO, $"[启动游戏] 按钮点击，时间: {startTime:HH:mm:ss.fff}");
            try
            {
                var selectedGame = _gameViewModel.SelectedGame;
                if (selectedGame == null)
                {
                    Log.Write(Level.INFO, "请先选择一个游戏");
                    return;
                }

                var selectedDevices = _deviceViewModel.Source?.Where(d => d.IsEnabled).ToList();
                if (selectedDevices == null || !selectedDevices.Any())
                {
                    Log.Write(Level.INFO, "请至少勾选一个设备");
                    return;
                }

                var onlineDevices = selectedDevices.Where(d => d.IsOnline).ToList();
                if (!onlineDevices.Any())
                {
                    Log.Write(Level.INFO, "没有在线设备可以启动游戏");
                    return;
                }

                var button = sender as Button;
                if (button != null)
                {
                    button.IsEnabled = false;
                    button.Content = "启动中...";
                }

                var tasks = onlineDevices.Select(device =>
                    _gameLaunchService.LaunchGameForDeviceAsync(selectedGame, device)
                ).ToList();
                await Task.WhenAll(tasks);

                if (button != null)
                {
                    button.IsEnabled = true;
                    button.Content = "启动游戏";
                }

                var endTime = DateTime.Now;
                Log.Write(Level.INFO, $"[启动游戏] 批量启动完成，时间: {endTime:HH:mm:ss.fff}，耗时: {(endTime-startTime).TotalSeconds:F3}秒");
                Log.Write(Level.INFO, $"批量启动完成，游戏: {selectedGame.Name}，设备数: {onlineDevices.Count}");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"启动游戏时发生错误: {ex.Message}");
                var button = sender as Button;
                if (button != null)
                {
                    button.IsEnabled = true;
                    button.Content = "启动游戏";
                }
            }
        }

        /// <summary>
        /// "开始游戏"按钮点击事件
        /// </summary>
        private void Btn_StartGameSession_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("开始游戏按钮被点击", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// "结束游戏"按钮点击事件
        /// </summary>
        private async void Btn_EndGame_Click(object sender, RoutedEventArgs e)
        {
            var startTime = DateTime.Now;
            Log.Write(Level.INFO, $"[结束游戏] 按钮点击，时间: {startTime:HH:mm:ss.fff}");
            try
            {
                var selectedDevices = _deviceViewModel.Source?.Where(d => d.IsEnabled).ToList();
                if (selectedDevices == null || !selectedDevices.Any())
                {
                    Log.Write(Level.INFO, "请至少勾选一个设备");
                    return;
                }

                var onlineDevices = selectedDevices.Where(d => d.IsOnline).ToList();
                if (!onlineDevices.Any())
                {
                    Log.Write(Level.INFO, "没有在线设备可以停止游戏");
                    return;
                }

                var selectedGame = _gameViewModel.Source?.FirstOrDefault(g => g.ID == SelectedGameId);
                if (selectedGame == null)
                {
                    Log.Write(Level.INFO, "请先选择一个游戏");
                    return;
                }

                var button = sender as Button;
                if (button != null)
                {
                    button.IsEnabled = false;
                    button.Content = "停止中...";
                }

                var tasks = onlineDevices.Select(device =>
                    _gameLaunchService.StopGameForDeviceAsync(selectedGame, device)
                ).ToList();
                await Task.WhenAll(tasks);

                if (button != null)
                {
                    button.IsEnabled = true;
                    button.Content = "结束游戏";
                }

                var endTime = DateTime.Now;
                Log.Write(Level.INFO, $"[结束游戏] 批量停止完成，时间: {endTime:HH:mm:ss.fff}，耗时: {(endTime-startTime).TotalSeconds:F3}秒");
                Log.Write(Level.INFO, $"批量停止完成，游戏: {selectedGame.Name}，设备数: {onlineDevices.Count}");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"停止游戏时发生错误: {ex.Message}");
                var button = sender as Button;
                if (button != null)
                {
                    button.IsEnabled = true;
                    button.Content = "结束游戏";
                }
            }
        }

        /// <summary>
        /// 关闭按钮点击事件，关闭主窗口
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">鼠标事件参数</param>
        private void Btn_WinClose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 最小化按钮点击事件，将窗口最小化
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">鼠标事件参数</param>
        private void Btn_WinMin_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// 最大化按钮点击事件，将窗口最大化
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">鼠标事件参数</param>
        private void Btn_WinMax_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.WindowState = WindowState.Maximized;
        }

        /// <summary>
        /// 恢复按钮点击事件，将窗口恢复到正常大小
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">鼠标事件参数</param>
        private void Btn_WinRecover_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.WindowState = WindowState.Normal;
        }

        #endregion

        #region Window State Management

        /// <summary>
        /// 窗口状态改变事件处理
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnStateChanged(EventArgs e)
        {
            base.OnStateChanged(e);
            if (this.WindowState == WindowState.Maximized)
            {
                Btn_WinMax.Visibility = Visibility.Collapsed;
                Btn_WinRecover.Visibility = Visibility.Visible;
            }
            else
            {
                Btn_WinMax.Visibility = Visibility.Visible;
                Btn_WinRecover.Visibility = Visibility.Collapsed;
            }
        }

        #endregion

        #region Configuration Management

        /// <summary>
        /// 获取当前选中的游戏ID
        /// </summary>
        /// <returns>当前选中的游戏ID</returns>
        public int GetCurrentSelectedGameId()
        {
            return SelectedGameId;
        }

        /// <summary>
        /// 获取当前选中的设备管理服务器Tag
        /// </summary>
        /// <returns>当前选中的设备管理服务器Tag</returns>
        public string GetCurrentSelectedDeviceServerTag()
        {
            return SelectedDeviceServerTag;
        }

        /// <summary>
        /// 保存选中项到配置文件
        /// </summary>
        private void SaveSelectedToConfig()
        {
            // 这里可以添加保存到配置文件的逻辑
            // 暂时留空，后续可以扩展
        }

        #endregion

        #region INotifyPropertyChanged

        /// <summary>
        /// 属性改变事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性改变事件
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
