﻿<UserControl x:Class="XRSvc.CustomControl.XRClientStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="190" d:DesignWidth="150" Background="#B4B4B4">
    <UserControl.Resources>
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter" />
        <local:BoolToColorConverter x:Key="BoolToColorConverter" />
        <local:NameIdFormatConverter x:Key="NameIdFormatConverter" />
    </UserControl.Resources>
    <Grid Width="150" Height="190">
        <StackPanel Orientation="Vertical">
            <Grid Margin="10,10,0,0">
                <CheckBox x:Name="CheckBox_Select" IsChecked="{Binding IsEnabled}" IsEnabled="{Binding IsOnline}">
                    <CheckBox.Content>
                        <MultiBinding Converter="{StaticResource NameIdFormatConverter}">
                            <Binding Path="Name"/>
                            <Binding Path="ID"/>
                        </MultiBinding>
                    </CheckBox.Content>
                </CheckBox>
                <TextBlock x:Name="TextBlock_ClientStatus" Text="{Binding IsOnline, Converter={StaticResource BoolToStatusConverter}}" Foreground="{Binding IsOnline, Converter={StaticResource BoolToColorConverter}}" HorizontalAlignment="Right" Margin="0,0,10,0"/>
            </Grid>
            <Grid HorizontalAlignment="Center" Margin="0,20,0,0">
                <!-- 游戏状态显示文本 -->
                <TextBlock x:Name="TextBlock_GameStatus" Text="游戏未启动" Foreground="Red"/>
            </Grid>
            <StackPanel Orientation="Vertical" Margin="0,25,0,0">
                <!-- 加入游戏按钮 -->
                <Button x:Name="Btn_JoinGame" Content="加入游戏" Width="110" Height="35" Click="Btn_JoinGame_Click"/>
                <!-- 退出游戏按钮 -->
                <Button x:Name="Btn_ExitGame" Content="退出游戏" Width="110" Height="35" Margin="0,10,0,0" Click="Btn_ExitGame_Click"/>
            </StackPanel>
        </StackPanel>
            
    </Grid>
</UserControl>
