﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Threading;
using XRSvc.DataPack;
using XRSvc.DataSource;

namespace XRSvc.ViewModels
{
    public class DeviceViewModel : INotifyPropertyChanged
    {
        #region Fields

        private ObservableCollection<DeviceSource> _devices;

        #endregion

        #region Properties

        public ObservableCollection<DeviceSource> Devices
        {
            get => _devices;
            set
            {
                _devices = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Events

        public event PropertyChangedEventHandler PropertyChanged;

        #endregion

        #region Constructor

        public DeviceViewModel()
        {
            _devices = new ObservableCollection<DeviceSource>();
        }

        #endregion

        #region Methods

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void ResetDevices()
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;

            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(ResetDevices);
                return;
            }

            foreach (var device in _devices)
            {
                device.SerialNumber = string.Empty;
                device.IP = string.Empty;
                device.Name = string.Empty;
                device.Type = DeviceType.SERVER;
                device.IsEnabled = false;
                device.IsOnline = false;
            }

            OnPropertyChanged(nameof(Devices));
        }

        public void UpdateDevices(DeviceInfo[] deviceInfos)
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;

            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(() => UpdateDevices(deviceInfos));
                return;
            }

            _devices.Clear();

            foreach (var info in deviceInfos)
            {
                _devices.Add(new DeviceSource(info));
            }

            OnPropertyChanged(nameof(Devices));
        }

        public DeviceInfo[] GetDeviceInfos()
        {
            var infos = new DeviceInfo[_devices.Count];
            for (int i = 0; i < _devices.Count; i++)
            {
                infos[i] = _devices[i].ToDeviceInfo();
            }
            return infos;
        }

        public void LoadDefaultDevices(int count = 3)
        {
            Dispatcher dispatcher = Application.Current.Dispatcher;
            if (!dispatcher.CheckAccess())
            {
                dispatcher.Invoke(() => LoadDefaultDevices(count));
                return;
            }
            _devices.Clear();

            for (int i = 1; i <= count; i++)
            {
                var deviceInfo = new DeviceInfo
                {
                    ID = i,
                    SerialNumber = $"SN{i:D3}",
                    IP = $"192.168.1.{i}",
                    Name = $"Device{i}",
                    Type = (DeviceType)(i % Enum.GetValues(typeof(DeviceType)).Length),
                    IsEnabled = (i % 2 == 1),
                    IsOnline = (i % 2 == 0),
                    BatteryLevel = 100 - (i - 1) * 10
                };
                _devices.Add(new DeviceSource(deviceInfo));
            }

            OnPropertyChanged(nameof(Devices));
        }

        #endregion
    }
}