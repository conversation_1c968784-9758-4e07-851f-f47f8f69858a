package com.gzcec.xrandroidclient;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

public class BatteryView extends View {
    private int level = 0; // 0-4
    private int maxLevel = 4;
    private int outlineColor = Color.parseColor("#00FF00");
    private int fillColor = Color.parseColor("#00FF00");
    private Paint outlinePaint;
    private Paint fillPaint;

    public BatteryView(Context context) {
        super(context);
        init(null);
    }

    public BatteryView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public BatteryView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(@Nullable AttributeSet attrs) {
        outlinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        outlinePaint.setStyle(Paint.Style.STROKE);
        outlinePaint.setStrokeWidth(1); // 原来是4，现在更细
        outlinePaint.setColor(outlineColor);

        fillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        fillPaint.setStyle(Paint.Style.FILL);
        fillPaint.setColor(fillColor);
    }

    public void setLevel(int level) {
        this.level = Math.max(0, Math.min(level, maxLevel));
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int w = getWidth();
        int h = getHeight();
        int batteryBodyW = (int)(w * 0.85);
        int batteryBodyH = (int)(h * 0.7);
        int batteryBodyL = 0;
        int batteryBodyT = (h - batteryBodyH) / 2;
        int batteryHeadW = w - batteryBodyW;
        int batteryHeadH = batteryBodyH / 2;
        int batteryHeadL = batteryBodyW;
        int batteryHeadT = batteryBodyT + batteryBodyH / 4;

        // Draw battery body
        canvas.drawRect(batteryBodyL, batteryBodyT, batteryBodyL + batteryBodyW, batteryBodyT + batteryBodyH, outlinePaint);
        // Draw battery head
        canvas.drawRect(batteryHeadL, batteryHeadT, batteryHeadL + batteryHeadW, batteryHeadT + batteryHeadH, outlinePaint);

        // Draw level
        int gap = 2; // 原来是6，改小
        int cellW = (batteryBodyW - gap * (maxLevel + 1)) / maxLevel;
        int cellH = batteryBodyH - gap * 2;
        android.util.Log.d("BatteryView", "onDraw: w=" + w + ", cellW=" + cellW + ", level=" + level);
        for (int i = 0; i < level; i++) {
            int left = batteryBodyL + gap + i * (cellW + gap);
            int top = batteryBodyT + gap;
            canvas.drawRect(left, top, left + cellW, top + cellH, fillPaint);
        }
    }
} 