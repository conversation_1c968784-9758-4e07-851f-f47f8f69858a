package com.gzcec.xrandroidclient.communication.messages.system;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;

/**
 * 连接状态消息
 */
public class ConnectionStatusMessage extends BaseMessage {
    private boolean isConnected;
    private String clientType;

    public ConnectionStatusMessage() {
        setType(MessageType.CONNECTION_STATUS);
    }

    // Getter和Setter方法
    public boolean isConnected() { return isConnected; }
    public void setConnected(boolean connected) { isConnected = connected; }
    
    public String getClientType() { return clientType; }
    public void setClientType(String clientType) { this.clientType = clientType; }
}
