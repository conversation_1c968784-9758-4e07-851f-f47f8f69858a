package com.gzcec.xrandroidclient.communication;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.gzcec.xrandroidclient.communication.client.MqttGameClient;
import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.messages.device.*;
import com.gzcec.xrandroidclient.communication.messages.game.*;
import com.gzcec.xrandroidclient.communication.messages.system.HeartbeatMessage;
import com.gzcec.xrandroidclient.communication.data.GameInfo;
import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.config.NetworkConfig;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 统一的MQTT通信管理器
 * 简化原有的多个管理器，提供统一的MQTT通信接口
 */
public class MqttCommunicationManager {
    private static final String TAG = "MqttCommunicationManager";
    private static MqttCommunicationManager instance;
    
    // 配置常量
    private static final long DEVICE_LIST_SYNC_INTERVAL = 60; // 60秒
    private static final long HEARTBEAT_INTERVAL = 60; // 60秒
    private static final long INITIAL_DELAY = 2; // 2秒
    
    // 核心组件
    private Context context;
    private MqttGameClient mqttClient;
    private NetworkConfig networkConfig;
    private Gson gson;
    private Handler mainHandler;
    private ScheduledExecutorService scheduler;
    private EnhancedConnectionManager enhancedConnectionManager;
    
    // 监听器管理
    private final List<GameControlListener> gameControlListeners = new CopyOnWriteArrayList<>();
    private final List<DeviceStatusListener> deviceStatusListeners = new CopyOnWriteArrayList<>();
    private final List<ConnectionListener> connectionListeners = new CopyOnWriteArrayList<>();
    
    // 定时任务
    private ScheduledFuture<?> deviceListSyncTask;
    private ScheduledFuture<?> heartbeatTask;
    
    // 状态管理
    private boolean isInitialized = false;
    private boolean isSyncEnabled = false;
    
    private MqttCommunicationManager() {
        mainHandler = new Handler(Looper.getMainLooper());
        scheduler = Executors.newScheduledThreadPool(2);
        gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSXXX")
                .create();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized MqttCommunicationManager getInstance() {
        if (instance == null) {
            instance = new MqttCommunicationManager();
        }
        return instance;
    }
    
    /**
     * 初始化通信管理器
     */
    public void initialize(Context context) {
        if (isInitialized) {
            Log.d(TAG, "通信管理器已初始化，跳过重复初始化");
            return;
        }
        
        this.context = context.getApplicationContext();
        this.networkConfig = new NetworkConfig(this.context);
        
        // 创建MQTT客户端
        String mqttHost = networkConfig.getMqttHost();
        int mqttPort = networkConfig.getMqttPort();
        
        Log.i(TAG, "初始化MQTT通信管理器: " + mqttHost + ":" + mqttPort);
        
        mqttClient = new MqttGameClient(this.context, mqttHost, mqttPort);
        
        // 设置内部监听器
        setupInternalListeners();

        // 初始化增强连接管理器
        setupEnhancedConnectionManager();

        isInitialized = true;
        Log.i(TAG, "MQTT通信管理器初始化完成");
    }
    
    /**
     * 设置内部监听器，处理消息分发
     */
    private void setupInternalListeners() {
        // 游戏控制监听器
        mqttClient.setGameControlListener(new MqttGameClient.GameControlListener() {
            @Override
            public void onGameListResponse(GameListResponseMessage response) {
                notifyGameControlListeners(listener -> listener.onGameListResponse(response));
            }
            
            @Override
            public void onGameStartResponse(GameStartResponseMessage response) {
                notifyGameControlListeners(listener -> listener.onGameStartResponse(response));
            }
            
            @Override
            public void onGameStopResponse(GameStopResponseMessage response) {
                notifyGameControlListeners(listener -> listener.onGameStopResponse(response));
            }
            
            @Override
            public void onGameProgressUpdate(GameProgressUpdateMessage update) {
                notifyGameControlListeners(listener -> listener.onGameProgressUpdate(update));
            }
        });
        
        // 设备状态监听器
        mqttClient.setDeviceStatusListener(new MqttGameClient.DeviceStatusListener() {
            @Override
            public void onDeviceListResponse(DeviceListResponseMessage response) {
                notifyDeviceStatusListeners(listener -> listener.onDeviceListResponse(response));
            }
            
            @Override
            public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update) {
                notifyDeviceStatusListeners(listener -> listener.onDeviceStatusUpdate(update));
            }
        });
        
        // 连接状态监听器
        mqttClient.setConnectionListener(new MqttGameClient.ConnectionListener() {
            @Override
            public void onConnected() {
                Log.i(TAG, "MQTT连接成功");
                notifyConnectionListeners(listener -> listener.onConnected());
                startSync();
            }
            
            @Override
            public void onDisconnected() {
                Log.w(TAG, "MQTT连接断开");
                notifyConnectionListeners(listener -> listener.onDisconnected());
                stopSync();
            }
            
            @Override
            public void onConnectionError(String error) {
                Log.e(TAG, "MQTT连接错误: " + error);
                notifyConnectionListeners(listener -> listener.onConnectionError(error));
            }
        });
    }

    /**
     * 设置增强连接管理器
     */
    private void setupEnhancedConnectionManager() {
        enhancedConnectionManager = EnhancedConnectionManager.getInstance();
        enhancedConnectionManager.initialize(context);

        // 启用增强连接管理（监听器会在EnhancedConnectionManager内部注册）
        enhancedConnectionManager.enable();
        Log.i(TAG, "增强连接管理器已启用");
    }

    /**
     * 连接到MQTT服务器
     */
    public void connect() {
        if (!isInitialized) {
            Log.e(TAG, "通信管理器未初始化，无法连接");
            return;
        }
        
        if (mqttClient.isConnected()) {
            Log.d(TAG, "MQTT已连接，跳过重复连接");
            return;
        }
        
        if (!networkConfig.isNetworkConnected()) {
            Log.e(TAG, "网络未连接，无法连接MQTT服务器");
            return;
        }
        
        Log.i(TAG, "连接到MQTT服务器: " + networkConfig.getMqttUrl());
        mqttClient.connect();
    }
    
    /**
     * 断开MQTT连接
     */
    public void disconnect() {
        stopSync();
        if (mqttClient != null) {
            mqttClient.disconnect();
        }
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * 强制重新发现服务器
     * 用于解决PC端后启动的问题
     */
    public void forceRediscoverServer() {
        if (enhancedConnectionManager != null) {
            Log.i(TAG, "手动触发服务器重新发现");
            enhancedConnectionManager.forceRediscover();
        }
    }
    
    // ========== 监听器管理 ==========
    
    public void addGameControlListener(GameControlListener listener) {
        if (listener != null && !gameControlListeners.contains(listener)) {
            gameControlListeners.add(listener);
            Log.d(TAG, "添加游戏控制监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    public void removeGameControlListener(GameControlListener listener) {
        if (gameControlListeners.remove(listener)) {
            Log.d(TAG, "移除游戏控制监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    public void addDeviceStatusListener(DeviceStatusListener listener) {
        if (listener != null && !deviceStatusListeners.contains(listener)) {
            deviceStatusListeners.add(listener);
            Log.d(TAG, "添加设备状态监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    public void removeDeviceStatusListener(DeviceStatusListener listener) {
        if (deviceStatusListeners.remove(listener)) {
            Log.d(TAG, "移除设备状态监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    public void addConnectionListener(ConnectionListener listener) {
        if (listener != null && !connectionListeners.contains(listener)) {
            connectionListeners.add(listener);
            Log.d(TAG, "添加连接状态监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    public void removeConnectionListener(ConnectionListener listener) {
        if (connectionListeners.remove(listener)) {
            Log.d(TAG, "移除连接状态监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    // ========== 监听器接口定义 ==========
    
    public interface GameControlListener {
        void onGameListResponse(GameListResponseMessage response);
        void onGameStartResponse(GameStartResponseMessage response);
        void onGameStopResponse(GameStopResponseMessage response);
        void onGameProgressUpdate(GameProgressUpdateMessage update);
    }
    
    public interface DeviceStatusListener {
        void onDeviceListResponse(DeviceListResponseMessage response);
        void onDeviceStatusUpdate(DeviceStatusUpdateMessage update);
    }
    
    public interface ConnectionListener {
        void onConnected();
        void onDisconnected();
        void onConnectionError(String error);
    }
    
    public interface ResponseCallback {
        void onResponse(BaseMessage response);
        void onTimeout();
    }

    // ========== 消息发送方法 ==========

    /**
     * 请求游戏列表
     */
    public void requestGameList(ResponseCallback callback) {
        requestGameList(false, callback);
    }

    public void requestGameList(boolean includeDisabled, ResponseCallback callback) {
        if (mqttClient != null) {
            // 适配器：将我们的回调转换为MqttGameClient的回调
            MqttGameClient.ResponseCallback mqttCallback = createMqttCallback(callback);
            mqttClient.requestGameList(includeDisabled, mqttCallback);
        }
    }

    /**
     * 启动游戏
     */
    public void startGame(GameInfo gameInfo, List<String> selectedDeviceIds, ResponseCallback callback) {
        if (mqttClient != null) {
            // 适配器：将我们的回调转换为MqttGameClient的回调
            MqttGameClient.ResponseCallback mqttCallback = createMqttCallback(callback);
            mqttClient.requestStartGame(gameInfo, selectedDeviceIds, mqttCallback);
        }
    }

    /**
     * 停止游戏
     */
    public void stopGame(GameInfo gameInfo, List<String> selectedDeviceIds, ResponseCallback callback) {
        if (mqttClient != null) {
            // 适配器：将我们的回调转换为MqttGameClient的回调
            MqttGameClient.ResponseCallback mqttCallback = createMqttCallback(callback);
            mqttClient.requestStopGame(gameInfo, selectedDeviceIds, mqttCallback);
        }
    }

    /**
     * 请求设备列表
     */
    public void requestDeviceList(ResponseCallback callback) {
        requestDeviceList(false, callback);
    }

    public void requestDeviceList(boolean onlineOnly, ResponseCallback callback) {
        if (mqttClient != null) {
            // 适配器：将我们的回调转换为MqttGameClient的回调
            MqttGameClient.ResponseCallback mqttCallback = createMqttCallback(callback);
            mqttClient.requestDeviceList(onlineOnly, mqttCallback);
        }
    }

    /**
     * 创建MQTT回调适配器
     */
    private MqttGameClient.ResponseCallback createMqttCallback(ResponseCallback callback) {
        if (callback == null) {
            return null;
        }

        return new MqttGameClient.ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                callback.onResponse(response);
            }

            @Override
            public void onTimeout() {
                callback.onTimeout();
            }
        };
    }

    // ========== 数据同步管理 ==========

    /**
     * 启动数据同步
     */
    private void startSync() {
        if (isSyncEnabled) {
            Log.d(TAG, "数据同步已启动，跳过重复启动");
            return;
        }

        Log.i(TAG, "启动数据同步");

        // 启动设备列表定时同步
        deviceListSyncTask = scheduler.scheduleWithFixedDelay(
            this::syncDeviceList,
            INITIAL_DELAY,
            DEVICE_LIST_SYNC_INTERVAL,
            TimeUnit.SECONDS
        );

        // 启动心跳检测
        heartbeatTask = scheduler.scheduleWithFixedDelay(
            this::sendHeartbeat,
            INITIAL_DELAY,
            HEARTBEAT_INTERVAL,
            TimeUnit.SECONDS
        );

        isSyncEnabled = true;
        Log.i(TAG, "数据同步启动完成");
    }

    /**
     * 停止数据同步
     */
    private void stopSync() {
        if (!isSyncEnabled) {
            return;
        }

        Log.i(TAG, "停止数据同步");

        if (deviceListSyncTask != null) {
            deviceListSyncTask.cancel(false);
        }

        if (heartbeatTask != null) {
            heartbeatTask.cancel(false);
        }

        isSyncEnabled = false;
        Log.i(TAG, "数据同步已停止");
    }

    /**
     * 同步设备列表
     */
    private void syncDeviceList() {
        if (!isConnected()) {
            Log.w(TAG, "MQTT未连接，跳过设备列表同步");
            return;
        }

        Log.d(TAG, "开始同步设备列表");
        requestDeviceList(false, new ResponseCallback() {
            @Override
            public void onResponse(BaseMessage response) {
                Log.d(TAG, "设备列表同步成功");
            }

            @Override
            public void onTimeout() {
                Log.w(TAG, "设备列表同步超时");
            }
        });
    }

    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        if (!isConnected()) {
            Log.w(TAG, "MQTT未连接，跳过心跳发送");
            return;
        }

        Log.d(TAG, "发送心跳");
        // 心跳逻辑可以通过mqttClient实现
    }

    // ========== 监听器通知方法 ==========

    private void notifyGameControlListeners(ListenerNotifier<GameControlListener> notifier) {
        for (GameControlListener listener : gameControlListeners) {
            try {
                notifier.notify(listener);
            } catch (Exception e) {
                Log.e(TAG, "通知游戏控制监听器失败: " + listener.getClass().getSimpleName(), e);
            }
        }
    }

    private void notifyDeviceStatusListeners(ListenerNotifier<DeviceStatusListener> notifier) {
        for (DeviceStatusListener listener : deviceStatusListeners) {
            try {
                notifier.notify(listener);
            } catch (Exception e) {
                Log.e(TAG, "通知设备状态监听器失败: " + listener.getClass().getSimpleName(), e);
            }
        }
    }

    private void notifyConnectionListeners(ListenerNotifier<ConnectionListener> notifier) {
        for (ConnectionListener listener : connectionListeners) {
            try {
                notifier.notify(listener);
            } catch (Exception e) {
                Log.e(TAG, "通知连接状态监听器失败: " + listener.getClass().getSimpleName(), e);
            }
        }
    }

    @FunctionalInterface
    private interface ListenerNotifier<T> {
        void notify(T listener);
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        stopSync();

        // 清理增强连接管理器
        if (enhancedConnectionManager != null) {
            enhancedConnectionManager.cleanup();
        }

        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }

        gameControlListeners.clear();
        deviceStatusListeners.clear();
        connectionListeners.clear();

        if (mqttClient != null) {
            mqttClient.disconnect();
        }

        Log.i(TAG, "MQTT通信管理器资源清理完成");
    }
}
