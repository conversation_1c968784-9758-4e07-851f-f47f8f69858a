<resources>
    <!-- 统一按钮样式模板 -->
    <style name="UnifiedButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:layout_height">44dp</item>
    </style>

    <!-- 游戏选择RadioButton样式 -->
    <style name="GameSelectionRadioButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:padding">8dp</item>
        <item name="android:button">@drawable/radio_button_small</item>
        <item name="android:drawablePadding">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
</resources>