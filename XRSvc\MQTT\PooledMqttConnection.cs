using System;
using MQTTnet.Client;

namespace XRSvc.MQTT
{
    /// <summary>
    /// 池化MQTT连接包装类
    /// 包装IMqttClient，添加连接池管理所需的元数据
    /// </summary>
    public class PooledMqttConnection
    {
        private readonly IMqttClient _client;
        private readonly string _clientId;
        private readonly DateTime _createdTime;
        private DateTime _lastUsedTime;
        private volatile bool _isActive;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="client">MQTT客户端</param>
        /// <param name="clientId">客户端ID</param>
        public PooledMqttConnection(IMqttClient client, string clientId)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _clientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
            _createdTime = DateTime.Now;
            _lastUsedTime = DateTime.Now;
            _isActive = false;
        }
        
        /// <summary>
        /// 获取MQTT客户端
        /// </summary>
        public IMqttClient Client
        {
            get
            {
                UpdateLastUsedTime();
                return _client;
            }
        }
        
        /// <summary>
        /// 获取客户端ID
        /// </summary>
        public string ClientId => _clientId;
        
        /// <summary>
        /// 获取创建时间
        /// </summary>
        public DateTime CreatedTime => _createdTime;
        
        /// <summary>
        /// 获取最后使用时间
        /// </summary>
        public DateTime LastUsedTime => _lastUsedTime;
        
        /// <summary>
        /// 更新最后使用时间
        /// </summary>
        public void UpdateLastUsedTime()
        {
            _lastUsedTime = DateTime.Now;
        }
        
        /// <summary>
        /// 标记为活跃状态
        /// </summary>
        public void MarkAsActive()
        {
            _isActive = true;
            UpdateLastUsedTime();
        }
        
        /// <summary>
        /// 标记为空闲状态
        /// </summary>
        public void MarkAsIdle()
        {
            _isActive = false;
            UpdateLastUsedTime();
        }
        
        /// <summary>
        /// 检查是否为活跃状态
        /// </summary>
        public bool IsActive => _isActive;
        
        /// <summary>
        /// 获取连接存活时间
        /// </summary>
        public TimeSpan AliveTime => DateTime.Now - _createdTime;
        
        /// <summary>
        /// 获取连接空闲时间
        /// </summary>
        public TimeSpan IdleTime => DateTime.Now - _lastUsedTime;
        
        /// <summary>
        /// 字符串表示
        /// </summary>
        public override string ToString()
        {
            return $"PooledMqttConnection{{clientId='{_clientId}', active={_isActive}, aliveTime={AliveTime.TotalMilliseconds}ms, idleTime={IdleTime.TotalMilliseconds}ms}}";
        }
        
        /// <summary>
        /// 相等性比较
        /// </summary>
        public override bool Equals(object obj)
        {
            if (this == obj) return true;
            if (obj == null || GetType() != obj.GetType()) return false;
            var that = (PooledMqttConnection)obj;
            return _clientId.Equals(that._clientId);
        }
        
        /// <summary>
        /// 获取哈希码
        /// </summary>
        public override int GetHashCode()
        {
            return _clientId.GetHashCode();
        }
    }
}
