<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#ffffff">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:background="@color/purple_500"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="←"
            android:textColor="#ffffff"
            android:textSize="20sp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="日志查看器"
            android:textColor="#ffffff"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_refresh"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="⟳"
            android:textColor="#ffffff"
            android:textSize="18sp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_menu"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="⋮"
            android:textColor="#ffffff"
            android:textSize="18sp"
            android:background="?android:attr/selectableItemBackgroundBorderless" />

    </LinearLayout>

    <!-- 搜索框 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#f5f5f5">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="搜索: "
            android:layout_gravity="center_vertical"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/et_search"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="输入关键词搜索日志..."
            android:textSize="14sp"
            android:padding="8dp"
            android:background="@android:drawable/edit_text"
            android:maxLines="1"
            android:imeOptions="actionSearch" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#e0e0e0" />

    <!-- 日志内容 -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp">

        <TextView
            android:id="@+id/tv_log_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="正在加载日志..."
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:textIsSelectable="true"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

</LinearLayout>
