﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using XRSvc.Utils;

namespace XRSvc.ViewModels
{
    public abstract class BaseViewModel : INotifyPropertyChanged
    {
        private bool _isLoading;
        private readonly UIThreadScheduler _uiScheduler;

        protected BaseViewModel()
        {
            _uiScheduler = UIThreadScheduler.Instance;
        }

        // 是否处于加载状态
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        // 属性变更事件
        public event PropertyChangedEventHandler PropertyChanged;

        // 属性变更通知方法
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // 在UI线程上执行指定操作（使用优化的调度器）
        protected void RunOnUIThread(Action action)
        {
            _uiScheduler.InvokeOnUIThread(action);
        }

        // 在UI线程上异步执行指定操作（使用优化的调度器）
        protected async Task RunOnUIThreadAsync(Action action)
        {
            await _uiScheduler.InvokeOnUIThreadAsync(action);
        }

        // 在UI线程上异步执行指定操作并返回结果
        protected async Task<T> RunOnUIThreadAsync<T>(Func<T> func)
        {
            return await _uiScheduler.InvokeOnUIThreadAsync(func);
        }

        // 批量执行UI操作（用于频繁更新）
        protected void BatchRunOnUIThread(Action action, string actionId = null)
        {
            _uiScheduler.BatchInvokeOnUIThread(action, actionId);
        }

        // 立即刷新所有批量操作
        protected async Task FlushUIBatchAsync()
        {
            await _uiScheduler.FlushBatchAsync();
        }

        // 兼容旧版本的方法（已废弃，建议使用新方法）
        [Obsolete("请使用 RunOnUIThreadAsync(Action action) 替代")]
        protected async Task RunOnUIThreadAsync(Func<Task> action)
        {
            if (_uiScheduler.IsOnUIThread)
            {
                await action();
            }
            else
            {
                await _uiScheduler.InvokeOnUIThreadAsync(async () => await action());
            }
        }
    }
}