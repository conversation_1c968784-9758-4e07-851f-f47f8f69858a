package com.gzcec.xrandroidclient.communication.messages.game;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;

/**
 * 游戏列表请求消息
 */
public class GameListRequestMessage extends BaseMessage {
    private String requestId;
    private boolean includeDisabled; // 是否包含禁用的游戏
    private String category; // 游戏分类过滤
    
    public GameListRequestMessage() {
        setType(MessageType.GAME_LIST_REQUEST);
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public boolean isIncludeDisabled() {
        return includeDisabled;
    }
    
    public void setIncludeDisabled(boolean includeDisabled) {
        this.includeDisabled = includeDisabled;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
}
