﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XRSvc.DataPack
{
 

    internal class DeviceInfo
    {
        ///<summary>
        ///设备ID
        /// </summary>
        public int ID;

        ///<summary>
        ///设备唯一序列号
        /// </summary>
        public string SerialNumber = string.Empty; // 默认值，避免为 null

        ///<summary>
        ///设备IP
        /// </summary>
        public string IP;

        ///<summary>
        ///设备名称
        ///</summary>
        public string Name;

        ///<summary>
        ///设备类型
        /// </summary>
        public DeviceType Type = DeviceType.SERVER; // 默认值

        ///<summary>
        ///是否启用
        /// </summary>
        public bool IsEnabled;

        ///<summary>
        ///设备在线状态
        /// </summary>
        public bool IsOnline;

    }
}
