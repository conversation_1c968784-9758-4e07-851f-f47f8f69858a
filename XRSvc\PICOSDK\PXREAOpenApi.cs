using System;
using System.Runtime.InteropServices;

/// <summary>
/// PICO XR设备开放API接口类，提供与PICO设备SDK的互操作功能
/// </summary>
public static class PXREAOpenApi
{
    #region PXREAClientSDK
    
    /// <summary>
    /// SDK回调委托，用于接收各种设备事件通知
    /// </summary>
    /// <param name="context">上下文指针</param>
    /// <param name="type">回调类型</param>
    /// <param name="status">状态码</param>
    /// <param name="userData">用户数据指针</param>
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]  
    public delegate void SDKCallBack(IntPtr context, PXREAOpenApiCallbackType type, int status, IntPtr userData);
    
    /// <summary>
    /// 初始化PICO SDK
    /// </summary>
    /// <param name="context">上下文指针</param>
    /// <param name="callBack">回调函数</param>
    /// <param name="mask">事件掩码</param>
    /// <returns>初始化结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int Init(IntPtr context, SDKCallBack callBack, uint mask);
    
    /// <summary>
    /// 反初始化PICO SDK
    /// </summary>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern void Deinit();
    
    /// <summary>
    /// 获取设备电池信息
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int GetBattery(IntPtr devID);

    /// <summary>
    /// 获取当前运行的应用
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int GetCurrentApp(IntPtr devID);

    /// <summary>
    /// 锁定设备
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int LockDevice(IntPtr devID);

    /// <summary>
    /// 解锁设备
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int UnlockDevice(IntPtr devID);

    /// <summary>
    /// 启动应用
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <param name="appName">应用名称指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int StartPlayApp(IntPtr devID, IntPtr appName);
    
    /// <summary>
    /// 停止应用
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int StopPlayApp(IntPtr devID, IntPtr appName);

    /// <summary>
    /// 开始监控设备
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int StartMonitor(IntPtr devID);

    /// <summary>
    /// 停止监控设备
    /// </summary>
    /// <param name="devID">设备ID指针</param>
    /// <returns>操作结果</returns>
    [DllImport("C:\\Program Files (x86)\\PICO Business Suite\\SDK\\clientdll\\64\\PXREAClientSDK.dll")]
    public static extern int StopMonitor(IntPtr devID);

    #endregion
}

/// <summary>
/// PICO SDK回调类型枚举，定义各种设备事件类型
/// </summary>
public enum PXREAOpenApiCallbackType: uint
{
    /// <summary>
    /// 帧捕获开始，用于VR直播
    /// </summary>
    PXREAFrameCaptureStart = 1,
    
    /// <summary>
    /// 帧捕获停止，用于VR直播
    /// </summary>
    PXREAFrameCaptureStop = 1 << 1,
    
    /// <summary>
    /// 服务器连接
    /// </summary>
    PXREAServerConnect = 1 << 2,
    
    /// <summary>
    /// 服务器断开
    /// </summary>
    PXREAServerDisconnect = 1 << 3,
    
    /// <summary>
    /// 设备发现
    /// </summary>
    PXREADeviceFind = 1 << 4,
    
    /// <summary>
    /// 设备丢失
    /// </summary>
    PXREADeviceMissing = 1 << 5,
    
    /// <summary>
    /// 设备电池状态，范围0-100
    /// </summary>
    PXREADeviceBattery = 1 << 6,
    
    /// <summary>
    /// 设备锁定
    /// </summary>
    PXREADeviceLock = 1 << 7,
    
    /// <summary>
    /// 设备解锁
    /// </summary>
    PXREADeviceUnlock = 1 << 8,
    
    /// <summary>
    /// 设备连接
    /// </summary>
    PXREADeviceConnect = 1 << 9,
    
    /// <summary>
    /// 设备传感器数据
    /// </summary>
    PXREADeviceSensor = 1 << 10,
    
    /// <summary>
    /// 设备关闭
    /// </summary>
    PXREADeviceShutdown = 1 << 11,
    
    /// <summary>
    /// 设备从休眠状态恢复
    /// </summary>
    PXREADeviceResume = 1 << 12,
    
    /// <summary>
    /// 监控参数变化
    /// </summary>
    PXREADeviceMonitorParameter = 1 << 13,
    
    /// <summary>
    /// 监控画面帧，YUV420P格式
    /// </summary>
    PXREADeviceMonitor = 1 << 14,
    
    /// <summary>
    /// 设备型号
    /// </summary>
    PXREADeviceModel = 1<< 15,
    
    /// <summary>
    /// 当前应用信息
    /// </summary>
    PXREACurrentApplication = 1 << 16,
    
    /// <summary>
    /// 控制器电池状态
    /// </summary>
    PXREAControllerBattery = 1 << 17,
    
    /// <summary>
    /// 屏幕状态
    /// </summary>
    PXREAScreenState = 1 << 18,
    
    /// <summary>
    /// 全掩码，接收所有事件
    /// </summary>
    PXREAFullMask = 0xffffffff
}

/// <summary>
/// 像素格式枚举
/// </summary>
public enum PXREAOpenApiPixFormat : int
{
    /// <summary>
    /// YUV420P格式
    /// </summary>
    Format_YUV420P = 18
}

/// <summary>
/// 设备电池信息结构体
/// </summary>
[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
public struct PXREADevBattery
{
    /// <summary>
    /// 设备序列号
    /// </summary>
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
    public string devID;
    
    /// <summary>
    /// 设备电池电量，范围0-100
    /// </summary>
    public uint battery;
}

/// <summary>
/// 监控帧数据结构体
/// </summary>
[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
public struct PXREAFrameBlob
{
    /// <summary>
    /// 设备序列号
    /// </summary>
    [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
    public string devID;
    
    /// <summary>
    /// 帧数据地址
    /// </summary>
    public IntPtr addr;
    
    /// <summary>
    /// 帧数据大小
    /// </summary>
    public uint size;
    
    /// <summary>
    /// 帧宽度
    /// </summary>
    public uint width;
    
    /// <summary>
    /// 帧高度
    /// </summary>
    public uint height;
    
    /// <summary>
    /// 每行字节数
    /// </summary>
    public uint bytesPerLine;
    
    /// <summary>
    /// 像素格式
    /// </summary>
    public int pixFormat;
}
