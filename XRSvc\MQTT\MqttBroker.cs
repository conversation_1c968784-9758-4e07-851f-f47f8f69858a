using MQTTnet;
using MQTTnet.Server;
using System;
using System.Threading.Tasks;
using Jskj.AppLog;
using System.Text;

namespace Jskj.XRSvc.MQTT
{
    public class MqttBroker
    {
        private MqttServer mqttServer;

        public async Task StartAsync(int port = 1883)
        {
            var options = new MqttServerOptionsBuilder()
                .WithDefaultEndpoint()
                .WithDefaultEndpointPort(port)
                .Build();

            mqttServer = new MqttFactory().CreateMqttServer(options);

            mqttServer.ValidatingConnectionAsync += e =>
            {
                // 可在此处添加连接验证逻辑
                return Task.CompletedTask;
            };

            // 拦截并打印所有接收到的消息
            mqttServer.InterceptingPublishAsync += e =>
            {
                var topic = e.ApplicationMessage.Topic;
                var payloadSegment = e.ApplicationMessage.PayloadSegment;
                string payload = (payloadSegment.Array == null || payloadSegment.Count == 0)
                    ? string.Empty
                    : Encoding.UTF8.GetString(payloadSegment.Array, payloadSegment.Offset, payloadSegment.Count);
                var clientId = e.ClientId;

                // 对设备相关的大消息进行简化日志输出
                if (topic == "xr_system/device_management/status_update" ||
                    topic == "xr_system/device_management/list_response")
                {
                    Log.Write(Level.INFO, $"[Broker] 收到消息: 主题={topic}, 内容长度={payload.Length}字符, 来自客户端: {clientId}");
                }
                else
                {
                    Log.Write(Level.INFO, $"[Broker] 收到消息: 主题={topic}, 内容={payload}, 来自客户端: {clientId}");
                }
                return Task.CompletedTask;
            };

            // 拦截并打印所有客户端订阅的主题
            mqttServer.InterceptingSubscriptionAsync += e =>
            {
                var clientId = e.ClientId;
                var topicFilter = e.TopicFilter?.Topic ?? "";
                Log.Write(Level.INFO, $"[Broker] 客户端 '{clientId}' 订阅了主题: {topicFilter}");
                return Task.CompletedTask;
            };

            await mqttServer.StartAsync();
            Log.Write(Level.INFO, $"MQTT Broker started on port {port}");
        }

        public async Task StopAsync()
        {
            if (mqttServer != null)
            {
                await mqttServer.StopAsync();
                Log.Write(Level.INFO, "MQTT Broker stopped.");
            }
        }
    }
} 