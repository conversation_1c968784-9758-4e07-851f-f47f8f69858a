﻿<Window x:Class="XRSvc.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:XRSvc.CustomControl"
        xmlns:localConverters="clr-namespace:XRSvc.Utils"
        mc:Ignorable="d"
        Title="XR播放系统 - 店铺端" Height="730" Width="1045" WindowState="Normal" WindowStartupLocation="CenterScreen" ResizeMode="NoResize" AllowsTransparency="True" WindowStyle="None" Background="{x:Null}">
    
    <!--资源,只用于设计，运行时使用程序入口统一的资源-->
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/style.xaml"/>
                <ResourceDictionary Source="/Resources/zh-cn.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <localConverters:OnlineStateTextConverter x:Key="OnlineStateTextConverter" />
            <localConverters:OnlineStateColorConverter x:Key="OnlineStateColorConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <Grid x:Name="Grid_Main" Visibility="Visible">
        <!--标题栏-->
        <Grid x:Name="TitleBar" VerticalAlignment="Top" Background="#2D2D30">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,0,0" Height="35">

                <!--最小化按钮-->
                <Grid x:Name="Btn_WinMin" Margin="0,0,0,0" Width="35" Height="35" Style="{DynamicResource TitleBarStyle}" MouseLeftButtonDown="Btn_WinMin_MouseLeftButtonDown">
                    <Path Data="M 0,0 L 12,0 L 12,1 L 0,1 z" Fill="White" HorizontalAlignment="Center" VerticalAlignment="Center" SnapsToDevicePixels="True"/>
                </Grid>

                <!--窗口最大化和向下还原按钮-->
                <Grid>
                    <!--窗口最大化按钮-->
                    <Grid x:Name="Btn_WinMax" Margin="5,0,0,0" Width="35" Height="35" Style="{DynamicResource TitleBarStyle}" MouseLeftButtonDown="Btn_WinMax_MouseLeftButtonDown" Visibility="Visible">
                        <Rectangle Width="12" Height="12" StrokeThickness="0.7" Stroke="White" SnapsToDevicePixels="True" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Grid>

                    <!--向下还原按钮-->
                    <Grid x:Name="Btn_WinRecover" Visibility="Collapsed" Margin="5,0,0,0" Width="35" Height="35" Style="{DynamicResource TitleBarStyle}" MouseLeftButtonDown="Btn_WinRecover_MouseLeftButtonDown">
                        <Rectangle Width="9" Height="9" StrokeThickness="0.7" Stroke="White" SnapsToDevicePixels="True" HorizontalAlignment="Left" VerticalAlignment="top" Margin="14,15,0,0"/>
                        <Rectangle Width="9" Height="9" StrokeThickness="0.7" Stroke="White" SnapsToDevicePixels="True" HorizontalAlignment="Left" VerticalAlignment="top" Margin="12,17,0,0" Fill="{Binding ElementName=TitleBar, Path=Background}"/>
                    </Grid>
                </Grid>

                <!--关闭按钮-->
                <Grid x:Name="Btn_WinClose" Width="35" Height="35" Margin="5,0,0,0" Style="{DynamicResource TitleBarStyle}" MouseLeftButtonDown="Btn_WinClose_MouseLeftButtonDown">
                    <Grid Width="12" Height="12" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Line X1="0" Y1="0" X2="11" Y2="11" Style="{DynamicResource LineStyle}"/>
                        <Line X1="11" Y1="0" X2="0" Y2="11" Style="{DynamicResource LineStyle}"/>
                    </Grid>
                </Grid>
            </StackPanel>
        </Grid>

        <!--窗口主要内容-->
        <Grid Margin="0,35,0,0" Background="#F0F0F0">
            <TabControl>
                <!--服务器: 游戏-->
                <TabItem Header="游戏" Width="50">

                    <!--TabControl: 游戏服务器-->
                    <Grid x:Name="Grid_Content" >
                        <TabControl Margin="5,5,5,0" >

                            <!--服务器1-->
                            <TabItem Header="服务器1">
                                <StackPanel Orientation="Vertical" >

                                    <!--游戏列表-->
                                    <GroupBox Header="游戏列表" Height="60" Margin="10,5,10,0" Padding="5">
                                        <ItemsControl x:Name="GameListItemsControl" ItemsSource="{Binding GameViewModel.Source}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" ItemWidth="100"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <local:GameStyle/>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </GroupBox>

                                    <!--游戏服务器-->
                                    <GroupBox Header="游戏服务器" Height="60" Margin="10,5,10,0" Padding="10,0,0,0">
                                        <local:GameServerStyle/>
                                    </GroupBox>

                                    <!--设备列表-->
                                    <TabControl Height="430" Background="#F9F9F9" VerticalAlignment="Top" Margin="10,10,10,0">
                                        <TabItem Header="设备列表1">
                                            <ScrollViewer>
                                                <ItemsControl ItemsSource="{Binding DeviceViewModel.Devices80View, RelativeSource={RelativeSource AncestorType=Window}}">
                                                    <ItemsControl.ItemsPanel>
                                                        <ItemsPanelTemplate>
                                                            <WrapPanel Orientation="Horizontal" />
                                                        </ItemsPanelTemplate>
                                                    </ItemsControl.ItemsPanel>
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <local:XRClientStyle Margin="10,10,0,0" />
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </ScrollViewer>
                                        </TabItem>
                                        <TabItem Header="设备列表2">
                                            <ScrollViewer>
                                                <ItemsControl ItemsSource="{Binding DeviceViewModel.Devices81View, RelativeSource={RelativeSource AncestorType=Window}}">
                                                    <ItemsControl.ItemsPanel>
                                                        <ItemsPanelTemplate>
                                                            <WrapPanel Orientation="Horizontal" />
                                                        </ItemsPanelTemplate>
                                                    </ItemsControl.ItemsPanel>
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <local:XRClientStyle Margin="10,10,0,0" />
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </ScrollViewer>
                                        </TabItem>
                                    </TabControl>

                                    <StackPanel Orientation="Horizontal" Margin="10,20,10,0">
                                        <Button x:Name="Btn_SelectAll" Content="全选" Width="50" Margin="0,0,10,0" Background="#FDFDFD" Click="Btn_SelectAll_Click"/>
                                        <Button x:Name="Btn_UnselectAll" Content="全不选" Width="50" Margin="30,0,0,0" Background="#FDFDFD" Click="Btn_UnselectAll_Click"/>
                                        <Button x:Name="Btn_StartGame" Content="启动游戏" Width="110" Height="35" Margin="30,0,0,0" Background="#FDFDFD" Click="Btn_StartGame_Click"/>
                                        <Button x:Name="Btn_StartGameSession" Content="开始游戏" Width="110" Height="35" Margin="30,0,0,0" Background="#FDFDFD" Click="Btn_StartGameSession_Click"/>
                                        <Button x:Name="Btn_EndGame" Content="结束游戏" Width="110" Height="35" Margin="30,0,0,0" Background="#FDFDFD" Click="Btn_EndGame_Click"/>
                                    </StackPanel>

                                    <!-- 游戏选择下拉框 -->
                                    <StackPanel Orientation="Horizontal" Margin="10,10,10,0">
                                        <TextBlock Text="当前选择游戏：" VerticalAlignment="Center"/>
                                        <ComboBox Width="200"
                                                  ItemsSource="{Binding GameViewModel.Source}"
                                                  DisplayMemberPath="Name"
                                                  SelectedValuePath="ID"
                                                  SelectedValue="{Binding SelectedGameId, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
                                    </StackPanel>

                                </StackPanel>
                            </TabItem>


                        </TabControl>

                    </Grid>
                </TabItem>

                <!--TabItem: 设备-->
                <TabItem Header="设备" Width="50">
                    <Grid Background="#F9F9F9">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <GroupBox Header="设备管理服务端" Margin="10,10,10,0" Padding="5" Grid.Row="0">
                            <StackPanel>
                                <ItemsControl ItemsSource="{Binding DeviceServerViewModel.Source}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" ItemWidth="150"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <local:DeviceServerStyle 
                                                ServerName="{Binding Name}"
                                                ServerTag="{Binding Tag}"
                                                StateText="{Binding IsOnline, Converter={StaticResource OnlineStateTextConverter}}"
                                                StateColor="{Binding IsOnline, Converter={StaticResource OnlineStateColorConverter}}"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </GroupBox>
                        <GroupBox Header="设备列表" Margin="10,10,10,10" Padding="5" Grid.Row="2" DataContext="{Binding DeviceViewModel, RelativeSource={RelativeSource AncestorType=Window}}">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <ItemsControl ItemsSource="{Binding Devices8081View}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel Orientation="Horizontal" Width="1000" HorizontalAlignment="Left" VerticalAlignment="Top">
                                                <WrapPanel.Resources>
                                                    <Style TargetType="local:DeviceStyle">
                                                        <Setter Property="Margin" Value="10,10,0,0"/>
                                                        <Setter Property="Width" Value="180"/>
                                                        <Setter Property="Height" Value="90"/>
                                                    </Style>
                                                </WrapPanel.Resources>
                                            </WrapPanel>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <local:DeviceStyle/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </GroupBox>
                    </Grid>
                </TabItem>

                <!--TabItem: 日志-->
                <TabItem Header="日志" Width="50">
                    <Grid Background="#F9F9F9">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="10">
                            <ItemsControl ItemsSource="{Binding LogMessages}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}" FontSize="14" Foreground="Black"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </TabItem>

                <!--TabItem: 设置-->
                <TabItem Header="设置">
                    <Grid Background="#F9F9F9">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="10">
                            <ItemsControl ItemsSource="{Binding GameViewModel.Source}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <TextBlock Text="{Binding Name}" Width="200" VerticalAlignment="Center" />
                                            <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" Width="200" Margin="10,0,10,0" />
                                            <Button Content="保存" Width="80" Margin="10,0,0,0"
                                                    Command="{Binding DataContext.GameViewModel.SaveGameNameCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                    CommandParameter="{Binding}" />
                                        </StackPanel>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </TabItem>
            </TabControl>
        </Grid>

    </Grid>
</Window>
