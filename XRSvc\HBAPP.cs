﻿using Jskj.AppLog;
using Jskj.Win32Api;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Windows;
using System.Windows.Forms;
using System.Drawing;
using XRSvc.DataPack;
using XRSvc.Utils;
using Application = System.Windows.Application;
using Point = System.Drawing.Point;
using Jskj.XRSvc.Config; // Added namespace for GameInfo
using Jskj.XRSvc.MQTT;
using System.Threading.Tasks;

namespace XRSvc
{
    public class HBAPP : System.Windows.Application, IDisposable
    {

        #region Field

        /// <summary>
        /// 参数类（配置）
        /// </summary>
        private Setting setting;

        private List<GameInfo> GameList;
        private List<DeviceInfo> DeviceList;
        private List<DeviceServerInfo> DeviceServerInfoList;


        //private XRServices _XrSvc;

        /// <summary>
        /// 本地信号量（用于单实例限制）
        /// </summary>
        private static bool CreatedNew;

        /// <summary>
        /// 限制某一时间访问某一资源的线程数（单实例信号量）
        /// </summary>
        private static Semaphore SingleInstanceWatcher;

        /// <summary>
        /// 主窗口
        /// </summary>
        private MainWindow MainWin;

        /// <summary>
        /// 托盘图标
        /// </summary>
        private NotifyIcon NotifyIcon;

        private MqttBroker mqttBroker;
        private MqttClientService mqttClientService;

        #endregion

        #region Property

        /// <summary>
        /// 与爱奇艺通讯的UDP服务
        /// </summary>
        //private XRServices XrSvc
        //{
        //    get
        //    {
        //        if (_XrSvc == null)
        //        {
        //            _XrSvc = new XRServices(this.Setting, this.GameList, this.HMDeviceList);
        //        }
        //        return _XrSvc;
        //    }
        //}

        #endregion

        #region Method

        /// <summary>
        /// 获取本地网络IP地址
        /// </summary>
        /// <returns>本地IP地址，如果获取失败返回127.0.0.1</returns>
        private string GetLocalIpAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        string ipAddress = ip.ToString();
                        // 排除回环地址和APIPA地址
                        if (!ipAddress.StartsWith("127.") && !ipAddress.StartsWith("169.254."))
                        {
                            return ipAddress;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"获取本地IP地址失败: {ex.Message}");
            }

            // 如果获取失败，返回默认地址
            Log.Write(Level.INFO, "无法获取本地IP地址，使用默认地址 127.0.0.1");
            return "127.0.0.1";
        }

        /// <summary>
        /// 主程序入口，初始化配置、托盘、主窗口等
        /// </summary>
        /// <param name="args">命令行参数</param>
        public void RunMain(string[] args)
        {
            //检测是否以参数启动
            //if (args.Length == 0)
            //{
            //    Log.Write(Level.DEBUG, $"Cmd Line Args is Empty...");
            //    Environment.Exit(-2);//退出程序
            //    return;
            //}

            //只允许启动一个实例
            SingleInstanceWatcher = new Semaphore(0, 1, Assembly.GetExecutingAssembly().GetName().Name, out CreatedNew);
            if (!CreatedNew)
            {
                Environment.Exit(-2);//退出程序
                return;
            }

            //以管理员权限启动
            //if (!IsRunAdministrator())
            //{
            //    RunAsAdmin(string.Join(" ", args));
            //    Environment.Exit(-2);//退出程序
            //}

            Log.Write(Level.DEBUG, "Application Startup....");

            // 启动MQTT服务器
            mqttBroker = new MqttBroker();
            Task.Run(async () => await mqttBroker.StartAsync(1883));

            // 获取本地网络IP地址
            string localIpAddress = GetLocalIpAddress();
            Log.Write(Level.INFO, $"本地IP地址: {localIpAddress}");

            //注册全局异常
            RegisterUnHandleExcettion();

            //设置进程优先级别
            Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;

            // 读取配置文件，如果不存在则创建默认配置文件
            setting = ReadOrCreateConfigFile();


            //this.XrSvc.Start();

            //创建任务栏图标
            CreateNotifyIcon();



            // 分别赋值给字段
            this.GameList = setting.GameList;
            this.DeviceList = setting.DeviceList;
            this.DeviceServerInfoList = setting.DeviceServers;

            // 用字段传递给 MainWindow，并传递上次选择的游戏和设备服务器
            this.MainWin = new MainWindow(this.GameList, this.DeviceList, this.DeviceServerInfoList, setting.LastSelectedGameId, setting.LastSelectedDeviceServerTag);
            this.MainWin.Closing += MainWin_Closing;

            // 重新创建MQTT客户端服务，传递DeviceViewModel和GameViewModel
            mqttClientService = new MqttClientService(localIpAddress, 1883,
                this.MainWin.GameLaunchService, this.MainWin.DeviceViewModel, this.MainWin.GameViewModel);

            // 设置DeviceViewModel的MQTT客户端引用，用于设备状态变化时的实时广播
            this.MainWin.DeviceViewModel.SetMqttClient(mqttClientService);

            Task.Run(async () => {
                await mqttClientService.ConnectAsync();
                await mqttClientService.SubscribeAsync("test/#");
            });


            //启动WPF应用程序
            this.Run(this.MainWin);

            
        }

        #endregion

        #region 配置文件相关

        /// <summary>
        /// 读取配置文件，如果不存在则创建默认配置文件
        /// </summary>
        /// <returns>配置对象</returns>
        private Jskj.XRSvc.Config.Setting ReadOrCreateConfigFile()
        {
            string configPath = "Config/Setting.json";
            
            try
            {
                // 确保Config目录存在
                string configDir = Path.GetDirectoryName(configPath);
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                // 尝试读取配置文件
                var setting = JsonFileHelper.ReadFromJsonFileOrDefault<Jskj.XRSvc.Config.Setting>(configPath, null);
                
                if (setting != null)
                {
                    Log.Write(Level.DEBUG, "读取现有配置文件...");
                    return setting;
                }
                else
                {
                    Log.Write(Level.DEBUG, "配置文件不存在或读取失败，创建默认配置文件...");
                    
                    // 创建默认配置
                    var defaultSetting = new Jskj.XRSvc.Config.Setting();
                    defaultSetting.InitDefault();
                    
                    // 保存默认配置文件
                    JsonFileHelper.WriteToJsonFile(configPath, defaultSetting);
                    
                    Log.Write(Level.DEBUG, "默认配置文件创建成功");
                    return defaultSetting;
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"配置文件操作失败: {ex.Message}");
                
                // 如果出现异常，返回默认配置
                var fallbackSetting = new Jskj.XRSvc.Config.Setting();
                fallbackSetting.InitDefault();
                return fallbackSetting;
            }
        }

        #endregion

        #region 启动权限相关

        /// <summary>
        /// 判断当前进程是否以管理员权限运行
        /// </summary>
        public static bool IsRunAdministrator()
        {
            System.Security.Principal.WindowsIdentity identity = System.Security.Principal.WindowsIdentity.GetCurrent();
            System.Security.Principal.WindowsPrincipal principal = new System.Security.Principal.WindowsPrincipal(identity);
            return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
        }

        /// <summary>
        /// 以管理员权限重新启动程序
        /// </summary>
        public static void RunAsAdmin(string args)
        {
            //创建启动对象
            System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
            startInfo.UseShellExecute = true;
            startInfo.WorkingDirectory = Environment.CurrentDirectory;
            startInfo.FileName = Assembly.GetExecutingAssembly().Location;
            startInfo.Arguments = args;

            //设置启动动作,确保以管理员身份运行
            startInfo.Verb = "runas";
            try
            {
                System.Diagnostics.Process.Start(startInfo);
            }
            catch
            {
                return;
            }
        }

        #endregion

        #region 全局异常处理

        /// <summary>
        /// 注册全局未处理异常
        /// </summary>
        public void RegisterUnHandleExcettion()
        {
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        /// <summary>
        /// WPF全局未处理异常回调
        /// </summary>
        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                e.Handled = true;
                Log.Write(Level.ERROR, e.Exception);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
            }
        }

        /// <summary>
        /// AppDomain全局未处理异常回调
        /// </summary>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception exception)
                {
                    Log.Write(Level.ERROR, exception);
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
            }
        }

        #endregion

        #region Handle NotifyIcon Part

        /// <summary>
        /// 创建托盘图标及菜单
        /// </summary>
        private void CreateNotifyIcon()
        {
            // 托盘
            this.NotifyIcon = new NotifyIcon
            {
                Text = "Jskj.GameEngine" + System.Reflection.Assembly.GetExecutingAssembly().GetName().Version,
                Icon = Icon.ExtractAssociatedIcon(System.Windows.Forms.Application.ExecutablePath),
                Visible = true
            };


            // 注册【退出程序】的鼠标点击事件
            ToolStripMenuItem QuitItem = new ToolStripMenuItem("退出");
            QuitItem.Click += new EventHandler(QuitAppMenuItem_Click);

            //关联托盘控件
            ContextMenuStrip ContextMenu = new ContextMenuStrip();
            ContextMenu.Items.AddRange(new ToolStripItem[] { QuitItem });

            //关联菜单，并注册正在打开的事件
            this.NotifyIcon.ContextMenuStrip = ContextMenu;
            this.NotifyIcon.ContextMenuStrip.Opening += ContextMenuStrip_Opening;

            //鼠标双击托盘图标的事件
            this.NotifyIcon.MouseDoubleClick += new MouseEventHandler((o, e) =>
            {
                if (e.Button == MouseButtons.Left)
                {
                    Application.Current.Dispatcher.BeginInvoke(new Action(delegate
                    {
                        Application.Current.MainWindow.ShowInTaskbar = false;
                        Application.Current.MainWindow.Show();
                        Application.Current.MainWindow.WindowState = WindowState.Normal;
                        //Application.Current.MainWindow.Activate();
                    }));
                }
            });
        }


        /// <summary>
        /// 托盘菜单"退出"点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void QuitAppMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                Application.Current.Dispatcher.BeginInvoke(new Action(delegate
                {
                    Application.Current.Shutdown();
                }));
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return;
            }
        }

        /// <summary>
        /// 托盘菜单打开前事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ContextMenuStrip_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                POINT p;
                WinHelper.GetCursorPos(out p);
                Point menuPosition = new Point();
                menuPosition.X = p.X;
                menuPosition.Y = p.Y - 30 - NotifyIcon.ContextMenuStrip.Size.Height;
                NotifyIcon.ContextMenuStrip.Show(menuPosition);
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return;
            }
        }

        #endregion

        #region Handle Window Event

        /// <summary>
        /// 主窗口关闭事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MainWin_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                this.NotifyIcon?.Dispose();//没在IDisposable接口处理，是因为时机太晚了，来不及清除托盘的图标
                // 保存当前选择的游戏和设备服务器到配置文件
                if (this.MainWin != null && this.setting != null)
                {
                    // 获取当前选择的游戏ID和设备服务器Tag
                    int selectedGameId = this.MainWin.GetCurrentSelectedGameId();
                    string selectedDeviceServerTag = this.MainWin.GetCurrentSelectedDeviceServerTag();
                    this.setting.LastSelectedGameId = selectedGameId;
                    this.setting.LastSelectedDeviceServerTag = selectedDeviceServerTag;
                    // 写回配置文件
                    JsonFileHelper.WriteToJsonFile("Config/Setting.json", this.setting);
                }
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, ex);
                return;
            }
        }

        #endregion

        #region IDisposable 成员

        ~HBAPP()
        {
            Dispose(false);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源（可重载）
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // 清理托管资源
                }

                //Log.Write(Level.DEBUG, "Dispose");
                System.Diagnostics.Debug.WriteLine("HBAPP Dispose");

                //this._XrSvc?.Shutdown();
            }
            catch
            {
                return;
            }
        }

        #endregion

    }
}
