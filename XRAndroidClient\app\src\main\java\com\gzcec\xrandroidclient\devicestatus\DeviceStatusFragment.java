package com.gzcec.xrandroidclient.devicestatus;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.widget.TextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import com.gzcec.xrandroidclient.devicestatus.StatusDeviceAdapter;
import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.communication.MqttCommunicationManager;
import com.gzcec.xrandroidclient.data.DeviceDataRepository;
import androidx.lifecycle.Observer;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceListResponseMessage;
import com.gzcec.xrandroidclient.communication.messages.device.DeviceStatusUpdateMessage;
import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.R;

public class DeviceStatusFragment extends Fragment implements
    MqttCommunicationManager.DeviceStatusListener,
    MqttCommunicationManager.ConnectionListener {

    private static final String TAG = "DeviceStatusFragment";

    private View mView;
    private RecyclerView recyclerView;
    private StatusDeviceAdapter adapter;
    private final List<DeviceInfo> deviceList = new ArrayList<>();
    private DeviceDataRepository deviceRepository;
    private Handler mainHandler;

    // MQTT通信管理器
    private MqttCommunicationManager mqttManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (mView != null) {
            Log.d(TAG, "视图已存在，复用现有视图");
            return mView;
        }

        Log.d(TAG, "创建DeviceStatusFragment视图");
        mView = inflater.inflate(R.layout.fragment_device_status, container, false);
        mainHandler = new Handler(Looper.getMainLooper());

        // 获取设备数据仓库
        deviceRepository = DeviceDataRepository.getInstance();

        // 初始化MQTT通信管理器
        initMqttManager();

        // 初始化UI组件
        initViews();

        // 设置数据绑定
        setupDataBinding();

        // 连接到MQTT服务器并请求设备列表
        connectAndRequestDeviceList();

        return mView;
    }

    /**
     * 初始化MQTT通信管理器
     */
    private void initMqttManager() {
        try {
            // 使用统一的MQTT通信管理器
            mqttManager = MqttCommunicationManager.getInstance();

            // 注册监听器
            mqttManager.addDeviceStatusListener(this);
            mqttManager.addConnectionListener(this);

            Log.d(TAG, "使用MQTT通信管理器");
        } catch (Exception e) {
            Log.e(TAG, "初始化MQTT通信管理器失败", e);
        }
    }

    /**
     * 设置数据绑定
     */
    private void setupDataBinding() {
        Log.d(TAG, "设置数据绑定");

        // 观察所有设备列表
        deviceRepository.getAllDevicesLiveData().observe(getViewLifecycleOwner(), new Observer<List<DeviceInfo>>() {
            @Override
            public void onChanged(List<DeviceInfo> devices) {
                if (devices != null) {
                    Log.d(TAG, "数据绑定更新: 收到 " + devices.size() + " 台设备");

                    // 更新本地设备列表
                    deviceList.clear();
                    deviceList.addAll(devices);

                    // 通知适配器数据变化
                    if (adapter != null) {
                        adapter.notifyDataSetChanged();
                    }

                    Log.d(TAG, "设备状态列表UI已更新");
                }
            }
        });

        // 观察设备统计信息
        deviceRepository.getStatisticsLiveData().observe(getViewLifecycleOwner(), new Observer<DeviceDataRepository.DeviceStatistics>() {
            @Override
            public void onChanged(DeviceDataRepository.DeviceStatistics statistics) {
                if (statistics != null) {
                    Log.d(TAG, "设备统计更新: " + statistics.toString());

                    // 可以在这里更新UI显示统计信息
                    // 例如：更新标题栏的在线设备数量等
                }
            }
        });
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        recyclerView = mView.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));

        // 初始化设备列表框架（显示所有HMD设备）
        initializeDeviceList();

        adapter = new StatusDeviceAdapter(getContext(), deviceList);
        recyclerView.setAdapter(adapter);

        Log.d(TAG, "UI组件初始化完成，设备数量: " + deviceList.size());
    }

    /**
     * 初始化设备列表
     */
    private void initializeDeviceList() {
        deviceList.clear();

        // 获取所有HMD设备
        List<DeviceInfo> allDevices = DeviceInfo.createInitialDeviceList();
        for (DeviceInfo device : allDevices) {
            if (device.getDeviceType() == com.gzcec.xrandroidclient.device.DeviceType.HMD) {
                deviceList.add(device);
            }
        }

        Log.d(TAG, "初始化设备列表完成: " + deviceList.size() + " 台HMD设备");
    }

    /**
     * 连接并请求设备列表
     */
    private void connectAndRequestDeviceList() {
        try {
            if (!mqttManager.isConnected()) {
                Log.i(TAG, "MQTT未连接，尝试连接");
                mqttManager.connect();
            } else {
                Log.d(TAG, "MQTT已连接，请求设备列表");
                requestDeviceList();
            }
        } catch (Exception e) {
            Log.e(TAG, "连接MQTT服务器失败", e);
        }
    }

    /**
     * 请求设备列表
     */
    private void requestDeviceList() {
        if (mqttManager != null && mqttManager.isConnected()) {
            Log.i(TAG, "请求设备列表");
            mqttManager.requestDeviceList(new MqttCommunicationManager.ResponseCallback() {
                @Override
                public void onResponse(BaseMessage response) {
                    Log.d(TAG, "设备列表请求回调: " + (response != null ? "收到响应" : "null"));
                }

                @Override
                public void onTimeout() {
                    Log.w(TAG, "设备列表请求超时");
                }
            });
        } else {
            Log.w(TAG, "MQTT通信管理器未连接，无法请求设备列表");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "Fragment恢复，检查MQTT连接");

        // 确保已注册到通信管理器
        if (mqttManager != null) {
            mqttManager.addDeviceStatusListener(this);
            mqttManager.addConnectionListener(this);
        }

        // 如果MQTT已连接但设备列表为空，请求设备列表
        if (mqttManager != null && mqttManager.isConnected() && isDeviceListEmpty()) {
            requestDeviceList();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "Fragment暂停");
        // 不断开MQTT连接，保持连接状态
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "DeviceStatusFragment销毁");

        // 从通信管理器中移除监听器
        if (mqttManager != null) {
            mqttManager.removeDeviceStatusListener(this);
            mqttManager.removeConnectionListener(this);
        }
    }

    /**
     * 检查设备列表是否为空（只有初始状态的设备）
     */
    private boolean isDeviceListEmpty() {
        for (DeviceInfo device : deviceList) {
            if (device.isOnline() || device.getBatteryLevel() > 0) {
                return false; // 有真实数据
            }
        }
        return true; // 只有初始数据
    }

    // ========== MQTT监听器实现 ==========

    // DeviceStatusListener 接口实现
    @Override
    public void onDeviceListResponse(DeviceListResponseMessage response) {
        mainHandler.post(() -> {
            Log.d(TAG, "收到设备列表: " + response.getTotalCount() + " 台设备");
            Log.d(TAG, "总设备数: " + response.getTotalCount() + ", 在线设备数: " + response.getOnlineCount());

            // 更新设备列表
            updateDeviceListFromServer(response.getDevices());

            // 移除弹窗提示，仅记录日志
            Log.d(TAG, "设备状态已更新: " + response.getTotalCount() + " 台设备，" + response.getOnlineCount() + " 台在线");
        });
    }

    @Override
    public void onDeviceStatusUpdate(DeviceStatusUpdateMessage update) {
        mainHandler.post(() -> {
            Log.d(TAG, "收到设备状态更新，设备数量: " + update.getDevices().size());

            // 更新设备状态
            updateDeviceListFromServer(update.getDevices());
        });
    }

    // ConnectionListener 接口实现
    @Override
    public void onConnected() {
        mainHandler.post(() -> {
            Toast.makeText(getContext(), "已连接到服务器", Toast.LENGTH_SHORT).show();
            Log.i(TAG, "MQTT连接成功");

            // 连接成功后自动请求设备列表
            requestDeviceList();
        });
    }

    @Override
    public void onDisconnected() {
        mainHandler.post(() -> {
            Toast.makeText(getContext(), "与服务器断开连接", Toast.LENGTH_SHORT).show();
            Log.w(TAG, "MQTT连接断开");
        });
    }

    @Override
    public void onConnectionError(String error) {
        mainHandler.post(() -> {
            Toast.makeText(getContext(), "连接错误: " + error, Toast.LENGTH_LONG).show();
            Log.e(TAG, "MQTT连接错误: " + error);
        });
    }

    /**
     * 从服务器响应更新设备列表
     */
    private void updateDeviceListFromServer(List<DeviceInfo> serverDevices) {
        if (serverDevices == null) return;

        // 使用DeviceInfo的统一更新方法
        int updatedCount = DeviceInfo.updateDeviceListFromServer(deviceList, serverDevices);

        // 只显示HMD设备
        List<DeviceInfo> hmdDevices = new ArrayList<>();
        for (DeviceInfo device : deviceList) {
            if (device.getDeviceType() == com.gzcec.xrandroidclient.device.DeviceType.HMD) {
                hmdDevices.add(device);
            }
        }

        // 更新显示的设备列表
        deviceList.clear();
        deviceList.addAll(hmdDevices);

        // 通知适配器数据变化
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }

        Log.d(TAG, "设备状态列表已更新: " + updatedCount + " 台设备信息已更新，显示 " + deviceList.size() + " 台HMD设备");
    }
}