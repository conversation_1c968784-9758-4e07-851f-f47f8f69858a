﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Data;
using XRSvc.CustomControl;
using XRSvc.DataPack; // Ensure GameInfo is referenced correctly
using XRSvc.ViewModels;
using XRSvc.DataSource;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 游戏列表, key=GameID
        /// </summary>
        private readonly Dictionary<int, GameInfo> GameList;

        public ObservableCollection<GameInfo> GameListValues { get; set; }

        public ICommand SaveGameNameCommand { get; }

        private GameViewModel _gameViewModel;
        private DeviceViewModel _deviceViewModel;

        public DeviceViewModel DeviceViewModel => _deviceViewModel;

        public MainWindow()
        {
            InitializeComponent();
            _gameViewModel = new GameViewModel();
            _gameViewModel.LoadDefaultGames();
            // 将命令赋值到 ViewModel
            _gameViewModel.SaveGameNameCommand = new RelayCommand<GameSource>(SaveGameName);

            _deviceViewModel = new DeviceViewModel();
            _deviceViewModel.LoadDefaultDevices();

            // 只绑定GameViewModel，保证XAML中{Binding Games}等路径可用
            this.DataContext = _gameViewModel;
        }

        private void SaveGameName(GameSource gameSource)
        {
            if (gameSource != null)
            {
                MessageBox.Show($"游戏名称已修改为: {gameSource.Name}", "修改成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}

