package com.gzcec.xrandroidclient.devicestatus;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;
import com.gzcec.xrandroidclient.device.DeviceInfo;
import com.gzcec.xrandroidclient.R;
import android.content.Context;

public class StatusDeviceAdapter extends RecyclerView.Adapter<StatusDeviceAdapter.ViewHolder> {
    private List<DeviceInfo> deviceList;
    private Context context;

    public StatusDeviceAdapter(Context context, List<DeviceInfo> deviceList) {
        this.context = context;
        this.deviceList = deviceList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.devicestatus_card, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        DeviceInfo device = deviceList.get(position);
        holder.tvDeviceId.setText(String.valueOf(device.getId())); // 显示设备ID
        holder.tvStatus.setText(device.isOnline() ? "在线" : "离线");

        // 设备离线时显示电量为0%
        String batteryText = device.isOnline() ? device.getBattery() : "0%";
        holder.tvBattery.setText(batteryText);

        holder.tvServerIp.setText(device.getIpAddress());
        holder.tvSnCode.setText("SN:" + device.getSerialNumber()); // 序列号添加冒号分隔

        // 设置状态图标和颜色
        if (device.isOnline()) {
            holder.ivStatus.setImageResource(R.drawable.ic_online);
            holder.tvStatus.setTextColor(android.graphics.Color.GREEN);
        } else {
            holder.ivStatus.setImageResource(R.drawable.ic_offline);
            holder.tvStatus.setTextColor(android.graphics.Color.RED);
        }

        // 动态设置电量字体颜色
        int batteryPercent = 0;
        try {
            String batteryStr = batteryText.replace("%", "").trim();
            batteryPercent = Integer.parseInt(batteryStr);
        } catch (Exception e) {
            // 解析失败，默认0
        }

        // 离线设备电量显示为灰色，在线设备根据电量显示不同颜色
        if (!device.isOnline()) {
            holder.tvBattery.setTextColor(context.getResources().getColor(android.R.color.darker_gray));
        } else if (batteryPercent > 60) {
            holder.tvBattery.setTextColor(context.getResources().getColor(R.color.battery_high));
        } else if (batteryPercent > 20) {
            holder.tvBattery.setTextColor(context.getResources().getColor(R.color.battery_medium));
        } else {
            holder.tvBattery.setTextColor(context.getResources().getColor(R.color.battery_low));
        }
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivStatus;
        TextView tvDeviceId, tvStatus, tvBattery, tvServerIp, tvSnCode;

        ViewHolder(View itemView) {
            super(itemView);
            ivStatus = itemView.findViewById(R.id.iv_status);
            tvDeviceId = itemView.findViewById(R.id.tv_device_id);
            tvStatus = itemView.findViewById(R.id.tv_status);
            tvBattery = itemView.findViewById(R.id.tv_battery);
            tvServerIp = itemView.findViewById(R.id.tv_server_ip);
            tvSnCode = itemView.findViewById(R.id.tv_sn_code);
        }
    }
}