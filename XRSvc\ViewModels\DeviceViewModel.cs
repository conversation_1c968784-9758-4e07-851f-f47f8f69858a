﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading;
using System.Threading.Tasks;
using XRSvc.DataPack;
using XRSvc.DataSource;
using System.Linq;
using PICOSDK;
using System.Timers;
using System.Windows;
using System.ComponentModel;
using System.Windows.Data;
using System.Collections.Specialized;
using Jskj.XRSvc.MQTT;

namespace XRSvc.ViewModels
{
    public class DeviceViewModel : BaseViewModel
    {
        #region Fields

        private ObservableCollection<DeviceSource> _source;
        private System.Timers.Timer _batteryTimer;
        private const int BatteryIntervalMs = 10000; // 电量获取间隔，单位毫秒

        // 新增：CollectionView 字段
        private ListCollectionView _devices80View;
        private ListCollectionView _devices81View;
        private ListCollectionView _devices8081View;

        // 延迟广播机制
        private System.Timers.Timer _broadcastTimer;
        private const int BroadcastDelayMs = 500; // 广播延迟，单位毫秒
        private volatile bool _hasPendingBroadcast = false;

        #endregion

        #region Properties

        /// <summary>
        /// 设备集合，供界面绑定
        /// </summary>
        public ObservableCollection<DeviceSource> Source
        {
            get => _source;
            set
            {
                _source = value;
                OnPropertyChanged();
                InitCollectionViews();
            }
        }

        /// <summary>
        /// 以80开头的设备集合视图
        /// </summary>
        public ListCollectionView Devices80View => _devices80View;

        /// <summary>
        /// 以81开头的设备集合视图
        /// </summary>
        public ListCollectionView Devices81View => _devices81View;

        /// <summary>
        /// 以80/81开头的设备集合视图
        /// </summary>
        public ListCollectionView Devices8081View => _devices8081View;

        #endregion

        #region Constructor

        // MQTT客户端引用，用于设备状态变化时的实时广播
        private MqttClientService _mqttClient;

        // 构造函数，初始化设备集合
        public DeviceViewModel()
        {
            _source = new ObservableCollection<DeviceSource>();
            InitCollectionViews();
            // 订阅设备管理器事件
            DeviceManager.Instance.DeviceFound += OnDeviceFound;
            DeviceManager.Instance.DeviceLost += OnDeviceLost;
            DeviceManager.Instance.BatteryChanged += OnBatteryChanged;
            // 初始化定时器但不启动
            _batteryTimer = new System.Timers.Timer(BatteryIntervalMs);
            _batteryTimer.Elapsed += BatteryTimer_Elapsed;
            _batteryTimer.AutoReset = true;
        }

        /// <summary>
        /// 设置MQTT客户端，用于设备状态变化时的实时广播
        /// </summary>
        public void SetMqttClient(MqttClientService mqttClient)
        {
            _mqttClient = mqttClient;
        }

        /// <summary>
        /// 初始化 CollectionView 视图及筛选条件
        /// </summary>
        private void InitCollectionViews()
        {
            _devices80View = new ListCollectionView(_source);
            _devices80View.Filter = d => ((DeviceSource)d).ID.ToString().StartsWith("80");

            _devices81View = new ListCollectionView(_source);
            _devices81View.Filter = d => ((DeviceSource)d).ID.ToString().StartsWith("81");

            _devices8081View = new ListCollectionView(_source);
            _devices8081View.Filter = d => {
                var idStr = ((DeviceSource)d).ID.ToString();
                return idStr.StartsWith("80") || idStr.StartsWith("81");
            };

            OnPropertyChanged(nameof(Devices80View));
            OnPropertyChanged(nameof(Devices81View));
            OnPropertyChanged(nameof(Devices8081View));
        }

        /// <summary>
        /// 刷新 CollectionView 视图
        /// </summary>
        private void RefreshViews()
        {
            _devices80View?.Refresh();
            _devices81View?.Refresh();
            _devices8081View?.Refresh();
            OnPropertyChanged(nameof(Devices80View));
            OnPropertyChanged(nameof(Devices81View));
            OnPropertyChanged(nameof(Devices8081View));
        }

        #endregion

        #region Methods

        // 重置所有设备信息
        public void ResetSource()
        {
            RunOnUIThread(() =>
            {
                foreach (var device in _source)
                {
                    device.SerialNumber = string.Empty;
                    device.IP = string.Empty;
                    device.Name = string.Empty;
                    device.Type = DeviceType.SERVER;
                    device.IsEnabled = false;
                    device.IsOnline = false;
                }
                OnPropertyChanged(nameof(Source));
                RefreshViews();
            });
        }

        // 更新设备集合
        public void UpdateSource(DeviceInfo[] deviceInfos)
        {
            RunOnUIThread(() =>
            {
                _source.Clear();

                foreach (var info in deviceInfos)
                {
                    _source.Add(new DeviceSource(info));
                }

                OnPropertyChanged(nameof(Source));
                RefreshViews();
            });
        }

        // 获取设备信息数组
        public DeviceInfo[] GetSourceInfos()
        {
            var infos = new DeviceInfo[_source.Count];
            for (int i = 0; i < _source.Count; i++)
            {
                infos[i] = _source[i].ToDeviceInfo();
            }
            return infos;
        }

        // 异步加载默认设备
        public async Task LoadDefaultDevicesAsync(CancellationToken cancellationToken)
        {
            IsLoading = true;

            // 在后台线程准备数据
            var devices = new List<DeviceSource>();
            var random = new Random();

            for (int i = 1; i <= 12; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    IsLoading = false;
                    return;
                }

                await Task.Delay(50, cancellationToken); // 模拟异步操作

                var device = new DeviceSource(new DeviceInfo
                {
                    ID = 8000 + i,
                    SerialNumber = $"SN-{i}",
                    Name = $"{i}号机",
                    DeviceType = DeviceType.HMD,
                    BatteryLevel = random.Next(0, 100),
                    IsEnabled = random.Next(0, 2) == 1,
                    IsOnline = random.Next(0, 2) == 1
                });
                devices.Add(device);
            }

            // 在UI线程更新界面
            await RunOnUIThreadAsync(() =>
            {
                _source.Clear();
                foreach (var device in devices)
                {
                    _source.Add(device);
                }

                OnPropertyChanged(nameof(Source));
                RefreshViews();
                IsLoading = false;
            });
        }

        // 设备发现事件处理
        private void OnDeviceFound(object sender, DeviceEventArgs e)
        {
            // 使用批量处理，避免频繁的UI更新
            BatchRunOnUIThread(() =>
            {
                var existing = _source.FirstOrDefault(d => d.SerialNumber == e.DevID);
                if (existing == null)
                {
                    var info = DeviceManager.Instance.Devices[e.DevID];
                    var ds = new DeviceSource(info);
                    _source.Add(ds);
                    DeviceManager.Instance.GetBattery(e.DevID);
                }
                else
                {
                    existing.IsOnline = true;
                    existing.ConnectionStatus = "已连接";
                }
                // 有设备在线时启动定时器
                if (_source.Any(d => d.IsOnline) && !_batteryTimer.Enabled)
                {
                    // 先立即获取一次所有在线设备电量
                    foreach (var device in _source.Where(d => d.IsOnline && !string.IsNullOrEmpty(d.SerialNumber)))
                    {
                        DeviceManager.Instance.GetBattery(device.SerialNumber);
                    }
                    _batteryTimer.Start();
                }
                OnPropertyChanged(nameof(Source));
                RefreshViews();

                // 设备状态变化时延迟广播MQTT更新，避免频繁发送
                ScheduleDeviceStatusBroadcast();
            }, $"device_found_{e.DevID}");
        }

        // 设备丢失事件处理
        private void OnDeviceLost(object sender, DeviceEventArgs e)
        {
            // 使用批量处理，避免频繁的UI更新
            BatchRunOnUIThread(() =>
            {
                var existing = _source.FirstOrDefault(d => d.SerialNumber == e.DevID);
                if (existing != null)
                {
                    existing.IsOnline = false;
                    existing.ConnectionStatus = "未连接";
                    // 设备离线时电量清零
                    existing.BatteryLevel = 0;
                    Jskj.AppLog.Log.Write(Jskj.AppLog.Level.DEBUG, $"设备 {existing.SerialNumber} 离线，电量已清零");
                }
                // 所有设备都离线时停止定时器
                if (!_source.Any(d => d.IsOnline) && _batteryTimer.Enabled)
                {
                    _batteryTimer.Stop();
                }
                OnPropertyChanged(nameof(Source));
                RefreshViews();

                // 设备状态变化时延迟广播MQTT更新，避免频繁发送
                ScheduleDeviceStatusBroadcast();
            }, $"device_lost_{e.DevID}");
        }

        // 电池变化事件处理
        private void OnBatteryChanged(object sender, DeviceEventArgs e)
        {
            RunOnUIThread(() =>
            {
                var existing = _source.FirstOrDefault(d => d.SerialNumber == e.DevID);
                if (existing != null)
                {
                    existing.BatteryLevel = e.Battery;
                }
                OnPropertyChanged(nameof(Source));
                RefreshViews();

                // 电量变化时立即广播MQTT更新
                BroadcastDeviceStatusUpdate();
            });
        }

        // 定时器回调，定时获取在线设备电量
        private void BatteryTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 只对在线设备获取电池信息
                foreach (var device in _source.Where(d => d.IsOnline && !string.IsNullOrEmpty(d.SerialNumber)))
                {
                    DeviceManager.Instance.GetBattery(device.SerialNumber);
                }
            });
        }

        /// <summary>
        /// 更新设备游戏运行状态（线程安全，优化版）
        /// </summary>
        /// <param name="serialNumber">设备序列号</param>
        /// <param name="isGameRunning">游戏是否运行</param>
        /// <param name="gamePackageName">游戏包名</param>
        public void UpdateDeviceGameStatus(string serialNumber, bool isGameRunning, string gamePackageName = null)
        {
            // 使用批量处理，减少UI线程切换
            BatchRunOnUIThread(() =>
            {
                var device = _source.FirstOrDefault(d => d.SerialNumber == serialNumber);
                if (device != null)
                {
                    device.IsGameRunning = isGameRunning;
                    device.CurrentGamePackageName = gamePackageName;
                    // 游戏状态更新完成后，清除启动中状态
                    device.IsGameStarting = false;

                    // 触发UI更新
                    OnPropertyChanged(nameof(Source));
                    RefreshViews();

                    // 延迟广播设备状态更新，避免频繁发送
                    ScheduleDeviceStatusBroadcast();
                }
                else
                {
                    Jskj.AppLog.Log.Write(Jskj.AppLog.Level.ERROR, $"未找到序列号为 {serialNumber} 的设备");
                }
            }, $"game_status_{serialNumber}");
        }

        /// <summary>
        /// 批量更新多个设备的游戏运行状态（线程安全）
        /// </summary>
        /// <param name="deviceUpdates">设备更新信息列表</param>
        public void UpdateMultipleDeviceGameStatus(List<(string SerialNumber, bool IsGameRunning, string GamePackageName)> deviceUpdates)
        {
            RunOnUIThread(() =>
            {
                bool hasUpdates = false;

                foreach (var update in deviceUpdates)
                {
                    var device = _source.FirstOrDefault(d => d.SerialNumber == update.SerialNumber);
                    if (device != null)
                    {
                        device.IsGameRunning = update.IsGameRunning;
                        device.CurrentGamePackageName = update.GamePackageName;
                        // 游戏状态更新完成后，清除启动中状态
                        device.IsGameStarting = false;
                        hasUpdates = true;
                    }
                }

                if (hasUpdates)
                {
                    // 触发UI更新
                    OnPropertyChanged(nameof(Source));
                    RefreshViews();

                    // 广播设备状态更新
                    BroadcastDeviceStatusUpdate();
                }
            });
        }

        /// <summary>
        /// 广播设备状态更新到MQTT
        /// </summary>
        private async void BroadcastDeviceStatusUpdate()
        {
            if (_mqttClient != null)
            {
                try
                {
                    await _mqttClient.BroadcastDeviceStatusUpdateAsync();
                }
                catch (Exception ex)
                {
                    // 记录错误但不影响主流程
                    System.Diagnostics.Debug.WriteLine($"MQTT广播设备状态失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 调度设备状态广播（延迟执行，避免频繁发送）
        /// </summary>
        private void ScheduleDeviceStatusBroadcast()
        {
            if (_broadcastTimer == null)
            {
                _broadcastTimer = new System.Timers.Timer(BroadcastDelayMs);
                _broadcastTimer.Elapsed += OnBroadcastTimerElapsed;
                _broadcastTimer.AutoReset = false; // 只执行一次
            }

            _hasPendingBroadcast = true;
            _broadcastTimer.Stop();
            _broadcastTimer.Start();
        }

        /// <summary>
        /// 广播定时器触发事件
        /// </summary>
        private void OnBroadcastTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (_hasPendingBroadcast)
            {
                _hasPendingBroadcast = false;
                BroadcastDeviceStatusUpdate();
            }
        }

        /// <summary>
        /// 立即刷新所有待处理的UI更新
        /// </summary>
        public async Task FlushPendingUIUpdatesAsync()
        {
            await FlushUIBatchAsync();
        }

        // 析构函数，释放定时器
        ~DeviceViewModel()
        {
            if (_batteryTimer != null)
            {
                _batteryTimer.Stop();
                _batteryTimer.Dispose();
            }

            if (_broadcastTimer != null)
            {
                _broadcastTimer.Stop();
                _broadcastTimer.Dispose();
            }
        }

        #endregion
    }
}