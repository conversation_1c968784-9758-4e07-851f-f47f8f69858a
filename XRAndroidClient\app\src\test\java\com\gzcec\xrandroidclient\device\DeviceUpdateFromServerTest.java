package com.gzcec.xrandroidclient.device;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试Android端设备信息以PC端为主的更新逻辑
 */
public class DeviceUpdateFromServerTest {

    private List<DeviceInfo> localDevices;
    private List<DeviceInfo> serverDevices;

    @Before
    public void setUp() {
        // 创建本地设备列表（模拟Android端初始化的设备）
        localDevices = new ArrayList<>();
        
        // 本地设备8001 - 初始状态
        DeviceInfo localDevice1 = new DeviceInfo();
        localDevice1.setId(8001);
        localDevice1.setSerialNumber("HMD_80_01");
        localDevice1.setName("1号机");
        localDevice1.setIpAddress("*************");
        localDevice1.setDeviceType(DeviceType.HMD);
        localDevice1.setOnline(false);
        localDevice1.setEnabled(false);
        localDevice1.setBatteryLevel(0);
        localDevice1.setSelected(true); // 用户已选中
        localDevices.add(localDevice1);

        // 本地设备8002 - 初始状态
        DeviceInfo localDevice2 = new DeviceInfo();
        localDevice2.setId(8002);
        localDevice2.setSerialNumber("HMD_80_02");
        localDevice2.setName("2号机");
        localDevice2.setIpAddress("*************");
        localDevice2.setDeviceType(DeviceType.HMD);
        localDevice2.setOnline(false);
        localDevice2.setEnabled(false);
        localDevice2.setBatteryLevel(0);
        localDevice2.setSelected(false);
        localDevices.add(localDevice2);

        // 创建服务器设备列表（模拟PC端传来的设备信息）
        serverDevices = new ArrayList<>();
        
        // 服务器设备8001 - PC端实际信息
        DeviceInfo serverDevice1 = new DeviceInfo();
        serverDevice1.setId(8001);
        serverDevice1.setSerialNumber("PA7N1DMGH3010660W"); // PC端真实序列号
        serverDevice1.setName("PICO设备01"); // PC端设备名称
        serverDevice1.setIpAddress("*************"); // PC端检测到的IP
        serverDevice1.setDeviceType(DeviceType.HMD);
        serverDevice1.setOnline(true); // PC端显示在线
        serverDevice1.setEnabled(true); // PC端已启用
        serverDevice1.setBatteryLevel(85); // PC端检测到的电量
        serverDevice1.setInGame(false);
        serverDevices.add(serverDevice1);

        // 服务器设备9001 - 新的外设设备
        DeviceInfo serverDevice2 = new DeviceInfo();
        serverDevice2.setId(9001);
        serverDevice2.setSerialNumber("MOTION_PLATFORM_REAL");
        serverDevice2.setName("动感平台设备");
        serverDevice2.setIpAddress("*************");
        serverDevice2.setDeviceType(DeviceType.MotionPlatform);
        serverDevice2.setOnline(true);
        serverDevice2.setEnabled(true);
        serverDevice2.setBatteryLevel(100);
        serverDevices.add(serverDevice2);
    }

    @Test
    public void testUpdateExistingDeviceWithPCInfo() {
        // 执行更新
        int updatedCount = DeviceInfo.updateDeviceListFromServer(localDevices, serverDevices);
        
        // 验证更新数量
        assertEquals("应该更新2个设备", 2, updatedCount);
        
        // 查找更新后的8001设备
        DeviceInfo updatedDevice = null;
        for (DeviceInfo device : localDevices) {
            if (device.getId() == 8001) {
                updatedDevice = device;
                break;
            }
        }
        
        assertNotNull("8001设备应该存在", updatedDevice);
        
        // 验证设备信息已更新为PC端信息
        assertEquals("序列号应更新为PC端信息", "PA7N1DMGH3010660W", updatedDevice.getSerialNumber());
        assertEquals("设备名称应更新为PC端信息", "PICO设备01", updatedDevice.getName());
        assertEquals("IP地址应更新为PC端信息", "*************", updatedDevice.getIpAddress());
        assertTrue("设备应显示为在线", updatedDevice.isOnline());
        assertTrue("设备应显示为启用", updatedDevice.isEnabled());
        assertEquals("电量应更新为PC端信息", 85.0, updatedDevice.getBatteryLevel(), 0.1);
        
        // 验证本地UI状态被保持
        assertTrue("选中状态应该被保持", updatedDevice.isSelected());
    }

    @Test
    public void testAddNewDeviceFromPC() {
        // 执行更新
        DeviceInfo.updateDeviceListFromServer(localDevices, serverDevices);
        
        // 查找新添加的9001设备
        DeviceInfo newDevice = null;
        for (DeviceInfo device : localDevices) {
            if (device.getId() == 9001) {
                newDevice = device;
                break;
            }
        }
        
        assertNotNull("9001设备应该被添加", newDevice);
        assertEquals("新设备序列号", "MOTION_PLATFORM_REAL", newDevice.getSerialNumber());
        assertEquals("新设备名称", "动感平台设备", newDevice.getName());
        assertEquals("新设备类型", DeviceType.MotionPlatform, newDevice.getDeviceType());
        assertTrue("新设备应在线", newDevice.isOnline());
        assertFalse("新设备默认未选中", newDevice.isSelected());
    }

    @Test
    public void testUnchangedDeviceRemainsSame() {
        // 执行更新
        DeviceInfo.updateDeviceListFromServer(localDevices, serverDevices);
        
        // 查找未更新的8002设备
        DeviceInfo unchangedDevice = null;
        for (DeviceInfo device : localDevices) {
            if (device.getId() == 8002) {
                unchangedDevice = device;
                break;
            }
        }
        
        assertNotNull("8002设备应该存在", unchangedDevice);
        
        // 验证设备信息保持原样（因为PC端没有传递8002的信息）
        assertEquals("序列号应保持不变", "HMD_80_02", unchangedDevice.getSerialNumber());
        assertEquals("设备名称应保持不变", "2号机", unchangedDevice.getName());
        assertEquals("IP地址应保持不变", "*************", unchangedDevice.getIpAddress());
        assertFalse("设备应保持离线", unchangedDevice.isOnline());
        assertFalse("设备应保持禁用", unchangedDevice.isEnabled());
        assertEquals("电量应保持不变", 0.0, unchangedDevice.getBatteryLevel(), 0.1);
        assertFalse("选中状态应保持不变", unchangedDevice.isSelected());
    }

    @Test
    public void testDeviceListSizeAfterUpdate() {
        int originalSize = localDevices.size();
        
        // 执行更新
        DeviceInfo.updateDeviceListFromServer(localDevices, serverDevices);
        
        // 验证设备列表大小
        assertEquals("设备列表应增加1个（新增9001设备）", originalSize + 1, localDevices.size());
    }

    @Test
    public void testNullInputHandling() {
        // 测试空输入处理
        int result1 = DeviceInfo.updateDeviceListFromServer(null, serverDevices);
        assertEquals("null本地列表应返回0", 0, result1);
        
        int result2 = DeviceInfo.updateDeviceListFromServer(localDevices, null);
        assertEquals("null服务器列表应返回0", 0, result2);
        
        int result3 = DeviceInfo.updateDeviceListFromServer(null, null);
        assertEquals("双null应返回0", 0, result3);
    }

    @Test
    public void testCompleteDeviceInfoOverride() {
        // 创建一个设备，所有信息都与PC端不同
        DeviceInfo localDevice = new DeviceInfo();
        localDevice.setId(8001);
        localDevice.setSerialNumber("OLD_SERIAL");
        localDevice.setName("旧设备名");
        localDevice.setIpAddress("192.168.1.999");
        localDevice.setDeviceType(DeviceType.SERVER); // 错误的类型
        localDevice.setOnline(false);
        localDevice.setEnabled(false);
        localDevice.setBatteryLevel(50);
        localDevice.setSelected(true); // 保持选中状态
        
        List<DeviceInfo> testLocal = new ArrayList<>();
        testLocal.add(localDevice);
        
        // 执行更新
        DeviceInfo.updateDeviceListFromServer(testLocal, serverDevices);
        
        DeviceInfo updated = testLocal.get(0);
        
        // 验证所有信息都被PC端信息覆盖
        assertEquals("序列号应被覆盖", "PA7N1DMGH3010660W", updated.getSerialNumber());
        assertEquals("名称应被覆盖", "PICO设备01", updated.getName());
        assertEquals("IP应被覆盖", "*************", updated.getIpAddress());
        assertEquals("类型应被覆盖", DeviceType.HMD, updated.getDeviceType());
        assertTrue("在线状态应被覆盖", updated.isOnline());
        assertTrue("启用状态应被覆盖", updated.isEnabled());
        assertEquals("电量应被覆盖", 85.0, updated.getBatteryLevel(), 0.1);
        
        // 但选中状态应该保持
        assertTrue("选中状态应该保持", updated.isSelected());
    }
}
