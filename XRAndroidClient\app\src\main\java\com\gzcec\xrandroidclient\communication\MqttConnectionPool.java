package com.gzcec.xrandroidclient.communication;

import android.content.Context;
import android.util.Log;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * MQTT连接池管理器
 * 优化MQTT连接的创建、复用和销毁，提升系统性能
 */
public class MqttConnectionPool {
    private static final String TAG = "MqttConnectionPool";
    
    // 单例实例
    private static volatile MqttConnectionPool instance;
    private static final Object lock = new Object();
    
    // 连接池配置
    private static final int MAX_POOL_SIZE = 5;           // 最大连接数
    private static final int MIN_POOL_SIZE = 2;           // 最小连接数
    private static final int CONNECTION_TIMEOUT = 30000;  // 连接超时(ms)
    private static final int KEEP_ALIVE_INTERVAL = 60;    // 心跳间隔(s)
    private static final long CONNECTION_MAX_IDLE = 300000; // 连接最大空闲时间(5分钟)
    
    // 连接池存储
    private final ConcurrentLinkedQueue<PooledMqttConnection> availableConnections;
    private final ConcurrentHashMap<String, PooledMqttConnection> activeConnections;
    private final AtomicInteger totalConnections;
    private final ReentrantLock poolLock;
    
    // 连接配置
    private Context context;
    private String brokerUrl;
    private String clientIdPrefix;
    private MqttConnectOptions defaultOptions;
    
    /**
     * 私有构造函数
     */
    private MqttConnectionPool() {
        this.availableConnections = new ConcurrentLinkedQueue<>();
        this.activeConnections = new ConcurrentHashMap<>();
        this.totalConnections = new AtomicInteger(0);
        this.poolLock = new ReentrantLock();
    }
    
    /**
     * 获取单例实例
     */
    public static MqttConnectionPool getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new MqttConnectionPool();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化连接池
     */
    public void initialize(Context context, String brokerUrl, String clientIdPrefix) {
        this.context = context;
        this.brokerUrl = brokerUrl;
        this.clientIdPrefix = clientIdPrefix;
        
        // 配置默认连接选项
        this.defaultOptions = new MqttConnectOptions();
        this.defaultOptions.setCleanSession(true);
        this.defaultOptions.setConnectionTimeout(CONNECTION_TIMEOUT / 1000);
        this.defaultOptions.setKeepAliveInterval(KEEP_ALIVE_INTERVAL);
        this.defaultOptions.setAutomaticReconnect(true);
        this.defaultOptions.setMaxInflight(100);
        
        // 预创建最小连接数
        preCreateConnections();
        
        Log.i(TAG, "MQTT连接池初始化完成 - 预创建连接数: " + MIN_POOL_SIZE);
    }
    
    /**
     * 预创建最小连接数
     */
    private void preCreateConnections() {
        poolLock.lock();
        try {
            for (int i = 0; i < MIN_POOL_SIZE; i++) {
                PooledMqttConnection connection = createNewConnection();
                if (connection != null) {
                    availableConnections.offer(connection);
                    totalConnections.incrementAndGet();
                }
            }
        } finally {
            poolLock.unlock();
        }
    }
    
    /**
     * 创建新的连接
     */
    private PooledMqttConnection createNewConnection() {
        try {
            String clientId = clientIdPrefix + "_" + System.currentTimeMillis() + "_" + 
                            Thread.currentThread().getId();
            
            MqttAsyncClient client = new MqttAsyncClient(brokerUrl, clientId, new MemoryPersistence());
            PooledMqttConnection pooledConnection = new PooledMqttConnection(client, clientId);
            
            Log.d(TAG, "创建新的MQTT连接: " + clientId);
            return pooledConnection;
            
        } catch (Exception e) {
            Log.e(TAG, "创建MQTT连接失败", e);
            return null;
        }
    }
    
    /**
     * 获取连接
     */
    public PooledMqttConnection getConnection() {
        poolLock.lock();
        try {
            // 首先尝试从可用连接池获取
            PooledMqttConnection connection = availableConnections.poll();
            
            if (connection != null) {
                // 检查连接是否仍然有效
                if (isConnectionValid(connection)) {
                    connection.markAsActive();
                    activeConnections.put(connection.getClientId(), connection);
                    Log.d(TAG, "复用连接: " + connection.getClientId());
                    return connection;
                } else {
                    // 连接无效，关闭并重新创建
                    closeConnection(connection);
                    totalConnections.decrementAndGet();
                }
            }
            
            // 如果没有可用连接且未达到最大连接数，创建新连接
            if (totalConnections.get() < MAX_POOL_SIZE) {
                connection = createNewConnection();
                if (connection != null) {
                    connection.markAsActive();
                    activeConnections.put(connection.getClientId(), connection);
                    totalConnections.incrementAndGet();
                    Log.d(TAG, "创建新连接: " + connection.getClientId());
                    return connection;
                }
            }
            
            Log.w(TAG, "无法获取MQTT连接 - 连接池已满或创建失败");
            return null;
            
        } finally {
            poolLock.unlock();
        }
    }
    
    /**
     * 归还连接到池中
     */
    public void returnConnection(PooledMqttConnection connection) {
        if (connection == null) return;
        
        poolLock.lock();
        try {
            // 从活跃连接中移除
            activeConnections.remove(connection.getClientId());
            
            // 检查连接是否仍然有效
            if (isConnectionValid(connection) && availableConnections.size() < MAX_POOL_SIZE) {
                connection.markAsIdle();
                availableConnections.offer(connection);
                Log.d(TAG, "连接归还到池中: " + connection.getClientId());
            } else {
                // 连接无效或池已满，关闭连接
                closeConnection(connection);
                totalConnections.decrementAndGet();
                Log.d(TAG, "关闭多余连接: " + connection.getClientId());
            }
            
        } finally {
            poolLock.unlock();
        }
    }
    
    /**
     * 检查连接是否有效
     */
    private boolean isConnectionValid(PooledMqttConnection connection) {
        if (connection == null || connection.getClient() == null) {
            return false;
        }
        
        // 检查连接状态
        if (!connection.getClient().isConnected()) {
            return false;
        }
        
        // 检查空闲时间
        long idleTime = System.currentTimeMillis() - connection.getLastUsedTime();
        if (idleTime > CONNECTION_MAX_IDLE) {
            Log.d(TAG, "连接空闲时间过长: " + connection.getClientId() + ", 空闲时间: " + idleTime + "ms");
            return false;
        }
        
        return true;
    }
    
    /**
     * 关闭连接
     */
    private void closeConnection(PooledMqttConnection connection) {
        if (connection != null && connection.getClient() != null) {
            try {
                if (connection.getClient().isConnected()) {
                    connection.getClient().disconnect();
                }
                connection.getClient().close();
                Log.d(TAG, "连接已关闭: " + connection.getClientId());
            } catch (Exception e) {
                Log.e(TAG, "关闭连接失败: " + connection.getClientId(), e);
            }
        }
    }
    
    /**
     * 获取连接池状态
     */
    public PoolStatus getPoolStatus() {
        return new PoolStatus(
            totalConnections.get(),
            availableConnections.size(),
            activeConnections.size(),
            MAX_POOL_SIZE
        );
    }
    
    /**
     * 清理空闲连接
     */
    public void cleanupIdleConnections() {
        poolLock.lock();
        try {
            availableConnections.removeIf(connection -> {
                if (!isConnectionValid(connection)) {
                    closeConnection(connection);
                    totalConnections.decrementAndGet();
                    return true;
                }
                return false;
            });
        } finally {
            poolLock.unlock();
        }
    }
    
    /**
     * 销毁连接池
     */
    public void destroy() {
        poolLock.lock();
        try {
            // 关闭所有活跃连接
            for (PooledMqttConnection connection : activeConnections.values()) {
                closeConnection(connection);
            }
            activeConnections.clear();
            
            // 关闭所有可用连接
            while (!availableConnections.isEmpty()) {
                PooledMqttConnection connection = availableConnections.poll();
                closeConnection(connection);
            }
            
            totalConnections.set(0);
            Log.i(TAG, "MQTT连接池已销毁");
            
        } finally {
            poolLock.unlock();
        }
    }
    
    /**
     * 连接池状态类
     */
    public static class PoolStatus {
        public final int totalConnections;
        public final int availableConnections;
        public final int activeConnections;
        public final int maxPoolSize;
        
        public PoolStatus(int total, int available, int active, int maxSize) {
            this.totalConnections = total;
            this.availableConnections = available;
            this.activeConnections = active;
            this.maxPoolSize = maxSize;
        }
        
        @Override
        public String toString() {
            return String.format("PoolStatus{total=%d, available=%d, active=%d, max=%d}", 
                totalConnections, availableConnections, activeConnections, maxPoolSize);
        }
    }
}
