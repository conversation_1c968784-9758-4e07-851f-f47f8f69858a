package com.gzcec.xrandroidclient.communication;

import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 池化MQTT连接包装类
 * 包装MqttAsyncClient，添加连接池管理所需的元数据
 */
public class PooledMqttConnection {
    private final MqttAsyncClient client;
    private final String clientId;
    private final long createdTime;
    private final AtomicLong lastUsedTime;
    private volatile boolean isActive;
    
    /**
     * 构造函数
     */
    public PooledMqttConnection(MqttAsyncClient client, String clientId) {
        this.client = client;
        this.clientId = clientId;
        this.createdTime = System.currentTimeMillis();
        this.lastUsedTime = new AtomicLong(System.currentTimeMillis());
        this.isActive = false;
    }
    
    /**
     * 获取MQTT客户端
     */
    public MqttAsyncClient getClient() {
        updateLastUsedTime();
        return client;
    }
    
    /**
     * 获取客户端ID
     */
    public String getClientId() {
        return clientId;
    }
    
    /**
     * 获取创建时间
     */
    public long getCreatedTime() {
        return createdTime;
    }
    
    /**
     * 获取最后使用时间
     */
    public long getLastUsedTime() {
        return lastUsedTime.get();
    }
    
    /**
     * 更新最后使用时间
     */
    public void updateLastUsedTime() {
        lastUsedTime.set(System.currentTimeMillis());
    }
    
    /**
     * 标记为活跃状态
     */
    public void markAsActive() {
        this.isActive = true;
        updateLastUsedTime();
    }
    
    /**
     * 标记为空闲状态
     */
    public void markAsIdle() {
        this.isActive = false;
        updateLastUsedTime();
    }
    
    /**
     * 检查是否为活跃状态
     */
    public boolean isActive() {
        return isActive;
    }
    
    /**
     * 获取连接存活时间
     */
    public long getAliveTime() {
        return System.currentTimeMillis() - createdTime;
    }
    
    /**
     * 获取连接空闲时间
     */
    public long getIdleTime() {
        return System.currentTimeMillis() - lastUsedTime.get();
    }
    
    @Override
    public String toString() {
        return String.format("PooledMqttConnection{clientId='%s', active=%s, aliveTime=%dms, idleTime=%dms}", 
            clientId, isActive, getAliveTime(), getIdleTime());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PooledMqttConnection that = (PooledMqttConnection) obj;
        return clientId.equals(that.clientId);
    }
    
    @Override
    public int hashCode() {
        return clientId.hashCode();
    }
}
