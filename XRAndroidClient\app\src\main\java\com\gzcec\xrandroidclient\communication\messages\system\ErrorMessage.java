package com.gzcec.xrandroidclient.communication.messages.system;

import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.constants.MessageType;

/**
 * 错误消息
 */
public class ErrorMessage extends BaseMessage {
    private String errorCode;
    private String errorDescription;
    private String errorDetails;
    private String relatedMessageId;

    public ErrorMessage() {
        setType(MessageType.SYSTEM_ERROR);
    }

    // Getter和Setter方法
    public String getErrorCode() { return errorCode; }
    public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
    
    public String getErrorDescription() { return errorDescription; }
    public void setErrorDescription(String errorDescription) { this.errorDescription = errorDescription; }
    
    public String getErrorDetails() { return errorDetails; }
    public void setErrorDetails(String errorDetails) { this.errorDetails = errorDetails; }
    
    public String getRelatedMessageId() { return relatedMessageId; }
    public void setRelatedMessageId(String relatedMessageId) { this.relatedMessageId = relatedMessageId; }
}
