package com.gzcec.xrandroidclient.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.Socket;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 网络诊断工具
 * 提供网络连接诊断和故障排除功能
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2.0
 */
public class NetworkDiagnostics {
    
    private static final String TAG = "NetworkDiagnostics";
    
    // ==================== 诊断结果类 ====================
    
    /**
     * 诊断结果
     */
    public static class DiagnosticResult {
        public final boolean success;
        public final String message;
        public final String details;
        public final long responseTime;
        
        public DiagnosticResult(boolean success, String message, String details, long responseTime) {
            this.success = success;
            this.message = message;
            this.details = details;
            this.responseTime = responseTime;
        }
        
        @Override
        public String toString() {
            return String.format("[%s] %s (耗时: %dms)\n详情: %s", 
                               success ? "成功" : "失败", message, responseTime, details);
        }
    }
    
    /**
     * 网络信息
     */
    public static class NetworkInfo {
        public final String localIp;
        public final String wifiSSID;
        public final String networkType;
        public final boolean isConnected;
        public final String gateway;
        
        public NetworkInfo(String localIp, String wifiSSID, String networkType, boolean isConnected, String gateway) {
            this.localIp = localIp;
            this.wifiSSID = wifiSSID;
            this.networkType = networkType;
            this.isConnected = isConnected;
            this.gateway = gateway;
        }
        
        @Override
        public String toString() {
            return String.format("本地IP: %s\nWiFi: %s\n网络类型: %s\n连接状态: %s\n网关: %s",
                               localIp, wifiSSID, networkType, isConnected ? "已连接" : "未连接", gateway);
        }
    }
    
    // ==================== 公共接口 ====================
    
    /**
     * 获取网络信息
     * @param context Android上下文
     * @return 网络信息
     */
    public static NetworkInfo getNetworkInfo(Context context) {
        String localIp = getLocalIpAddress();
        String wifiSSID = getWifiSSID(context);
        String networkType = getNetworkType(context);
        boolean isConnected = isNetworkConnected(context);
        String gateway = getGatewayAddress(context);
        
        return new NetworkInfo(localIp, wifiSSID, networkType, isConnected, gateway);
    }
    
    /**
     * 测试MQTT连接
     * @param host 主机地址
     * @param port 端口
     * @param timeoutMs 超时时间（毫秒）
     * @return 诊断结果
     */
    public static DiagnosticResult testMqttConnection(String host, int port, int timeoutMs) {
        long startTime = System.currentTimeMillis();
        
        try {
            Socket socket = new Socket();
            socket.connect(new java.net.InetSocketAddress(host, port), timeoutMs);
            socket.close();
            
            long responseTime = System.currentTimeMillis() - startTime;
            return new DiagnosticResult(true, "MQTT连接测试成功", 
                                      String.format("服务器 %s:%d 可达", host, port), responseTime);
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            return new DiagnosticResult(false, "MQTT连接测试失败", 
                                      String.format("无法连接到 %s:%d - %s", host, port, e.getMessage()), responseTime);
        }
    }
    
    /**
     * Ping测试
     * @param host 主机地址
     * @return 诊断结果
     */
    public static DiagnosticResult pingHost(String host) {
        long startTime = System.currentTimeMillis();
        
        try {
            InetAddress address = InetAddress.getByName(host);
            boolean reachable = address.isReachable(5000);
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            if (reachable) {
                return new DiagnosticResult(true, "Ping测试成功", 
                                          String.format("主机 %s 可达", host), responseTime);
            } else {
                return new DiagnosticResult(false, "Ping测试失败", 
                                          String.format("主机 %s 不可达", host), responseTime);
            }
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            return new DiagnosticResult(false, "Ping测试失败", 
                                      String.format("无法解析主机 %s - %s", host, e.getMessage()), responseTime);
        }
    }
    
    /**
     * 扫描网段中的活跃主机
     * @param networkPrefix 网络前缀 (例如: "192.168.1")
     * @param startRange 起始范围
     * @param endRange 结束范围
     * @return 活跃主机列表
     */
    public static List<String> scanActiveHosts(String networkPrefix, int startRange, int endRange) {
        List<String> activeHosts = new ArrayList<>();
        ExecutorService executor = Executors.newFixedThreadPool(20);
        List<Future<String>> futures = new ArrayList<>();
        
        for (int i = startRange; i <= endRange; i++) {
            final String host = networkPrefix + "." + i;
            futures.add(executor.submit(() -> {
                try {
                    InetAddress address = InetAddress.getByName(host);
                    if (address.isReachable(1000)) {
                        return host;
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
                return null;
            }));
        }
        
        for (Future<String> future : futures) {
            try {
                String result = future.get(2, TimeUnit.SECONDS);
                if (result != null) {
                    activeHosts.add(result);
                }
            } catch (Exception e) {
                // 忽略超时和异常
            }
        }
        
        executor.shutdown();
        return activeHosts;
    }
    
    /**
     * 综合网络诊断
     * @param context Android上下文
     * @param mqttHost MQTT服务器地址
     * @param mqttPort MQTT端口
     * @return 诊断结果列表
     */
    public static List<DiagnosticResult> comprehensiveDiagnosis(Context context, String mqttHost, int mqttPort) {
        List<DiagnosticResult> results = new ArrayList<>();
        
        // 1. 网络连接检查
        boolean networkConnected = isNetworkConnected(context);
        results.add(new DiagnosticResult(networkConnected, "网络连接检查", 
                                       networkConnected ? "设备已连接到网络" : "设备未连接到网络", 0));
        
        if (!networkConnected) {
            return results; // 如果网络未连接，直接返回
        }
        
        // 2. 本地IP获取
        String localIp = getLocalIpAddress();
        results.add(new DiagnosticResult(localIp != null, "本地IP获取", 
                                       localIp != null ? "本地IP: " + localIp : "无法获取本地IP", 0));
        
        // 3. DNS解析测试
        DiagnosticResult dnsResult = testDNSResolution("www.baidu.com");
        results.add(dnsResult);
        
        // 4. 网关连通性测试
        String gateway = getGatewayAddress(context);
        if (gateway != null) {
            DiagnosticResult gatewayResult = pingHost(gateway);
            results.add(gatewayResult);
        }
        
        // 5. MQTT服务器连通性测试
        if (mqttHost != null) {
            DiagnosticResult mqttPing = pingHost(mqttHost);
            results.add(mqttPing);
            
            DiagnosticResult mqttConnect = testMqttConnection(mqttHost, mqttPort, 5000);
            results.add(mqttConnect);
        }
        
        return results;
    }
    
    // ==================== 私有工具方法 ====================
    
    /**
     * 获取本地IP地址
     */
    private static String getLocalIpAddress() {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                List<InetAddress> addrs = Collections.list(intf.getInetAddresses());
                for (InetAddress addr : addrs) {
                    if (!addr.isLoopbackAddress()) {
                        String sAddr = addr.getHostAddress();
                        if (sAddr != null && sAddr.indexOf(':') < 0) {
                            return sAddr;
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取本地IP失败", e);
        }
        return null;
    }
    
    /**
     * 获取WiFi SSID
     */
    private static String getWifiSSID(Context context) {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                if (wifiInfo != null) {
                    return wifiInfo.getSSID();
                }
            }
        } catch (SecurityException e) {
            Log.w(TAG, "无法访问WiFi SSID，权限不足: " + e.getMessage());
            return "权限不足";
        } catch (Exception e) {
            Log.e(TAG, "获取WiFi SSID失败", e);
        }
        return "未知";
    }
    
    /**
     * 获取网络类型
     */
    private static String getNetworkType(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            android.net.NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork != null) {
                return activeNetwork.getTypeName();
            }
        }
        return "未知";
    }
    
    /**
     * 检查网络连接状态
     */
    private static boolean isNetworkConnected(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            android.net.NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
        }
        return false;
    }
    
    /**
     * 获取网关地址
     */
    private static String getGatewayAddress(Context context) {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                int gateway = wifiManager.getDhcpInfo().gateway;
                return String.format("%d.%d.%d.%d",
                    (gateway & 0xff),
                    (gateway >> 8 & 0xff),
                    (gateway >> 16 & 0xff),
                    (gateway >> 24 & 0xff));
            }
        } catch (SecurityException e) {
            Log.w(TAG, "无法访问网关信息，权限不足: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "获取网关地址失败", e);
        }
        return null;
    }
    
    /**
     * DNS解析测试
     */
    private static DiagnosticResult testDNSResolution(String hostname) {
        long startTime = System.currentTimeMillis();
        
        try {
            InetAddress address = InetAddress.getByName(hostname);
            long responseTime = System.currentTimeMillis() - startTime;
            
            return new DiagnosticResult(true, "DNS解析测试", 
                                      String.format("%s 解析为 %s", hostname, address.getHostAddress()), responseTime);
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            return new DiagnosticResult(false, "DNS解析测试", 
                                      String.format("无法解析 %s - %s", hostname, e.getMessage()), responseTime);
        }
    }
}
