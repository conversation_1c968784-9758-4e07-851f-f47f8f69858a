package com.gzcec.xrandroidclient.communication.client;

import android.content.Context;
import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.gzcec.xrandroidclient.communication.messages.base.BaseMessage;
import com.gzcec.xrandroidclient.communication.messages.game.*;
import com.gzcec.xrandroidclient.communication.messages.device.*;
import com.gzcec.xrandroidclient.communication.messages.system.*;
import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.gzcec.xrandroidclient.communication.data.GameInfo;
import com.gzcec.xrandroidclient.communication.MqttConnectionPool;
import com.gzcec.xrandroidclient.communication.PooledMqttConnection;
import com.gzcec.xrandroidclient.utils.MqttConnectionDiagnostics;
import com.gzcec.xrandroidclient.utils.XRLog;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Android端MQTT游戏控制客户端
 * 负责与PC服务端进行游戏控制通信
 */
public class MqttGameClient {
    private static final String TAG = "MqttGameClient";
    
    // MQTT连接参数
    private static final int CONNECTION_TIMEOUT = 10;
    private static final int KEEP_ALIVE_INTERVAL = 30;
    private static final int QOS_LEVEL = 1;
    
    // 客户端实例和连接池
    private MqttClient mqttClient;
    private PooledMqttConnection pooledConnection;
    private MqttConnectionPool connectionPool;
    private String brokerUrl;
    private String clientId;
    private Gson gson;
    private Context context;

    // 连接状态
    private boolean isConnected = false;
    
    // 监听器
    private GameControlListener gameControlListener;
    private DeviceStatusListener deviceStatusListener;
    private ConnectionListener connectionListener;

    // 诊断工具
    private MqttConnectionDiagnostics diagnostics;
    
    // 等待响应的请求映射
    private Map<String, ResponseCallback> pendingRequests = new ConcurrentHashMap<>();
    
    /**
     * 构造函数
     * @param context Android上下文
     * @param brokerHost MQTT Broker主机地址
     * @param brokerPort MQTT Broker端口
     */
    public MqttGameClient(Context context, String brokerHost, int brokerPort) {
        this.context = context;
        this.brokerUrl = "tcp://" + brokerHost + ":" + brokerPort;
        this.clientId = "AndroidGameClient_" + UUID.randomUUID().toString().substring(0, 8);
        this.gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSXXX")  // 支持ISO 8601格式，包含微秒和时区
                .create();

        // 初始化连接池
        this.connectionPool = MqttConnectionPool.getInstance();
        this.connectionPool.initialize(context, brokerUrl, "AndroidGameClient");

        // 初始化诊断工具
        this.diagnostics = new MqttConnectionDiagnostics(context);
    }
    
    // 接口定义
    public interface GameControlListener {
        void onGameListResponse(GameListResponseMessage response);
        void onGameStartResponse(GameStartResponseMessage response);
        void onGameStopResponse(GameStopResponseMessage response);
        void onGameProgressUpdate(GameProgressUpdateMessage update);
    }
    
    public interface DeviceStatusListener {
        void onDeviceListResponse(DeviceListResponseMessage response);
        void onDeviceStatusUpdate(DeviceStatusUpdateMessage update);
    }
    
    public interface ConnectionListener {
        void onConnected();
        void onDisconnected();
        void onConnectionError(String error);
    }
    
    public interface ResponseCallback {
        void onResponse(BaseMessage response);
        void onTimeout();
    }
    
    // 设置监听器
    public void setGameControlListener(GameControlListener listener) {
        this.gameControlListener = listener;
    }
    
    public void setDeviceStatusListener(DeviceStatusListener listener) {
        this.deviceStatusListener = listener;
    }
    
    public void setConnectionListener(ConnectionListener listener) {
        this.connectionListener = listener;
    }
    
    /**
     * 连接到MQTT Broker (使用连接池优化)
     */
    public void connect() {
        XRLog.enter(TAG, "connect");
        long startTime = System.currentTimeMillis();

        try {
            if (isConnected && pooledConnection != null &&
                pooledConnection.getClient().isConnected()) {
                XRLog.w(TAG, "MQTT客户端已连接，跳过重复连接");
                return;
            }

            // 从连接池获取连接
            pooledConnection = connectionPool.getConnection();
            if (pooledConnection == null) {
                throw new Exception("无法从连接池获取MQTT连接");
            }

            // 获取异步客户端并转换为同步客户端
            MqttAsyncClient asyncClient = pooledConnection.getClient();
            mqttClient = new MqttClient(brokerUrl, pooledConnection.getClientId(), new MemoryPersistence());

            MqttConnectOptions options = new MqttConnectOptions();
            options.setConnectionTimeout(CONNECTION_TIMEOUT);
            options.setKeepAliveInterval(KEEP_ALIVE_INTERVAL);
            options.setCleanSession(true);
            options.setAutomaticReconnect(true);

            mqttClient.setCallback(new MqttCallbackExtended() {
                @Override
                public void connectComplete(boolean reconnect, String serverURI) {
                    XRLog.i(TAG, "🔗 MQTT连接成功: " + serverURI + (reconnect ? " (重连)" : " (首次连接)"));
                    XRLog.i(TAG, "📊 连接池状态: " + connectionPool.getPoolStatus());
                    XRLog.network(TAG, serverURI, "MQTT_CONNECT", 200);
                    isConnected = true;
                    subscribeToTopics();
                    if (connectionListener != null) {
                        connectionListener.onConnected();
                    }
                }

                @Override
                public void connectionLost(Throwable cause) {
                    XRLog.w(TAG, "❌ MQTT连接丢失", cause);
                    XRLog.network(TAG, brokerUrl, "MQTT_DISCONNECT", 0);
                    isConnected = false;

                    // 连接丢失时，将连接归还到池中进行重新评估
                    if (pooledConnection != null) {
                        connectionPool.returnConnection(pooledConnection);
                        pooledConnection = null;
                    }

                    if (connectionListener != null) {
                        connectionListener.onDisconnected();
                    }
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    handleIncomingMessage(topic, new String(message.getPayload()));
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    // 消息发送完成
                }
            });

            // 如果客户端未连接，则连接
            if (!mqttClient.isConnected()) {
                mqttClient.connect(options);
            }

        } catch (Exception e) {
            Log.e(TAG, "MQTT连接失败", e);

            // 连接失败时清理资源
            if (pooledConnection != null) {
                connectionPool.returnConnection(pooledConnection);
                pooledConnection = null;
            }

            // 运行连接诊断
            runConnectionDiagnostics(e.getMessage());

            if (connectionListener != null) {
                connectionListener.onConnectionError(e.getMessage());
            }
        }
    }

    /**
     * 订阅主题
     */
    private void subscribeToTopics() {
        try {
            String[] topics = {
                "xr_system/game_control/list_response",
                "xr_system/game_control/start_response",
                "xr_system/game_control/stop_response",
                "xr_system/game_control/progress_update",
                "xr_system/device_management/list_response",
                "xr_system/device_management/status_update",
                "xr_system/device_management/status_response",
                "xr_system/system_status/heartbeat",
                "xr_system/system_status/error"
            };

            for (String topic : topics) {
                mqttClient.subscribe(topic, QOS_LEVEL);
                Log.d(TAG, "订阅主题: " + topic);
            }
        } catch (Exception e) {
            Log.e(TAG, "订阅主题失败", e);
        }
    }

    /**
     * 处理接收到的消息
     */
    private void handleIncomingMessage(String topic, String payload) {
        try {
            // 对设备相关的大消息进行简化日志输出
            if ("xr_system/device_management/status_update".equals(topic) ||
                "xr_system/device_management/list_response".equals(topic)) {
                Log.d(TAG, "收到消息 - 主题: " + topic + ", 内容长度: " + payload.length() + "字符");
            } else {
                Log.d(TAG, "收到消息 - 主题: " + topic + ", 内容: " + payload);
            }

            if (topic.contains("game_control/list_response")) {
                GameListResponseMessage response = gson.fromJson(payload, GameListResponseMessage.class);
                if (gameControlListener != null) {
                    gameControlListener.onGameListResponse(response);
                }
                // 处理回调
                handleResponseCallback(response);
            } else if (topic.contains("game_start_response")) {
                GameStartResponseMessage response = gson.fromJson(payload, GameStartResponseMessage.class);
                if (gameControlListener != null) {
                    gameControlListener.onGameStartResponse(response);
                }
            } else if (topic.contains("game_stop_response")) {
                GameStopResponseMessage response = gson.fromJson(payload, GameStopResponseMessage.class);
                if (gameControlListener != null) {
                    gameControlListener.onGameStopResponse(response);
                }
            } else if (topic.contains("progress_update")) {
                GameProgressUpdateMessage update = gson.fromJson(payload, GameProgressUpdateMessage.class);
                if (gameControlListener != null) {
                    gameControlListener.onGameProgressUpdate(update);
                }
            } else if (topic.contains("list_response")) {
                DeviceListResponseMessage response = gson.fromJson(payload, DeviceListResponseMessage.class);
                if (deviceStatusListener != null) {
                    deviceStatusListener.onDeviceListResponse(response);
                }
                // 处理回调
                handleResponseCallback(response);
            } else if (topic.contains("status_update")) {
                DeviceStatusUpdateMessage update = gson.fromJson(payload, DeviceStatusUpdateMessage.class);
                if (deviceStatusListener != null) {
                    deviceStatusListener.onDeviceStatusUpdate(update);
                }
            } else if (topic.contains("status_response")) {
                DeviceStatusUpdateMessage response = gson.fromJson(payload, DeviceStatusUpdateMessage.class);
                if (deviceStatusListener != null) {
                    deviceStatusListener.onDeviceStatusUpdate(response);
                }
                // 处理回调
                handleResponseCallback(response);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理消息失败", e);
        }
    }

    /**
     * 处理响应回调
     */
    private void handleResponseCallback(BaseMessage response) {
        try {
            String requestId = null;

            // 根据消息类型获取请求ID
            if (response instanceof DeviceListResponseMessage) {
                requestId = ((DeviceListResponseMessage) response).getRequestId();
            } else if (response instanceof DeviceStatusUpdateMessage) {
                // DeviceStatusUpdateMessage可能需要添加requestId字段
                // 这里暂时使用messageId
                requestId = response.getMessageId();
            }

            if (requestId != null) {
                ResponseCallback callback = pendingRequests.remove(requestId);
                if (callback != null) {
                    callback.onResponse(response);
                    Log.d(TAG, "响应回调已处理: " + requestId);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "处理响应回调失败", e);
        }
    }

    /**
     * 断开连接 (优化版本 - 归还连接到池中)
     */
    public void disconnect() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                Log.i(TAG, "MQTT连接已断开");
            }

            // 将连接归还到连接池
            if (pooledConnection != null) {
                connectionPool.returnConnection(pooledConnection);
                Log.i(TAG, "连接已归还到连接池: " + pooledConnection.getClientId());
                pooledConnection = null;
            }

            isConnected = false;

        } catch (Exception e) {
            Log.e(TAG, "断开MQTT连接失败", e);

            // 即使断开失败，也要尝试归还连接
            if (pooledConnection != null) {
                connectionPool.returnConnection(pooledConnection);
                pooledConnection = null;
            }
        }
    }

    /**
     * 运行连接诊断
     */
    private void runConnectionDiagnostics(String errorMessage) {
        if (diagnostics == null) {
            return;
        }

        // 从brokerUrl中提取主机和端口
        String host = extractHostFromUrl(brokerUrl);
        int port = extractPortFromUrl(brokerUrl);

        Log.d(TAG, "开始MQTT连接诊断，错误信息: " + errorMessage);

        diagnostics.diagnoseMqttConnection(host, port, new MqttConnectionDiagnostics.DiagnosticsCallback() {
            @Override
            public void onResult(MqttConnectionDiagnostics.DiagnosticsResult result) {
                Log.w(TAG, "=== MQTT连接诊断报告 ===");
                Log.w(TAG, result.toString());
                Log.w(TAG, "=== 解决建议 ===");
                Log.w(TAG, result.suggestions);
                Log.w(TAG, "========================");
            }
        });
    }

    /**
     * 从URL中提取主机名
     */
    private String extractHostFromUrl(String url) {
        try {
            // 移除协议前缀 tcp://
            String hostPort = url.replace("tcp://", "");
            // 分割主机和端口
            String[] parts = hostPort.split(":");
            return parts[0];
        } catch (Exception e) {
            Log.e(TAG, "提取主机名失败: " + url, e);
            return "localhost";
        }
    }

    /**
     * 从URL中提取端口
     */
    private int extractPortFromUrl(String url) {
        try {
            // 移除协议前缀 tcp://
            String hostPort = url.replace("tcp://", "");
            // 分割主机和端口
            String[] parts = hostPort.split(":");
            if (parts.length > 1) {
                return Integer.parseInt(parts[1]);
            }
            return 1883; // 默认MQTT端口
        } catch (Exception e) {
            Log.e(TAG, "提取端口失败: " + url, e);
            return 1883;
        }
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * 请求启动游戏
     * @param gameInfo 游戏信息
     * @param selectedDeviceIds 选中的设备ID列表
     * @param callback 响应回调
     */
    public void requestStartGame(GameInfo gameInfo, List<String> selectedDeviceIds, ResponseCallback callback) {
        try {
            if (!isConnected()) {
                Log.w(TAG, "MQTT未连接，无法发送游戏启动请求");
                if (callback != null) {
                    callback.onTimeout();
                }
                return;
            }

            // 构建游戏启动请求消息
            GameStartRequestMessage request = new GameStartRequestMessage();
            request.setSelectedGame(gameInfo);
            request.setSelectedDeviceIds(selectedDeviceIds);
            request.setMessageId(UUID.randomUUID().toString());
            request.setTimestamp(new Date());

            String payload = gson.toJson(request);
            String topic = "xr_system/game_control/start_request";

            // 保存回调以便响应时调用
            if (callback != null) {
                pendingRequests.put(request.getMessageId(), callback);
            }

            // 发布消息
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(QOS_LEVEL);
            mqttClient.publish(topic, message);

            Log.d(TAG, "游戏启动请求已发送: " + payload);

        } catch (Exception e) {
            Log.e(TAG, "发送游戏启动请求失败", e);
            if (callback != null) {
                callback.onTimeout();
            }
        }
    }

    /**
     * 请求停止游戏
     * @param gameInfo 游戏信息
     * @param selectedDeviceIds 选中的设备ID列表
     * @param callback 响应回调
     */
    public void requestStopGame(GameInfo gameInfo, List<String> selectedDeviceIds, ResponseCallback callback) {
        try {
            if (!isConnected()) {
                Log.w(TAG, "MQTT未连接，无法发送游戏停止请求");
                if (callback != null) {
                    callback.onTimeout();
                }
                return;
            }

            // 构建游戏停止请求消息
            GameStopRequestMessage request = new GameStopRequestMessage();
            request.setSelectedGame(gameInfo);
            request.setSelectedDeviceIds(selectedDeviceIds);
            request.setMessageId(UUID.randomUUID().toString());
            request.setTimestamp(new Date());

            String payload = gson.toJson(request);
            String topic = "xr_system/game_control/stop_request";

            // 保存回调以便响应时调用
            if (callback != null) {
                pendingRequests.put(request.getMessageId(), callback);
            }

            // 发布消息
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(QOS_LEVEL);
            mqttClient.publish(topic, message);

            Log.d(TAG, "游戏停止请求已发送: " + payload);

        } catch (Exception e) {
            Log.e(TAG, "发送游戏停止请求失败", e);
            if (callback != null) {
                callback.onTimeout();
            }
        }
    }

    /**
     * 请求设备列表
     * @param onlineOnly 是否只获取在线设备
     * @param callback 响应回调
     */
    public void requestDeviceList(boolean onlineOnly, ResponseCallback callback) {
        try {
            if (!isConnected()) {
                Log.w(TAG, "MQTT未连接，无法发送设备列表请求");
                if (callback != null) {
                    callback.onTimeout();
                }
                return;
            }

            // 构建设备列表请求消息
            DeviceListRequestMessage request = new DeviceListRequestMessage();
            request.setOnlineOnly(onlineOnly);
            request.setMessageId(UUID.randomUUID().toString());
            request.setTimestamp(new Date());

            String payload = gson.toJson(request);
            String topic = "xr_system/device_management/list_request";

            // 保存回调以便响应时调用
            if (callback != null) {
                pendingRequests.put(request.getMessageId(), callback);
            }

            // 发布消息
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(QOS_LEVEL);
            mqttClient.publish(topic, message);

            Log.d(TAG, "设备列表请求已发送: " + payload);

        } catch (Exception e) {
            Log.e(TAG, "发送设备列表请求失败", e);
            if (callback != null) {
                callback.onTimeout();
            }
        }
    }

    /**
     * 请求设备列表（重载方法，支持DeviceListRequestMessage）
     */
    public void requestDeviceList(DeviceListRequestMessage request, ResponseCallback callback) {
        try {
            if (!isConnected()) {
                Log.w(TAG, "MQTT未连接，无法发送设备列表请求");
                if (callback != null) {
                    callback.onTimeout();
                }
                return;
            }

            String payload = gson.toJson(request);
            String topic = "xr_system/device_management/list_request";

            // 保存回调以便响应时调用
            if (callback != null) {
                pendingRequests.put(request.getRequestId(), callback);
            }

            // 发布消息
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(QOS_LEVEL);
            mqttClient.publish(topic, message);

            Log.d(TAG, "设备列表请求已发送: " + request.getRequestId());

        } catch (Exception e) {
            Log.e(TAG, "发送设备列表请求失败", e);
            if (callback != null) {
                callback.onTimeout();
            }
        }
    }

    /**
     * 请求设备状态更新
     */
    public void requestDeviceStatus(DeviceStatusRequestMessage request, ResponseCallback callback) {
        try {
            if (!isConnected()) {
                Log.w(TAG, "MQTT未连接，无法发送设备状态请求");
                if (callback != null) {
                    callback.onTimeout();
                }
                return;
            }

            String payload = gson.toJson(request);
            String topic = "xr_system/device_management/status_request";

            // 保存回调以便响应时调用
            if (callback != null) {
                pendingRequests.put(request.getRequestId(), callback);
            }

            // 发布消息
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(QOS_LEVEL);
            mqttClient.publish(topic, message);

            Log.d(TAG, "设备状态请求已发送: " + request.getRequestId());

        } catch (Exception e) {
            Log.e(TAG, "发送设备状态请求失败", e);
            if (callback != null) {
                callback.onTimeout();
            }
        }
    }

    /**
     * 请求游戏列表
     */
    public void requestGameList(boolean includeDisabled, ResponseCallback callback) {
        XRLog.enter(TAG, "requestGameList");

        try {
            if (!isConnected()) {
                XRLog.w(TAG, "MQTT未连接，无法发送游戏列表请求");
                if (callback != null) {
                    callback.onTimeout();
                }
                return;
            }

            GameListRequestMessage request = new GameListRequestMessage();
            request.setRequestId("game_list_" + System.currentTimeMillis());
            request.setMessageId(UUID.randomUUID().toString());
            request.setTimestamp(new Date());
            request.setIncludeDisabled(includeDisabled);

            String payload = gson.toJson(request);
            String topic = "xr_system/game_control/list_request";

            // 保存回调以便响应时调用
            if (callback != null) {
                pendingRequests.put(request.getRequestId(), callback);
            }

            // 发布消息
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(QOS_LEVEL);
            mqttClient.publish(topic, message);

            XRLog.mqtt(TAG, topic, "发送", request.getRequestId());
            XRLog.d(TAG, "游戏列表请求已发送: " + request.getRequestId());

        } catch (Exception e) {
            XRLog.e(TAG, "发送游戏列表请求失败", e);
            if (callback != null) {
                callback.onTimeout();
            }
        }

        XRLog.exit(TAG, "requestGameList");
    }
}
