<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?android:windowBackground"
    tools:context=".MainActivity">

    <!-- 连接状态栏 -->
    <LinearLayout
        android:id="@+id/connection_status_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@color/status_bar_background"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tv_connection_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="连接状态: 检查中..."
            android:textColor="@color/status_text_color"
            android:textSize="12sp" />

        <Button
            android:id="@+id/btn_reconnect"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="重连"
            android:textSize="10sp"
            android:minWidth="60dp"
            android:background="@drawable/btn_reconnect_background"
            android:textColor="@color/white" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/background_dark" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/bottom_nav_dark"
        app:menu="@menu/bottom_nav_menu"
        app:itemIconTint="@color/bottom_nav_item_color"
        app:itemTextColor="@color/bottom_nav_item_color" />

</LinearLayout> 