﻿using Jskj.XRSystem.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using XRSvc.CustomControl;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 游戏列表, key=GameID
        /// </summary>
        private readonly Dictionary<int, GameInfo> GameList;

        public ObservableCollection<GameInfo> GameListValues { get; set; }

        public ICommand SaveGameNameCommand { get; }

        public MainWindow()
        {
            InitializeComponent();
            SaveGameNameCommand = new RelayCommand<GameInfo>(SaveGameName);
        }

        public MainWindow(List<GameInfo> gameList)
        {
            this.GameList = gameList.ToDictionary(g => g.ID, g => g);
            this.GameListValues = new ObservableCollection<GameInfo>(gameList);
            SaveGameNameCommand = new RelayCommand<GameInfo>(SaveGameName);
            DataContext = this;
            InitializeComponent();
        }

        private void SaveGameName(GameInfo gameInfo)
        {
            if (gameInfo != null)
            {
                MessageBox.Show($"游戏名称已修改为: {gameInfo.Name}", "修改成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}

