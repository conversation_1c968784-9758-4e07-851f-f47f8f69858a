﻿using System;
using System.Collections.Generic;
using XRSvc.DataPack;

namespace XRSvc.Communication
{
    /// <summary>
    /// 消息基类
    /// </summary>
    public abstract class BaseMessage
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 发送者ID
        /// </summary>
        public string SenderId { get; set; }

        /// <summary>
        /// 接收者ID
        /// </summary>
        public string ReceiverId { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 消息类型 - 与Android端兼容
        /// </summary>
        public string Type { get; set; }
    }



    /// <summary>
    /// 设备列表请求消息
    /// </summary>
    public class DeviceListRequestMessage : BaseMessage
    {
        /// <summary>
        /// 是否只返回在线设备
        /// </summary>
        public bool OnlineOnly { get; set; }

        /// <summary>
        /// 设备类型过滤器
        /// </summary>
        public string DeviceTypeFilter { get; set; }
    }

    /// <summary>
    /// 设备列表响应消息
    /// </summary>
    public class DeviceListResponseMessage : BaseMessage
    {
        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 设备列表
        /// </summary>
        public List<DeviceInfo> Devices { get; set; } = new List<DeviceInfo>();

        /// <summary>
        /// 总设备数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 在线设备数量
        /// </summary>
        public int OnlineCount { get; set; }
    }

    /// <summary>
    /// 设备状态更新消息
    /// </summary>
    public class DeviceStatusUpdateMessage : BaseMessage
    {
        /// <summary>
        /// 设备列表
        /// </summary>
        public List<DeviceInfo> Devices { get; set; } = new List<DeviceInfo>();
    }

    /// <summary>
    /// 连接状态消息
    /// </summary>
    public class ConnectionStatusMessage : BaseMessage
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 客户端类型
        /// </summary>
        public string ClientType { get; set; }
    }



    /// <summary>
    /// 游戏列表请求消息
    /// </summary>
    public class GameListRequestMessage : BaseMessage
    {
        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 是否包含禁用的游戏
        /// </summary>
        public bool IncludeDisabled { get; set; }

        /// <summary>
        /// 游戏分类过滤器
        /// </summary>
        public string Category { get; set; }
    }

    /// <summary>
    /// 游戏列表响应消息
    /// </summary>
    public class GameListResponseMessage : BaseMessage
    {
        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 游戏列表
        /// </summary>
        public List<GameInfo> Games { get; set; } = new List<GameInfo>();

        /// <summary>
        /// 游戏总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 游戏启动请求消息
    /// </summary>
    public class GameStartRequestMessage : BaseMessage
    {
        /// <summary>
        /// 选中的游戏
        /// </summary>
        public GameInfo SelectedGame { get; set; }

        /// <summary>
        /// 选中的设备ID列表
        /// </summary>
        public List<string> SelectedDeviceIds { get; set; } = new List<string>();
    }

    /// <summary>
    /// 游戏停止请求消息
    /// </summary>
    public class GameStopRequestMessage : BaseMessage
    {
        /// <summary>
        /// 选中的游戏
        /// </summary>
        public GameInfo SelectedGame { get; set; }

        /// <summary>
        /// 选中的设备ID列表
        /// </summary>
        public List<string> SelectedDeviceIds { get; set; } = new List<string>();

        /// <summary>
        /// 是否强制停止
        /// </summary>
        public bool ForceStop { get; set; } = false;
    }

    /// <summary>
    /// 设备启动结果
    /// </summary>
    public class DeviceStartResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// 游戏启动响应消息
    /// </summary>
    public class GameStartResponseMessage : BaseMessage
    {
        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 设备启动结果列表
        /// </summary>
        public List<DeviceStartResult> Results { get; set; } = new List<DeviceStartResult>();
    }

    /// <summary>
    /// 游戏停止响应消息
    /// </summary>
    public class GameStopResponseMessage : BaseMessage
    {
        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 设备停止结果列表
        /// </summary>
        public List<DeviceStopResult> Results { get; set; } = new List<DeviceStopResult>();
    }

    /// <summary>
    /// 设备停止结果
    /// </summary>
    public class DeviceStopResult
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime StopTime { get; set; }
    }

    /// <summary>
    /// 心跳消息
    /// </summary>
    public class HeartbeatMessage : BaseMessage
    {
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 附加信息
        /// </summary>
        public Dictionary<string, object> Info { get; set; } = new Dictionary<string, object>();
    }
}
