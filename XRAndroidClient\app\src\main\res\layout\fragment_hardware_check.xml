<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#181A20"
    android:paddingLeft="8dp"
    android:paddingRight="8dp"
    android:paddingTop="32dp"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="场地硬件检测"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:paddingTop="8dp"
        android:paddingBottom="12dp"
        android:background="@drawable/bg_title_gradient"
        android:elevation="4dp"
        android:layout_marginBottom="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_horizontal"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        android:paddingTop="16dp"
        android:paddingBottom="12dp"
        android:background="@drawable/bg_section_round"
        android:layout_marginBottom="12dp"
        android:elevation="2dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp">

        <Button
            android:id="@+id/btn_all_start"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:text="一键启动"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/btn_green_selector"/>

        <Button
            android:id="@+id/btn_all_stop"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:text="一键关闭"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:background="@drawable/btn_red_selector"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/bg_section_round">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>
</LinearLayout>