﻿using System.Windows;
using System.Windows.Controls;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace XRSvc.CustomControl
{
    /// <summary>
    /// 游戏样式控件，支持选中状态管理
    /// </summary>
    public partial class GameStyle : UserControl, INotifyPropertyChanged
    {
        #region Events

        /// <summary>
        /// 选中状态改变事件
        /// </summary>
        public event RoutedEventHandler SelectionChanged;

        #endregion

        #region Properties

        private bool _isSelected;
        /// <summary>
        /// 是否被选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();
                    UpdateRadioButtonState();
                }
            }
        }

        private int _gameId;
        /// <summary>
        /// 游戏ID
        /// </summary>
        public int GameId
        {
            get => _gameId;
            set
            {
                _gameId = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// 构造函数，初始化控件
        /// </summary>
        public GameStyle()
        {
            InitializeComponent();
            DataContextChanged += GameStyle_DataContextChanged;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// 数据上下文改变事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void GameStyle_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.NewValue != null)
            {
                // 从数据上下文获取游戏ID
                var gameSource = e.NewValue as DataSource.GameSource;
                if (gameSource != null)
                {
                    GameId = gameSource.ID;
                    // 检查是否应该被选中
                    CheckSelectionState();
                }
            }
        }

        /// <summary>
        /// 单选按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void RadioButton_GameName_Click(object sender, RoutedEventArgs e)
        {
            if (!IsSelected)
            {
                IsSelected = true;
                // 通知主窗口更新选中的游戏
                var mainWindow = System.Windows.Application.Current.MainWindow as MainWindow;
                if (mainWindow?.GameViewModel != null)
                {
                    var gameSource = DataContext as DataSource.GameSource;
                    if (gameSource != null)
                    {
                        mainWindow.GameViewModel.SelectedGame = gameSource;
                        mainWindow.SelectedGameId = gameSource.ID;
                    }
                }
                SelectionChanged?.Invoke(this, e);
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// 更新单选按钮状态
        /// </summary>
        private void UpdateRadioButtonState()
        {
            if (RadioButton_GameName != null)
            {
                RadioButton_GameName.IsChecked = IsSelected;
            }
        }

        /// <summary>
        /// 检查选中状态
        /// </summary>
        private void CheckSelectionState()
        {
            // 获取主窗口的GameViewModel
            var mainWindow = System.Windows.Application.Current.MainWindow as MainWindow;
            if (mainWindow?.GameViewModel != null)
            {
                var selectedGame = mainWindow.GameViewModel.SelectedGame;
                if (selectedGame != null && selectedGame.ID == GameId)
                {
                    IsSelected = true;
                }
                else
                {
                    IsSelected = false;
                }
            }
        }

        #endregion

        #region INotifyPropertyChanged

        /// <summary>
        /// 属性改变事件
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 触发属性改变事件
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
