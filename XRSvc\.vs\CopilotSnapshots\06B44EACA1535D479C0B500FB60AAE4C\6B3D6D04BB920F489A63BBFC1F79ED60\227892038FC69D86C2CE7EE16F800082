﻿<UserControl x:Class="XRSvc.CustomControl.GameStyle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:XRSvc.CustomControl"
             mc:Ignorable="d" 
             d:DesignHeight="30" d:DesignWidth="100">
    <Grid x:Name="GamePanel" FocusVisualStyle="{x:Null}" Height="30">
        <RadioButton x:Name="RadioButton_GameName" 
                     Content="{Binding Name}" 
                     Tag="{Binding ID}" 
                     GroupName="GameGroup" 
                     HorizontalAlignment="Left" 
                     VerticalAlignment="Center" 
                     FontSize="12" 
                     Margin="5"/>
    </Grid>
</UserControl>
