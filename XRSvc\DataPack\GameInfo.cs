﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Collections.Generic;

namespace XRSvc.DataPack
{
    public class GameInfo
    {

        #region Filed

        /// <summary>
        /// ID编号
        /// </summary>
        public int ID;

        /// <summary>
        /// 影片名称
        /// </summary>
        public string Name;

        /// <summary>
        /// 影片类别
        /// </summary>
        public int GameCategoryIndex;

        /// <summary>
        /// APK包名
        /// </summary>
        public string PackageName;

        /// <summary>
        /// 视频文件相对路径
        /// </summary>
        public string VideoFilePath;

        /// <summary>
        /// 影片类型
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public GameType GameType;

        /// <summary>
        /// 视频格式
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public VideoFormat VideoFormat;

        /// <summary>
        /// 视频时长，单位：秒
        /// </summary>
        public int Duration;

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int ShowOrder;       

        /// <summary>
        /// 是否显示
        /// </summary>
        public bool IsShow;

        /// <summary>
        /// 是否播放动作
        /// </summary>
        public bool IsPlayAction  = false;

        /// <summary>
        /// 动作文件相对路径
        /// </summary>
        public string ActionFilePath;

        /// <summary>
        /// 是否可用 - 与Android端兼容
        /// </summary>
        [JsonProperty("isAvailable")]
        public bool IsAvailable => IsShow;

        /// <summary>
        /// 是否启用 - 与Android端兼容
        /// </summary>
        [JsonProperty("isEnabled")]
        public bool IsEnabled => IsShow;

        /// <summary>
        /// 游戏版本 - 与Android端兼容
        /// </summary>
        [JsonProperty("version")]
        public string Version => "1.0";

        /// <summary>
        /// 游戏描述 - 与Android端兼容
        /// </summary>
        [JsonProperty("description")]
        public string Description => Name;

        /// <summary>
        /// 图标路径 - 与Android端兼容
        /// </summary>
        [JsonProperty("iconPath")]
        public string IconPath => "";

        /// <summary>
        /// 游戏分类 - 与Android端兼容
        /// </summary>
        [JsonProperty("category")]
        public string Category => GameCategoryIndex.ToString();

        #endregion

    }
}
