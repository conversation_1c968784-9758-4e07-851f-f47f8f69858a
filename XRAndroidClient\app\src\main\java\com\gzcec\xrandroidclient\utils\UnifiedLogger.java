package com.gzcec.xrandroidclient.utils;

import android.util.Log;

/**
 * 统一日志工具类
 * 提供与PC端一致的日志接口和格式
 */
public class UnifiedLogger {
    private static final String DEFAULT_TAG = "XRAndroidClient";
    private static boolean isDebugMode = true;
    
    /**
     * 日志级别枚举 - 与PC端保持一致
     */
    public enum Level {
        DEBUG("D", "DEBUG"),
        INFO("I", "INFO"),
        WARN("W", "WARN"),
        ERROR("E", "ERROR");
        
        private final String androidLevel;
        private final String displayName;
        
        Level(String androidLevel, String displayName) {
            this.androidLevel = androidLevel;
            this.displayName = displayName;
        }
        
        public String getAndroidLevel() {
            return androidLevel;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * 设置调试模式
     */
    public static void setDebugMode(boolean debug) {
        isDebugMode = debug;
    }
    
    /**
     * 统一的日志写入方法 - 与PC端Log.Write保持一致
     */
    public static void write(Level level, String message) {
        write(level, DEFAULT_TAG, message, null);
    }
    
    public static void write(Level level, String tag, String message) {
        write(level, tag, message, null);
    }
    
    public static void write(Level level, String message, Throwable throwable) {
        write(level, DEFAULT_TAG, message, throwable);
    }
    
    public static void write(Level level, String tag, String message, Throwable throwable) {
        // 调试级别的日志只在调试模式下输出
        if (level == Level.DEBUG && !isDebugMode) {
            return;
        }
        
        // 格式化消息，与PC端格式保持一致
        String formattedMessage = formatMessage(level, tag, message);
        
        // 输出到logcat
        switch (level) {
            case DEBUG:
                Log.d(tag, formattedMessage, throwable);
                break;
            case INFO:
                Log.i(tag, formattedMessage, throwable);
                break;
            case WARN:
                Log.w(tag, formattedMessage, throwable);
                break;
            case ERROR:
                Log.e(tag, formattedMessage, throwable);
                break;
        }
        
        // 输出到文件
        LogManager.getInstance().writeLog(level.getAndroidLevel(), tag, formattedMessage, throwable);
    }
    
    /**
     * 格式化消息，与PC端格式保持一致
     */
    private static String formatMessage(Level level, String tag, String message) {
        // PC端格式: [时间] [线程ID] [方法名] | 级别 | 消息
        // Android端简化格式: [线程ID] | 级别 | 消息
        long threadId = Thread.currentThread().getId();
        return String.format("[%08d] | %s | %s", threadId, level.getDisplayName(), message);
    }
    
    // ========== 便捷方法 - 兼容原有XRLog的使用方式 ==========
    
    public static void d(String message) {
        write(Level.DEBUG, message);
    }
    
    public static void d(String tag, String message) {
        write(Level.DEBUG, tag, message);
    }
    
    public static void i(String message) {
        write(Level.INFO, message);
    }
    
    public static void i(String tag, String message) {
        write(Level.INFO, tag, message);
    }
    
    public static void w(String message) {
        write(Level.WARN, message);
    }
    
    public static void w(String tag, String message) {
        write(Level.WARN, tag, message);
    }
    
    public static void w(String message, Throwable throwable) {
        write(Level.WARN, message, throwable);
    }
    
    public static void w(String tag, String message, Throwable throwable) {
        write(Level.WARN, tag, message, throwable);
    }
    
    public static void e(String message) {
        write(Level.ERROR, message);
    }
    
    public static void e(String tag, String message) {
        write(Level.ERROR, tag, message);
    }
    
    public static void e(String message, Throwable throwable) {
        write(Level.ERROR, message, throwable);
    }
    
    public static void e(String tag, String message, Throwable throwable) {
        write(Level.ERROR, tag, message, throwable);
    }
    
    // ========== 专用日志方法 - 保持XRLog的特色功能 ==========
    
    /**
     * 记录方法进入
     */
    public static void enter(String tag, String methodName) {
        write(Level.DEBUG, tag, "🔵 进入方法: " + methodName);
    }
    
    /**
     * 记录方法退出
     */
    public static void exit(String tag, String methodName) {
        write(Level.DEBUG, tag, "🔴 退出方法: " + methodName);
    }
    
    /**
     * 记录方法执行时间
     */
    public static void time(String tag, String methodName, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        write(Level.DEBUG, tag, String.format("⏱️ 方法 %s 执行时间: %d ms", methodName, duration));
    }
    
    /**
     * 记录MQTT消息
     */
    public static void mqtt(String topic, String action, String messageId) {
        mqtt(DEFAULT_TAG, topic, action, messageId);
    }
    
    public static void mqtt(String tag, String topic, String action, String messageId) {
        write(Level.INFO, tag, String.format("📡 MQTT %s: %s [%s]", action, topic, messageId));
    }
    
    /**
     * 记录设备状态变化
     */
    public static void device(String deviceName, String status, String details) {
        device(DEFAULT_TAG, deviceName, status, details);
    }
    
    public static void device(String tag, String deviceName, String status, String details) {
        write(Level.INFO, tag, String.format("📱 设备 %s: %s - %s", deviceName, status, details));
    }
    
    /**
     * 记录用户操作
     */
    public static void user(String action, String details) {
        user(DEFAULT_TAG, action, details);
    }
    
    public static void user(String tag, String action, String details) {
        write(Level.INFO, tag, String.format("👤 用户操作: %s - %s", action, details));
    }
    
    /**
     * 记录网络请求
     */
    public static void network(String url, String method, int responseCode) {
        network(DEFAULT_TAG, url, method, responseCode);
    }
    
    public static void network(String tag, String url, String method, int responseCode) {
        write(Level.INFO, tag, String.format("🌐 网络请求: %s %s -> %d", method, url, responseCode));
    }
    
    /**
     * 记录性能指标
     */
    public static void performance(String metric, long value, String unit) {
        performance(DEFAULT_TAG, metric, value, unit);
    }
    
    public static void performance(String tag, String metric, long value, String unit) {
        write(Level.INFO, tag, String.format("📊 性能指标: %s = %d %s", metric, value, unit));
    }
    
    /**
     * 记录内存使用情况
     */
    public static void memory() {
        memory(DEFAULT_TAG);
    }
    
    public static void memory(String tag) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024; // KB
        long freeMemory = runtime.freeMemory() / 1024;   // KB
        long usedMemory = totalMemory - freeMemory;      // KB
        long maxMemory = runtime.maxMemory() / 1024;     // KB
        
        write(Level.DEBUG, tag, String.format("💾 内存使用: %d/%d KB (最大: %d KB)", 
            usedMemory, totalMemory, maxMemory));
    }
}
