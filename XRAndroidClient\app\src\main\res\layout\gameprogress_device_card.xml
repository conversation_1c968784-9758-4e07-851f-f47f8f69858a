<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    card_view:cardCornerRadius="8dp"
    android:layout_margin="5dp"
    android:background="@drawable/card_border"
    android:foreground="@drawable/device_card_border">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#23272E"
        android:padding="12dp">

        <!-- 设备信息区域 - 左侧 -->
        <LinearLayout
            android:id="@+id/ll_device_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_control_area"
            app:layout_constraintWidth_percent="0.5">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <CheckBox
                    android:id="@+id/cb_device"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1号机"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_device_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:layout_marginStart="8dp"
                    android:text="(8001)" />
            </LinearLayout>

            <!-- 状态信息区 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_game_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="游戏未启动"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/iv_game_status"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="4dp"
                        android:src="@drawable/ic_red_dot" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_server_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未连接游戏服务器"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/iv_server_status"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="4dp"
                        android:src="@drawable/ic_green_dot" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <!-- 控制区域 - 右侧 -->
        <LinearLayout
            android:id="@+id/ll_control_area"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/ll_device_info"
            app:layout_constraintWidth_percent="0.5">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.gzcec.xrandroidclient.BatteryView
                    android:id="@+id/battery_view"
                    android:layout_width="25dp"
                    android:layout_height="20dp" />

                <TextView
                    android:id="@+id/tv_battery_percent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="0%"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_high"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="离线"
                    android:textColor="#FF0000"
                    android:textSize="14sp"
                    android:layout_marginStart="8dp" />

                <ImageView
                    android:id="@+id/iv_high"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginStart="2dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_red_dot" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_button_area"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="12dp"
                    app:layout_constraintEnd_toEndOf="@id/ll_control_area">
                    <Button
                        style="@style/UnifiedButton"
                        android:id="@+id/btn_join"
                        android:layout_width="200dp"
                        android:layout_height="44dp"
                        android:text="加入游戏"
                        android:drawableStart="@android:drawable/ic_media_play"
                        android:layout_marginBottom="8dp" />
                    <Button
                        style="@style/UnifiedButton"
                        android:id="@+id/btn_exit"
                        android:layout_width="200dp"
                        android:layout_height="44dp"
                        android:text="退出游戏"
                        android:drawableStart="@android:drawable/ic_media_play" />
                </LinearLayout>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>