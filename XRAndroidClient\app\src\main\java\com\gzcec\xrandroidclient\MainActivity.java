package com.gzcec.xrandroidclient;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.gzcec.xrandroidclient.GameSetting.GameSettingsFragment;
import com.gzcec.xrandroidclient.hardwarecheck.HardwareCheckFragment;
import com.gzcec.xrandroidclient.gameprogress.GameProgressFragment;
import com.gzcec.xrandroidclient.devicestatus.DeviceStatusFragment;
import com.gzcec.xrandroidclient.communication.MqttCommunicationManager;
import com.gzcec.xrandroidclient.communication.EnhancedConnectionManager;

public class MainActivity extends AppCompatActivity implements MqttCommunicationManager.ConnectionListener {
    private static final String TAG = "MainActivity";

    // Fragment实例缓存，避免重复创建
    private GameProgressFragment gameProgressFragment;
    private DeviceStatusFragment deviceStatusFragment;
    private HardwareCheckFragment hardwareCheckFragment;
    private GameSettingsFragment gameSettingsFragment;

    // UI组件
    private TextView tvConnectionStatus;
    private Button btnReconnect;

    // 状态更新
    private Runnable statusUpdateRunnable;
    private static final int STATUS_UPDATE_INTERVAL = 2000; // 2秒更新一次

    private Fragment currentFragment;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 隐藏ActionBar（头部标题）
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // 隐藏系统底部导航栏，实现沉浸式体验（不隐藏状态栏）
        getWindow().getDecorView().setSystemUiVisibility(
                android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        | android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        );

        // 初始化UI组件
        initViews();

        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setOnNavigationItemSelectedListener(item -> {
            Fragment selectedFragment = null;
            int id = item.getItemId();

            if (id == R.id.nav_game_progress) {
                if (gameProgressFragment == null) {
                    gameProgressFragment = new GameProgressFragment();
                    Log.d(TAG, "创建新的GameProgressFragment实例");
                }
                selectedFragment = gameProgressFragment;
            } else if (id == R.id.nav_device_status) {
                if (deviceStatusFragment == null) {
                    deviceStatusFragment = new DeviceStatusFragment();
                    Log.d(TAG, "创建新的DeviceStatusFragment实例");
                }
                selectedFragment = deviceStatusFragment;
            } else if (id == R.id.nav_hardware_check) {
                if (hardwareCheckFragment == null) {
                    hardwareCheckFragment = new HardwareCheckFragment();
                    Log.d(TAG, "创建新的HardwareCheckFragment实例");
                }
                selectedFragment = hardwareCheckFragment;
            } else if (id == R.id.nav_game_settings) {
                if (gameSettingsFragment == null) {
                    gameSettingsFragment = new GameSettingsFragment();
                    Log.d(TAG, "创建新的GameSettingsFragment实例");
                }
                selectedFragment = gameSettingsFragment;
            }

            if (selectedFragment != null && selectedFragment != currentFragment) {
                Log.d(TAG, "切换到Fragment: " + selectedFragment.getClass().getSimpleName());
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, selectedFragment)
                        .commit();
                currentFragment = selectedFragment;
            }
            return true;
        });

        // 默认显示第一个Tab
        bottomNav.setSelectedItemId(R.id.nav_game_progress);
    }



    /**
     * 初始化UI组件
     */
    private void initViews() {
        tvConnectionStatus = findViewById(R.id.tv_connection_status);
        btnReconnect = findViewById(R.id.btn_reconnect);

        // 设置重连按钮点击事件
        btnReconnect.setOnClickListener(v -> {
            Log.i(TAG, "用户点击重连按钮");
            performReconnect();
        });

        // 设置连接状态监听器
        setupConnectionStatusListener();

        // 初始化连接状态显示
        updateConnectionStatus();

        // 启动定期状态更新
        startStatusUpdates();
    }

    /**
     * 设置连接状态监听器
     */
    private void setupConnectionStatusListener() {
        // 注册为MqttCommunicationManager的连接监听器
        MqttCommunicationManager.getInstance().addConnectionListener(this);

        // 设置增强连接管理器的监听器（不覆盖，而是添加额外的UI更新）
        EnhancedConnectionManager enhancedManager = EnhancedConnectionManager.getInstance();
        if (enhancedManager != null) {
            // 这里不设置监听器，避免覆盖MqttCommunicationManager中的设置
            // 增强连接管理器的状态变化会通过MqttCommunicationManager传递到这里
        }
    }

    /**
     * 执行重连操作
     */
    private void performReconnect() {
        tvConnectionStatus.setText("连接状态: 重新连接中...");
        tvConnectionStatus.setTextColor(getColor(R.color.status_connecting));

        // 禁用重连按钮，防止重复点击
        btnReconnect.setEnabled(false);

        // 强制重新发现服务器
        MqttCommunicationManager.getInstance().forceRediscoverServer();

        // 2秒后重新启用按钮
        btnReconnect.postDelayed(() -> {
            btnReconnect.setEnabled(true);
        }, 2000);
    }

    /**
     * 更新连接状态显示
     */
    private void updateConnectionStatus() {
        boolean isConnected = MqttCommunicationManager.getInstance().isConnected();

        if (isConnected) {
            tvConnectionStatus.setText("连接状态: 已连接");
            tvConnectionStatus.setTextColor(getColor(R.color.status_connected));
        } else {
            tvConnectionStatus.setText("连接状态: 未连接");
            tvConnectionStatus.setTextColor(getColor(R.color.status_disconnected));
        }
    }

    // ========== MqttCommunicationManager.ConnectionListener 实现 ==========

    @Override
    public void onConnected() {
        runOnUiThread(() -> {
            Log.i(TAG, "MainActivity收到连接成功通知");
            tvConnectionStatus.setText("连接状态: 已连接");
            tvConnectionStatus.setTextColor(getColor(R.color.status_connected));
        });
    }

    @Override
    public void onDisconnected() {
        runOnUiThread(() -> {
            Log.w(TAG, "MainActivity收到连接断开通知");
            tvConnectionStatus.setText("连接状态: 已断开");
            tvConnectionStatus.setTextColor(getColor(R.color.status_disconnected));
        });
    }

    @Override
    public void onConnectionError(String error) {
        runOnUiThread(() -> {
            Log.e(TAG, "MainActivity收到连接错误通知: " + error);
            tvConnectionStatus.setText("连接状态: 连接错误");
            tvConnectionStatus.setTextColor(getColor(R.color.status_disconnected));
        });
    }

    /**
     * 启动定期状态更新
     */
    private void startStatusUpdates() {
        if (statusUpdateRunnable == null) {
            statusUpdateRunnable = new Runnable() {
                @Override
                public void run() {
                    updateConnectionStatus();
                    // 继续下次更新
                    tvConnectionStatus.postDelayed(this, STATUS_UPDATE_INTERVAL);
                }
            };
        }

        // 启动定期更新
        tvConnectionStatus.postDelayed(statusUpdateRunnable, STATUS_UPDATE_INTERVAL);
        Log.d(TAG, "启动定期状态更新");
    }

    /**
     * 停止定期状态更新
     */
    private void stopStatusUpdates() {
        if (statusUpdateRunnable != null && tvConnectionStatus != null) {
            tvConnectionStatus.removeCallbacks(statusUpdateRunnable);
            Log.d(TAG, "停止定期状态更新");
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 恢复状态更新
        if (tvConnectionStatus != null) {
            startStatusUpdates();
        }

        // 确保MQTT连接并触发服务器重新发现
        try {
            MqttCommunicationManager mqttManager = MqttCommunicationManager.getInstance();

            // 如果未连接，先尝试重新发现服务器
            if (!mqttManager.isConnected()) {
                Log.i(TAG, "MQTT未连接，触发服务器重新发现");
                mqttManager.forceRediscoverServer();
            }

            mqttManager.connect();
            Log.d(TAG, "MainActivity恢复，确保MQTT连接");
        } catch (Exception e) {
            Log.e(TAG, "恢复MQTT连接失败", e);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        // 暂停状态更新
        stopStatusUpdates();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 停止状态更新
        stopStatusUpdates();

        // 移除连接监听器
        MqttCommunicationManager.getInstance().removeConnectionListener(this);

        Log.d(TAG, "MainActivity销毁");
        // 注意：不在这里断开MQTT连接，因为其他Activity可能还需要使用
    }
}