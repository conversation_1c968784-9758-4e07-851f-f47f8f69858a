package com.gzcec.xrandroidclient.utils;

import android.util.Log;

/**
 * 自定义日志工具类
 * 自动将日志写入文件和logcat
 */
public class XRLog {
    private static final String DEFAULT_TAG = "XRAndroidClient";
    private static boolean isDebugMode = true; // 可以通过配置控制
    
    /**
     * 设置调试模式
     */
    public static void setDebugMode(boolean debug) {
        isDebugMode = debug;
    }
    
    /**
     * Verbose级别日志
     */
    public static void v(String tag, String message) {
        if (isDebugMode) {
            Log.v(tag, message);
            LogManager.getInstance().writeLog("V", tag, message);
        }
    }
    
    public static void v(String message) {
        v(DEFAULT_TAG, message);
    }
    
    /**
     * Debug级别日志
     */
    public static void d(String tag, String message) {
        if (isDebugMode) {
            Log.d(tag, message);
            LogManager.getInstance().writeLog("D", tag, message);
        }
    }
    
    public static void d(String message) {
        d(DEFAULT_TAG, message);
    }
    
    /**
     * Info级别日志
     */
    public static void i(String tag, String message) {
        Log.i(tag, message);
        LogManager.getInstance().writeLog("I", tag, message);
    }
    
    public static void i(String message) {
        i(DEFAULT_TAG, message);
    }
    
    /**
     * Warning级别日志
     */
    public static void w(String tag, String message) {
        Log.w(tag, message);
        LogManager.getInstance().writeLog("W", tag, message);
    }
    
    public static void w(String message) {
        w(DEFAULT_TAG, message);
    }
    
    public static void w(String tag, String message, Throwable throwable) {
        Log.w(tag, message, throwable);
        LogManager.getInstance().writeLog("W", tag, message, throwable);
    }
    
    public static void w(String message, Throwable throwable) {
        w(DEFAULT_TAG, message, throwable);
    }
    
    /**
     * Error级别日志
     */
    public static void e(String tag, String message) {
        Log.e(tag, message);
        LogManager.getInstance().writeLog("E", tag, message);
    }
    
    public static void e(String message) {
        e(DEFAULT_TAG, message);
    }
    
    public static void e(String tag, String message, Throwable throwable) {
        Log.e(tag, message, throwable);
        LogManager.getInstance().writeLog("E", tag, message, throwable);
    }
    
    public static void e(String message, Throwable throwable) {
        e(DEFAULT_TAG, message, throwable);
    }
    
    /**
     * What a Terrible Failure级别日志
     */
    public static void wtf(String tag, String message) {
        Log.wtf(tag, message);
        LogManager.getInstance().writeLog("WTF", tag, message);
    }
    
    public static void wtf(String message) {
        wtf(DEFAULT_TAG, message);
    }
    
    public static void wtf(String tag, String message, Throwable throwable) {
        Log.wtf(tag, message, throwable);
        LogManager.getInstance().writeLog("WTF", tag, message, throwable);
    }
    
    public static void wtf(String message, Throwable throwable) {
        wtf(DEFAULT_TAG, message, throwable);
    }
    
    /**
     * 记录方法进入
     */
    public static void enter(String tag, String methodName) {
        if (isDebugMode) {
            String message = ">>> 进入方法: " + methodName;
            Log.d(tag, message);
            LogManager.getInstance().writeLog("D", tag, message);
        }
    }
    
    public static void enter(String methodName) {
        enter(DEFAULT_TAG, methodName);
    }
    
    /**
     * 记录方法退出
     */
    public static void exit(String tag, String methodName) {
        if (isDebugMode) {
            String message = "<<< 退出方法: " + methodName;
            Log.d(tag, message);
            LogManager.getInstance().writeLog("D", tag, message);
        }
    }
    
    public static void exit(String methodName) {
        exit(DEFAULT_TAG, methodName);
    }
    
    /**
     * 记录方法执行时间
     */
    public static void time(String tag, String methodName, long startTime) {
        if (isDebugMode) {
            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("⏱️ 方法 %s 执行时间: %d ms", methodName, duration);
            Log.d(tag, message);
            LogManager.getInstance().writeLog("D", tag, message);
        }
    }
    
    public static void time(String methodName, long startTime) {
        time(DEFAULT_TAG, methodName, startTime);
    }
    
    /**
     * 记录网络请求
     */
    public static void network(String tag, String url, String method, int responseCode) {
        String message = String.format("🌐 网络请求: %s %s -> %d", method, url, responseCode);
        Log.i(tag, message);
        LogManager.getInstance().writeLog("I", tag, message);
    }
    
    public static void network(String url, String method, int responseCode) {
        network(DEFAULT_TAG, url, method, responseCode);
    }
    
    /**
     * 记录MQTT消息
     */
    public static void mqtt(String tag, String topic, String action, String messageId) {
        String message = String.format("📡 MQTT %s: %s [%s]", action, topic, messageId);
        Log.i(tag, message);
        LogManager.getInstance().writeLog("I", tag, message);
    }
    
    public static void mqtt(String topic, String action, String messageId) {
        mqtt(DEFAULT_TAG, topic, action, messageId);
    }
    
    /**
     * 记录设备状态变化
     */
    public static void device(String tag, String deviceName, String status, String details) {
        String message = String.format("📱 设备 %s: %s - %s", deviceName, status, details);
        Log.i(tag, message);
        LogManager.getInstance().writeLog("I", tag, message);
    }
    
    public static void device(String deviceName, String status, String details) {
        device(DEFAULT_TAG, deviceName, status, details);
    }
    
    /**
     * 记录用户操作
     */
    public static void user(String tag, String action, String details) {
        String message = String.format("👤 用户操作: %s - %s", action, details);
        Log.i(tag, message);
        LogManager.getInstance().writeLog("I", tag, message);
    }
    
    public static void user(String action, String details) {
        user(DEFAULT_TAG, action, details);
    }
    
    /**
     * 记录性能数据
     */
    public static void performance(String tag, String metric, long value, String unit) {
        String message = String.format("⚡ 性能指标 %s: %d %s", metric, value, unit);
        Log.i(tag, message);
        LogManager.getInstance().writeLog("I", tag, message);
    }
    
    public static void performance(String metric, long value, String unit) {
        performance(DEFAULT_TAG, metric, value, unit);
    }
    
    /**
     * 记录内存使用情况
     */
    public static void memory(String tag) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        String message = String.format("💾 内存使用: %d/%d KB (最大: %d KB)", 
            usedMemory / 1024, totalMemory / 1024, maxMemory / 1024);
        Log.d(tag, message);
        LogManager.getInstance().writeLog("D", tag, message);
    }
    
    public static void memory() {
        memory(DEFAULT_TAG);
    }
}
