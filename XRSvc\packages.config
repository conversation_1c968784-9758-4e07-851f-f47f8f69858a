﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.6" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="9.0.6" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.6" targetFramework="net48" />
  <package id="MQTTnet" version="4.3.7.1207" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
</packages>