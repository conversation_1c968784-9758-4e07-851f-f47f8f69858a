using System;
using System.Windows;
using XRSvc.DataPack;

namespace XRSvc.DataSource
{
    public class DeviceServerSource : DependencyObject
    {
        #region Dependency Properties

        public static readonly DependencyProperty NameProperty = DependencyProperty.Register("Name", typeof(string), typeof(DeviceServerSource));
        public static readonly DependencyProperty TagProperty = DependencyProperty.Register("Tag", typeof(string), typeof(DeviceServerSource));
        public static readonly DependencyProperty IsOnlineProperty = DependencyProperty.Register("IsOnline", typeof(bool), typeof(DeviceServerSource));
        public static readonly DependencyProperty IsSelectedProperty = DependencyProperty.Register("IsSelected", typeof(bool), typeof(DeviceServerSource));

        #endregion

        #region Properties

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string Name
        {
            get => (string)GetValue(NameProperty);
            set => SetValue(NameProperty, value);
        }

        /// <summary>
        /// 服务器标识标签
        /// </summary>
        public string Tag
        {
            get => (string)GetValue(TagProperty);
            set => SetValue(TagProperty, value);
        }

        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline
        {
            get => (bool)GetValue(IsOnlineProperty);
            set => SetValue(IsOnlineProperty, value);
        }

        /// <summary>
        /// 是否被选中
        /// </summary>
        public bool IsSelected
        {
            get => (bool)GetValue(IsSelectedProperty);
            set => SetValue(IsSelectedProperty, value);
        }

        /// <summary>
        /// 在线状态文本
        /// </summary>
        public string StateText
        {
            get => IsOnline ? "在线" : "离线";
        }

        /// <summary>
        /// 在线状态颜色
        /// </summary>
        public System.Windows.Media.Brush StateColor
        {
            get => IsOnline ? System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Gray;
        }

        #endregion
    }
}