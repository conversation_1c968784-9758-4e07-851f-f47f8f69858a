package com.gzcec.xrandroidclient.communication.messages.base;

import com.gzcec.xrandroidclient.communication.constants.MessageType;
import com.google.gson.annotations.SerializedName;
import java.util.Date;

/**
 * 消息基类
 */
public abstract class BaseMessage {
    private String messageId;
    private String senderId;
    private MessageType type;
    private Date timestamp;

    public BaseMessage() {
        this.timestamp = new Date();
    }

    // Getter和Setter方法
    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }
    
    public String getSenderId() { return senderId; }
    public void setSenderId(String senderId) { this.senderId = senderId; }
    
    public MessageType getType() { return type; }
    public void setType(MessageType type) { this.type = type; }
    
    public Date getTimestamp() { return timestamp; }
    public void setTimestamp(Date timestamp) { this.timestamp = timestamp; }
}
