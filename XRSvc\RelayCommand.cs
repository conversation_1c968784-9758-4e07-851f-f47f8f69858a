using System;
using System.Windows.Input;

namespace XRSvc
{
    // 通用命令实现类，支持泛型参数
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Func<T, bool> _canExecute;

        // 构造函数，传入执行方法和可执行判断方法
        public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
        {
            _execute = execute;
            _canExecute = canExecute;
        }

        // 判断命令是否可执行
        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute((T)parameter);
        }

        // 执行命令
        public void Execute(object parameter)
        {
            _execute((T)parameter);
        }

        // 命令可执行状态变更事件
        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }
    }
}
