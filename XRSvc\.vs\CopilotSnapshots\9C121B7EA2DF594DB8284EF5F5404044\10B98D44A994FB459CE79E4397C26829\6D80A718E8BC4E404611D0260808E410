﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Threading;
using System.Threading.Tasks;
using XRSvc.DataPack; // Ensure GameInfo is referenced correctly
using XRSvc.ViewModels;
using XRSvc.DataSource;
using System.IO;

namespace XRSvc
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public ICommand SaveGameNameCommand { get; }

        private GameViewModel _gameViewModel;
        private DeviceViewModel _deviceViewModel;
        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        public ObservableCollection<string> LogMessages { get; } = new ObservableCollection<string>();
        private long lastPosition = 0;
        private string logPath = System.IO.Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase, "Logs", "XRSvc.log");

        public GameViewModel GameViewModel => _gameViewModel;
        public DeviceViewModel DeviceViewModel => _deviceViewModel;
        public ObservableCollection<DeviceServerInfo> DeviceServers { get; set; } = new ObservableCollection<DeviceServerInfo>();

        public MainWindow(List<GameInfo> gameInfos, List<DeviceInfo> deviceInfos, List<DeviceServerInfo> deviceServers)
        {
            InitializeComponent();
            _gameViewModel = new GameViewModel();
            _deviceViewModel = new DeviceViewModel();
            _gameViewModel.UpdateGames(gameInfos.ToArray());
            _deviceViewModel.UpdateDevices(deviceInfos.ToArray());
            if (deviceServers != null)
            {
                foreach (var server in deviceServers)
                {
                    DeviceServers.Add(server);
                }
            }
            else
            {
                // 默认添加两个示例
                DeviceServers.Add(new DeviceServerInfo { Name = "PICO企业套件", Tag = "10001", IsOnline = false });
                DeviceServers.Add(new DeviceServerInfo { Name = "自研服务端", Tag = "10002", IsOnline = true });
            }
            this.DataContext = this;
            StartLogFileWatcher();
        }

        private void SaveGameName(GameSource gameSource)
        {
            if (gameSource != null)
            {
                MessageBox.Show($"游戏名称已修改为: {gameSource.Name}", "修改成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            base.OnClosed(e);
        }

        private void StartLogFileWatcher()
        {
            Task.Run(() =>
            {
                while (true)
                {
                    try
                    {
                        if (File.Exists(logPath))
                        {
                            using (var fs = new FileStream(logPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                            {
                                fs.Seek(lastPosition, SeekOrigin.Begin);
                                using (var sr = new StreamReader(fs))
                                {
                                    string line;
                                    while ((line = sr.ReadLine()) != null)
                                    {
                                        var logLine = line;
                                        Dispatcher.Invoke(() => LogMessages.Add(logLine));
                                    }
                                    lastPosition = fs.Position;
                                }
                            }
                        }
                    }
                    catch { }
                    System.Threading.Thread.Sleep(1000);
                }
            });
        }

        private void Btn_SelectAll_Click(object sender, RoutedEventArgs e)
        {
            if (DeviceViewModel?.Devices != null)
            {
                foreach (var device in DeviceViewModel.Devices)
                {
                    device.IsEnabled = true;
                }
            }
        }

        private void Btn_UnselectAll_Click(object sender, RoutedEventArgs e)
        {
            if (DeviceViewModel?.Devices != null)
            {
                foreach (var device in DeviceViewModel.Devices)
                {
                    device.IsEnabled = false;
                }
            }
        }

        private void Btn_EndGame_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("结束游戏按钮被点击", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
