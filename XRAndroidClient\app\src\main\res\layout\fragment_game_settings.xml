<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">
    <!-- 游戏选择区 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_card_gray"
        android:orientation="vertical"
        android:layout_margin="16dp"
        android:padding="16dp"
        android:layout_gravity="center_horizontal"
        android:elevation="2dp"
        android:clipToPadding="false"
        android:clipChildren="false">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <CheckBox
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="唐诡"
                android:textColor="#FFFFFF"/>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="8dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="游戏版本: (v06071400)"
                    android:textColor="#888888"
                    android:textSize="14sp"/>
            </LinearLayout>
            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_more"
                android:tint="#888888"
                android:layout_marginStart="8dp"/>
        </LinearLayout>
        <Button
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:text="切换版本"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_marginTop="12dp"/>
    </LinearLayout>

    <!-- 功能按钮区 -->
    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="3"
        android:rowCount="2"
        android:background="@drawable/bg_card_gray"
        android:layout_margin="16dp"
        android:padding="16dp">

        <Button
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_columnWeight="1"
            android:text="游戏加速"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_margin="8dp"/>
        <Button
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_columnWeight="1"
            android:text="游戏减速"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_margin="8dp"/>
        <Button
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_columnWeight="1"
            android:text="游戏一倍速"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_margin="8dp"/>
        <Button
            android:id="@+id/btn_network_settings"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_columnWeight="1"
            android:text="网络设置"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_margin="8dp"/>
        <Button
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_columnWeight="1"
            android:text="设置"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_margin="8dp"/>
        <Button
            android:id="@+id/btn_view_logs"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_columnWeight="1"
            android:text="日志"
            android:background="@drawable/bg_btn_dark"
            android:textColor="#FFFFFF"
            android:layout_margin="8dp"/>
    </GridLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

</LinearLayout> 