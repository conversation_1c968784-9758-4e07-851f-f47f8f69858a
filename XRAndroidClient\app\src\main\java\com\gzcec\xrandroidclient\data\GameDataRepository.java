package com.gzcec.xrandroidclient.data;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.gzcec.xrandroidclient.communication.data.GameInfo;
import com.gzcec.xrandroidclient.utils.XRLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 游戏数据仓库
 * 管理游戏列表数据，提供LiveData支持
 */
public class GameDataRepository {
    private static final String TAG = "GameDataRepository";
    private static GameDataRepository instance;
    
    // LiveData for UI observation
    private MutableLiveData<List<GameInfo>> gamesLiveData = new MutableLiveData<>();
    private MutableLiveData<GameInfo> selectedGameLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> loadingLiveData = new MutableLiveData<>();
    private MutableLiveData<String> errorLiveData = new MutableLiveData<>();
    
    // 内部数据存储
    private List<GameInfo> gamesList = new ArrayList<>();
    private GameInfo selectedGame = null;
    private long lastUpdateTime = 0;
    
    private GameDataRepository() {
        // 初始化默认值
        gamesLiveData.setValue(new ArrayList<>());
        loadingLiveData.setValue(false);
        XRLog.d(TAG, "游戏数据仓库初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized GameDataRepository getInstance() {
        if (instance == null) {
            instance = new GameDataRepository();
        }
        return instance;
    }
    
    /**
     * 获取游戏列表LiveData
     */
    public LiveData<List<GameInfo>> getGamesLiveData() {
        return gamesLiveData;
    }
    
    /**
     * 获取选中游戏LiveData
     */
    public LiveData<GameInfo> getSelectedGameLiveData() {
        return selectedGameLiveData;
    }
    
    /**
     * 获取加载状态LiveData
     */
    public LiveData<Boolean> getLoadingLiveData() {
        return loadingLiveData;
    }
    
    /**
     * 获取错误信息LiveData
     */
    public LiveData<String> getErrorLiveData() {
        return errorLiveData;
    }
    
    /**
     * 更新游戏列表数据
     */
    public void updateGames(List<GameInfo> games) {
        XRLog.enter(TAG, "updateGames");
        
        if (games == null) {
            XRLog.w(TAG, "接收到空的游戏列表");
            return;
        }
        
        synchronized (this) {
            gamesList.clear();
            gamesList.addAll(games);
            lastUpdateTime = System.currentTimeMillis();
            
            // 按名称排序
            Collections.sort(gamesList, new Comparator<GameInfo>() {
                @Override
                public int compare(GameInfo g1, GameInfo g2) {
                    return g1.getName().compareToIgnoreCase(g2.getName());
                }
            });
            
            // 更新LiveData
            gamesLiveData.postValue(new ArrayList<>(gamesList));
            
            XRLog.i(TAG, "游戏列表已更新: " + games.size() + " 个游戏");
            
            // 记录游戏信息
            for (GameInfo game : games) {
                XRLog.d(TAG, "游戏: " + game.getName() + " (" + game.getVersion() + ") - " + 
                    (game.isAvailable() ? "可用" : "不可用"));
            }
        }
        
        XRLog.exit(TAG, "updateGames");
    }
    
    /**
     * 设置选中的游戏
     */
    public void setSelectedGame(GameInfo game) {
        XRLog.user(TAG, "选择游戏", game != null ? game.getName() : "无");
        
        synchronized (this) {
            selectedGame = game;
            selectedGameLiveData.postValue(game);
        }
    }
    
    /**
     * 获取当前选中的游戏
     */
    public GameInfo getSelectedGame() {
        return selectedGame;
    }
    
    /**
     * 根据ID获取游戏
     */
    public GameInfo getGameById(int gameId) {
        synchronized (this) {
            for (GameInfo game : gamesList) {
                if (game.getId() == gameId) {
                    return game;
                }
            }
        }
        return null;
    }
    
    /**
     * 根据包名获取游戏
     */
    public GameInfo getGameByPackageName(String packageName) {
        if (packageName == null) return null;
        
        synchronized (this) {
            for (GameInfo game : gamesList) {
                if (packageName.equals(game.getPackageName())) {
                    return game;
                }
            }
        }
        return null;
    }
    
    /**
     * 获取所有游戏列表
     */
    public List<GameInfo> getGames() {
        synchronized (this) {
            return new ArrayList<>(gamesList);
        }
    }

    /**
     * 获取可用的游戏列表
     */
    public List<GameInfo> getAvailableGames() {
        List<GameInfo> availableGames = new ArrayList<>();
        synchronized (this) {
            for (GameInfo game : gamesList) {
                if (game.isAvailable() && game.isEnabled()) {
                    availableGames.add(game);
                }
            }
        }
        return availableGames;
    }
    
    /**
     * 获取指定分类的游戏
     */
    public List<GameInfo> getGamesByCategory(String category) {
        List<GameInfo> categoryGames = new ArrayList<>();
        if (category == null) return categoryGames;
        
        synchronized (this) {
            for (GameInfo game : gamesList) {
                if (category.equals(game.getCategory())) {
                    categoryGames.add(game);
                }
            }
        }
        return categoryGames;
    }
    
    /**
     * 获取所有游戏分类
     */
    public List<String> getAllCategories() {
        List<String> categories = new ArrayList<>();
        synchronized (this) {
            for (GameInfo game : gamesList) {
                String category = game.getCategory();
                if (category != null && !categories.contains(category)) {
                    categories.add(category);
                }
            }
        }
        Collections.sort(categories);
        return categories;
    }
    
    /**
     * 设置加载状态
     */
    public void setLoading(boolean loading) {
        loadingLiveData.postValue(loading);
        if (loading) {
            XRLog.d(TAG, "开始加载游戏列表");
        } else {
            XRLog.d(TAG, "游戏列表加载完成");
        }
    }
    
    /**
     * 设置错误信息
     */
    public void setError(String error) {
        errorLiveData.postValue(error);
        if (error != null) {
            XRLog.e(TAG, "游戏数据错误: " + error);
        }
    }
    
    /**
     * 清除错误信息
     */
    public void clearError() {
        errorLiveData.postValue(null);
    }
    
    /**
     * 获取游戏总数
     */
    public int getGameCount() {
        synchronized (this) {
            return gamesList.size();
        }
    }
    
    /**
     * 获取可用游戏数量
     */
    public int getAvailableGameCount() {
        int count = 0;
        synchronized (this) {
            for (GameInfo game : gamesList) {
                if (game.isAvailable() && game.isEnabled()) {
                    count++;
                }
            }
        }
        return count;
    }
    
    /**
     * 获取最后更新时间
     */
    public long getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    /**
     * 检查数据是否需要刷新
     */
    public boolean needsRefresh(long maxAgeMillis) {
        return (System.currentTimeMillis() - lastUpdateTime) > maxAgeMillis;
    }
    
    /**
     * 清空游戏列表
     */
    public void clear() {
        XRLog.d(TAG, "清空游戏列表");
        synchronized (this) {
            gamesList.clear();
            selectedGame = null;
            lastUpdateTime = 0;
            
            gamesLiveData.postValue(new ArrayList<>());
            selectedGameLiveData.postValue(null);
        }
    }
    
    /**
     * 获取游戏统计信息
     */
    public GameStatistics getStatistics() {
        synchronized (this) {
            int totalGames = gamesList.size();
            int availableGames = 0;
            int enabledGames = 0;
            List<String> categories = getAllCategories();
            
            for (GameInfo game : gamesList) {
                if (game.isAvailable()) availableGames++;
                if (game.isEnabled()) enabledGames++;
            }
            
            return new GameStatistics(totalGames, availableGames, enabledGames, categories.size());
        }
    }
    
    /**
     * 游戏统计信息类
     */
    public static class GameStatistics {
        public final int totalGames;
        public final int availableGames;
        public final int enabledGames;
        public final int categoryCount;
        
        public GameStatistics(int totalGames, int availableGames, int enabledGames, int categoryCount) {
            this.totalGames = totalGames;
            this.availableGames = availableGames;
            this.enabledGames = enabledGames;
            this.categoryCount = categoryCount;
        }
        
        @Override
        public String toString() {
            return String.format("游戏统计: 总数=%d, 可用=%d, 启用=%d, 分类=%d", 
                totalGames, availableGames, enabledGames, categoryCount);
        }
    }
}
