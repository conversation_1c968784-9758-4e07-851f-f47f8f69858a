using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using XRSvc.CustomControl;
using XRSvc.DataPack;

namespace XRSvc.DataSource
{
    public class DeviceSource : DependencyObject
    {
        public static readonly DependencyProperty IDProperty = DependencyProperty.Register("ID", typeof(int), typeof(DeviceSource));
        public static readonly DependencyProperty SerialNumberProperty = DependencyProperty.Register("SerialNumber", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty IPProperty = DependencyProperty.Register("IP", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty NameProperty = DependencyProperty.Register("Name", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty TypeProperty = DependencyProperty.Register("Type", typeof(DeviceType), typeof(DeviceSource));
        public static readonly DependencyProperty IsEnabledProperty = DependencyProperty.Register("IsEnabled", typeof(bool), typeof(DeviceSource));
        public static readonly DependencyProperty IsOnlineProperty = DependencyProperty.Register("IsOnline", typeof(bool), typeof(DeviceSource));
        public static readonly DependencyProperty BatteryLevelProperty = DependencyProperty.Register("BatteryLevel", typeof(double), typeof(DeviceSource));
        public static readonly DependencyProperty DeviceTypeProperty = DependencyProperty.Register("DeviceType", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty ConnectionStatusProperty = DependencyProperty.Register("ConnectionStatus", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty IsGameRunningProperty = DependencyProperty.Register("IsGameRunning", typeof(bool), typeof(DeviceSource));
        public static readonly DependencyProperty CurrentGamePackageNameProperty = DependencyProperty.Register("CurrentGamePackageName", typeof(string), typeof(DeviceSource));
        public static readonly DependencyProperty IsGameStartingProperty = DependencyProperty.Register("IsGameStarting", typeof(bool), typeof(DeviceSource));

        // 设备ID
        public int ID
        {
            get { return (int)GetValue(IDProperty); }
            set { SetValue(IDProperty, value); }
        }

        // 设备唯一序列号
        public string SerialNumber
        {
            get { return (string)GetValue(SerialNumberProperty); }
            set { SetValue(SerialNumberProperty, value); }
        }

        // 设备IP
        public string IP
        {
            get { return (string)GetValue(IPProperty); }
            set { SetValue(IPProperty, value); }
        }

        // 设备名称
        public string Name
        {
            get { return (string)GetValue(NameProperty); }
            set { SetValue(NameProperty, value); }
        }

        // 设备类型
        public DeviceType Type
        {
            get { return (DeviceType)GetValue(TypeProperty); }
            set { SetValue(TypeProperty, value); }
        }

        /// <summary>
        /// 是否启用（勾选状态）- 只有在设备在线时才能被设置为true
        /// </summary>
        public bool IsEnabled
        {
            get { return (bool)GetValue(IsEnabledProperty); }
            set 
            { 
                // 只有在设备在线时才能被勾选
                if (value && !IsOnline)
                {
                    return; // 离线设备不能被勾选
                }
                SetValue(IsEnabledProperty, value); 
            }
        }

        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline
        {
            get { return (bool)GetValue(IsOnlineProperty); }
            set 
            { 
                SetValue(IsOnlineProperty, value);
                // 当设备离线时，自动取消勾选
                if (!value && IsEnabled)
                {
                    SetValue(IsEnabledProperty, false);
                }
            }
        }

        // 电池电量
        public double BatteryLevel
        {
            get { return (double)GetValue(BatteryLevelProperty); }
            set { SetValue(BatteryLevelProperty, value); }
        }

        // 设备类型字符串
        public string DeviceType
        {
            get { return (string)GetValue(DeviceTypeProperty); }
            set { SetValue(DeviceTypeProperty, value); }
        }

        // 连接状态
        public string ConnectionStatus
        {
            get { return (string)GetValue(ConnectionStatusProperty); }
            set { SetValue(ConnectionStatusProperty, value); }
        }

        // 游戏是否正在运行
        public bool IsGameRunning
        {
            get { return (bool)GetValue(IsGameRunningProperty); }
            set
            {
                bool oldValue = IsGameRunning;
                SetValue(IsGameRunningProperty, value);
                if (oldValue != value)
                {
                    // 只在状态真正变化时写日志
                    if (value)
                        Jskj.AppLog.Log.Write(Jskj.AppLog.Level.INFO, $"[{SerialNumber}] 游戏 {CurrentGamePackageName} 启动成功");
                    else
                        Jskj.AppLog.Log.Write(Jskj.AppLog.Level.INFO, $"[{SerialNumber}] 游戏 {CurrentGamePackageName} 停止成功");
                }
            }
        }

        // 当前运行的游戏包名
        public string CurrentGamePackageName
        {
            get { return (string)GetValue(CurrentGamePackageNameProperty); }
            set { SetValue(CurrentGamePackageNameProperty, value); }
        }

        // 游戏是否正在启动中
        public bool IsGameStarting
        {
            get { return (bool)GetValue(IsGameStartingProperty); }
            set { SetValue(IsGameStartingProperty, value); }
        }

        // 默认构造函数
        public DeviceSource() { }

        /// <summary>
        /// 通过DeviceInfo初始化的构造函数
        /// </summary>
        /// <param name="info">设备信息</param>
        public DeviceSource(DeviceInfo info)
        {
            ID = info.ID;
            SerialNumber = info.SerialNumber;
            IP = info.IpAddress;
            Name = info.Name;
            Type = info.DeviceType;
            IsOnline = info.IsOnline;
            BatteryLevel = info.BatteryLevel;
            DeviceType = info.DeviceType.ToString();
            ConnectionStatus = info.IsOnline ? "已连接" : "未连接";
            IsGameRunning = false;
            CurrentGamePackageName = null;
            IsGameStarting = false;

            // 只有在设备在线时才能被勾选
            IsEnabled = info.IsEnabled && info.IsOnline;
        }

        // 转换为DeviceInfo对象
        public DeviceInfo ToDeviceInfo()
        {
            return new DeviceInfo
            {
                ID = this.ID,
                SerialNumber = this.SerialNumber,
                IpAddress = this.IP,
                Name = this.Name,
                DeviceType = this.Type,
                IsEnabled = this.IsEnabled,
                IsOnline = this.IsOnline,
                BatteryLevel = this.BatteryLevel
            };
        }
    }
}