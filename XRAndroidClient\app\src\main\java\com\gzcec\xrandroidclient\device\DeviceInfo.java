package com.gzcec.xrandroidclient.device;

import com.gzcec.xrandroidclient.DeviceIdManager;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 设备信息类
 * 用于存储和管理XR设备的基本信息，与PC端DeviceInfo保持完全同步
 *
 * <AUTHOR>
 * @version 2.0
 * @since 1.0
 */
public class DeviceInfo {

    // 核心设备信息
    @SerializedName("id")
    private int id;

    @SerializedName("serialNumber")
    private String serialNumber;

    @SerializedName("name")
    private String name;

    @SerializedName("ipAddress")
    private String ipAddress;

    @SerializedName("deviceType")
    private DeviceType deviceType;

    // 状态信息
    @SerializedName("isOnline")
    private boolean isOnline;

    @SerializedName("isEnabled")
    private boolean isEnabled;

    @SerializedName("batteryLevel")
    private double batteryLevel;

    @SerializedName("isInGame")
    private boolean isInGame;

    @SerializedName("currentGamePackage")
    private String currentGamePackage;

    @SerializedName("gameStatus")
    private String gameStatus;

    // 本地UI状态 (不参与JSON序列化)
    private boolean isSelected;
    private boolean isGameStarting; // 游戏启动中状态
    private boolean isGameStopping; // 游戏停止中状态

    // 时间戳信息
    @SerializedName("lastUpdated")
    private Date lastUpdated;

    @SerializedName("lastConnected")
    private Date lastConnected;

    // Getter和Setter方法
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public String getSerialNumber() { return serialNumber; }
    public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }

    public DeviceType getDeviceType() { return deviceType; }
    public void setDeviceType(DeviceType deviceType) { this.deviceType = deviceType; }

    public boolean isOnline() { return isOnline; }
    public void setOnline(boolean online) { this.isOnline = online; }

    public boolean isEnabled() { return isEnabled; }
    public void setEnabled(boolean enabled) { this.isEnabled = enabled; }

    public double getBatteryLevel() { return batteryLevel; }
    public void setBatteryLevel(double batteryLevel) { this.batteryLevel = batteryLevel; }

    public boolean isInGame() { return isInGame; }
    public void setInGame(boolean inGame) { this.isInGame = inGame; }

    public String getCurrentGamePackage() { return currentGamePackage; }
    public void setCurrentGamePackage(String currentGamePackage) { this.currentGamePackage = currentGamePackage; }

    public String getGameStatus() { return gameStatus; }
    public void setGameStatus(String gameStatus) { this.gameStatus = gameStatus; }

    public boolean isSelected() { return isSelected; }
    public void setSelected(boolean selected) { this.isSelected = selected; }

    public boolean isGameStarting() { return isGameStarting; }
    public void setGameStarting(boolean gameStarting) { this.isGameStarting = gameStarting; }

    public boolean isGameStopping() { return isGameStopping; }
    public void setGameStopping(boolean gameStopping) { this.isGameStopping = gameStopping; }

    public Date getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(Date lastUpdated) { this.lastUpdated = lastUpdated; }

    public Date getLastConnected() { return lastConnected; }
    public void setLastConnected(Date lastConnected) { this.lastConnected = lastConnected; }

    // 兼容方法
    public String getBattery() {
        if (batteryLevel < 0) return "N/A";
        return String.format("%.0f%%", batteryLevel);
    }

    /**
     * 获取设备序列号的简写方法（兼容旧代码）
     */
    public String getSn() {
        return serialNumber;
    }

    /**
     * 创建初始设备列表（用于测试和初始化）
     * 初始化30个头显设备和6个外设设备，与PC端保持一致
     */
    public static List<DeviceInfo> createInitialDeviceList() {
        List<DeviceInfo> devices = new ArrayList<>();

        // 创建30个头显设备
        // 80服务器设备 (8001-8015)
        for (int i = 1; i <= 15; i++) {
            DeviceInfo device = new DeviceInfo();
            device.setId(8000 + i);
            device.setSerialNumber("HMD_80_" + String.format("%02d", i));
            device.setName(i + "号机");
            device.setIpAddress("192.168.1." + (99 + i));
            device.setDeviceType(DeviceType.HMD);
            device.setOnline(false); // 默认离线，等待PC端更新
            device.setEnabled(false); // 默认禁用，等待PC端更新
            device.setBatteryLevel(0); // 默认电量为0，等待PC端更新
            device.setInGame(false);
            device.setLastUpdated(new Date());
            devices.add(device);
        }

        // 81服务器设备 (8101-8115)
        for (int i = 1; i <= 15; i++) {
            DeviceInfo device = new DeviceInfo();
            device.setId(8100 + i);
            device.setSerialNumber("HMD_81_" + String.format("%02d", i));
            device.setName(i + "号机");
            device.setIpAddress("192.168.1." + (114 + i));
            device.setDeviceType(DeviceType.HMD);
            device.setOnline(false); // 默认离线，等待PC端更新
            device.setEnabled(false); // 默认禁用，等待PC端更新
            device.setBatteryLevel(0); // 默认电量为0，等待PC端更新
            device.setInGame(false);
            device.setLastUpdated(new Date());
            devices.add(device);
        }

        // 创建6个外设设备
        // 动感平台
        DeviceInfo motionPlatform = new DeviceInfo();
        motionPlatform.setId(9001);
        motionPlatform.setSerialNumber("MOTION_PLATFORM_001");
        motionPlatform.setName("动感平台");
        motionPlatform.setIpAddress("*************");
        motionPlatform.setDeviceType(DeviceType.MotionPlatform);
        motionPlatform.setOnline(false);
        motionPlatform.setEnabled(false);
        motionPlatform.setBatteryLevel(100); // 外设设备通常不需要电池
        motionPlatform.setInGame(false);
        motionPlatform.setLastUpdated(new Date());
        devices.add(motionPlatform);

        // 风扇
        DeviceInfo fan = new DeviceInfo();
        fan.setId(9002);
        fan.setSerialNumber("FAN_001");
        fan.setName("可调速风扇");
        fan.setIpAddress("*************");
        fan.setDeviceType(DeviceType.Fan);
        fan.setOnline(false);
        fan.setEnabled(false);
        fan.setBatteryLevel(100);
        fan.setInGame(false);
        fan.setLastUpdated(new Date());
        devices.add(fan);

        // 喷水设备
        DeviceInfo waterSpray = new DeviceInfo();
        waterSpray.setId(9003);
        waterSpray.setSerialNumber("WATER_SPRAY_001");
        waterSpray.setName("喷水设备");
        waterSpray.setIpAddress("*************");
        waterSpray.setDeviceType(DeviceType.WaterSpray);
        waterSpray.setOnline(false);
        waterSpray.setEnabled(false);
        waterSpray.setBatteryLevel(100);
        waterSpray.setInGame(false);
        waterSpray.setLastUpdated(new Date());
        devices.add(waterSpray);

        // 热感设备
        DeviceInfo hotness = new DeviceInfo();
        hotness.setId(9004);
        hotness.setSerialNumber("HOTNESS_001");
        hotness.setName("热感设备");
        hotness.setIpAddress("*************");
        hotness.setDeviceType(DeviceType.Hotness);
        hotness.setOnline(false);
        hotness.setEnabled(false);
        hotness.setBatteryLevel(100);
        hotness.setInGame(false);
        hotness.setLastUpdated(new Date());
        devices.add(hotness);

        // 可推拉门
        DeviceInfo doorManual = new DeviceInfo();
        doorManual.setId(9005);
        doorManual.setSerialNumber("DOOR_MANUAL_001");
        doorManual.setName("可推拉门");
        doorManual.setIpAddress("*************");
        doorManual.setDeviceType(DeviceType.DoorManual);
        doorManual.setOnline(false);
        doorManual.setEnabled(false);
        doorManual.setBatteryLevel(100);
        doorManual.setInGame(false);
        doorManual.setLastUpdated(new Date());
        devices.add(doorManual);

        // 指令控制门
        DeviceInfo doorCmdControl = new DeviceInfo();
        doorCmdControl.setId(9006);
        doorCmdControl.setSerialNumber("DOOR_CMD_001");
        doorCmdControl.setName("指令控制门");
        doorCmdControl.setIpAddress("*************");
        doorCmdControl.setDeviceType(DeviceType.DoorCmdControl);
        doorCmdControl.setOnline(false);
        doorCmdControl.setEnabled(false);
        doorCmdControl.setBatteryLevel(100);
        doorCmdControl.setInGame(false);
        doorCmdControl.setLastUpdated(new Date());
        devices.add(doorCmdControl);

        return devices;
    }

    /**
     * 从服务器响应更新设备列表
     * 以PC端设备信息为主，根据设备ID匹配更新Android端设备信息
     * @param localDevices 本地设备列表
     * @param serverDevices 服务器设备列表
     * @return 更新的设备数量
     */
    public static int updateDeviceListFromServer(List<DeviceInfo> localDevices, List<DeviceInfo> serverDevices) {
        if (localDevices == null || serverDevices == null) {
            return 0;
        }

        int updatedCount = 0;

        // 遍历服务器设备列表，更新本地设备信息
        for (DeviceInfo serverDevice : serverDevices) {
            boolean found = false;

            // 在本地列表中查找对应设备（优先按ID匹配）
            for (DeviceInfo localDevice : localDevices) {
                if (localDevice.getId() == serverDevice.getId()) {
                    // 保存本地UI状态（选中状态）
                    boolean wasSelected = localDevice.isSelected();

                    // 以PC端信息为主，更新所有设备信息
                    localDevice.setSerialNumber(serverDevice.getSerialNumber());
                    localDevice.setName(serverDevice.getName());
                    localDevice.setIpAddress(serverDevice.getIpAddress());
                    localDevice.setDeviceType(serverDevice.getDeviceType());
                    localDevice.setOnline(serverDevice.isOnline());
                    localDevice.setEnabled(serverDevice.isEnabled());
                    localDevice.setBatteryLevel(serverDevice.getBatteryLevel());
                    localDevice.setInGame(serverDevice.isInGame());
                    localDevice.setCurrentGamePackage(serverDevice.getCurrentGamePackage());
                    localDevice.setGameStatus(serverDevice.getGameStatus());
                    localDevice.setGameStarting(serverDevice.isGameStarting());
                    localDevice.setLastUpdated(new Date());

                    // 恢复本地UI状态
                    localDevice.setSelected(wasSelected);

                    found = true;
                    updatedCount++;
                    break;
                }
            }

            // 如果本地没有找到该设备ID，添加到列表中
            if (!found) {
                // 创建新设备，确保选中状态为false
                DeviceInfo newDevice = new DeviceInfo();
                newDevice.setId(serverDevice.getId());
                newDevice.setSerialNumber(serverDevice.getSerialNumber());
                newDevice.setName(serverDevice.getName());
                newDevice.setIpAddress(serverDevice.getIpAddress());
                newDevice.setDeviceType(serverDevice.getDeviceType());
                newDevice.setOnline(serverDevice.isOnline());
                newDevice.setEnabled(serverDevice.isEnabled());
                newDevice.setBatteryLevel(serverDevice.getBatteryLevel());
                newDevice.setInGame(serverDevice.isInGame());
                newDevice.setCurrentGamePackage(serverDevice.getCurrentGamePackage());
                newDevice.setGameStatus(serverDevice.getGameStatus());
                newDevice.setGameStarting(serverDevice.isGameStarting());
                newDevice.setSelected(false); // 新设备默认未选中
                newDevice.setLastUpdated(new Date());

                localDevices.add(newDevice);
                updatedCount++;
            }
        }

        return updatedCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeviceInfo that = (DeviceInfo) o;
        return id == that.id && Objects.equals(serialNumber, that.serialNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, serialNumber);
    }
}