# XR系统 Android端 API参考文档

## 📡 MQTT主题结构

### 主题层次
```
xr_system/
├── game_control/           # 游戏控制
│   ├── start_request      # 游戏启动请求
│   ├── start_response     # 游戏启动响应  
│   ├── stop_request       # 游戏停止请求
│   ├── stop_response      # 游戏停止响应
│   ├── progress_update    # 游戏进度更新
│   └── server_status      # 游戏服务器状态
├── device_management/      # 设备管理
│   ├── list_request       # 设备列表请求
│   ├── list_response      # 设备列表响应
│   ├── status_update      # 设备状态更新
│   ├── selection_changed  # 设备选择变更
│   └── battery_status     # 设备电池状态
└── system_status/          # 系统状态
    ├── heartbeat          # 心跳消息
    ├── error              # 系统错误
    ├── connection         # 连接状态
    └── log                # 系统日志
```

## 📨 消息格式定义

### 基础消息结构
```json
{
    "messageId": "uuid",           // 消息唯一标识
    "senderId": "client_id",       // 发送者ID
    "timestamp": "ISO8601",        // 时间戳
    "messageType": "request|response|update|notification"
}
```

### 游戏控制消息

#### 游戏启动请求 (game_control/start_request)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-************",
    "senderId": "AndroidClient_PICO_1642567890123",
    "timestamp": "2024-07-24T10:30:00.000Z",
    "messageType": "request",
    "selectedGame": {
        "gameId": "vr_adventure_001",
        "gameName": "VR冒险世界",
        "gameVersion": "1.2.0",
        "gameType": "Adventure",
        "maxPlayers": 8,
        "estimatedDuration": 1800
    },
    "selectedDeviceIds": ["device_001", "device_002", "device_003"],
    "gameSettings": {
        "difficulty": "Normal",        // Easy|Normal|Hard
        "enableSpecialEffects": true,
        "audioVolume": 80,
        "enableVibration": true
    }
}
```

#### 游戏启动响应 (game_control/start_response)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440001",
    "requestId": "550e8400-e29b-41d4-a716-************",
    "senderId": "PCServer_Main",
    "timestamp": "2024-07-24T10:30:05.000Z",
    "messageType": "response",
    "success": true,
    "message": "游戏启动成功",
    "gameSessionId": "session_20240724_103000",
    "startedDevices": [
        {
            "deviceId": "device_001",
            "status": "Started",
            "gamePort": 7777,
            "playerSlot": 1
        }
    ],
    "failedDevices": [
        {
            "deviceId": "device_003",
            "status": "Failed",
            "errorCode": "DEVICE_OFFLINE",
            "errorMessage": "设备离线"
        }
    ]
}
```

### 设备管理消息

#### 设备列表请求 (device_management/list_request)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440002",
    "senderId": "AndroidClient_PICO_1642567890123",
    "timestamp": "2024-07-24T10:25:00.000Z",
    "messageType": "request",
    "filters": {
        "onlineOnly": true,
        "deviceTypes": ["HMD", "MotionPlatform", "Fan"],
        "minBatteryLevel": 20,
        "includeDetails": true
    }
}
```

#### 设备列表响应 (device_management/list_response)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440003",
    "requestId": "550e8400-e29b-41d4-a716-446655440002",
    "senderId": "PCServer_Main",
    "timestamp": "2024-07-24T10:25:02.000Z",
    "messageType": "response",
    "totalCount": 25,
    "onlineCount": 18,
    "devices": [
        {
            "deviceId": "device_001",
            "deviceName": "PICO-001",
            "serialNumber": "PX001234567",
            "deviceType": "HMD",
            "isOnline": true,
            "ipAddress": "*************",
            "batteryLevel": 85,
            "gameStatus": "Idle",
            "inGame": false,
            "lastSeen": "2024-07-24T10:24:58.000Z",
            "firmwareVersion": "4.2.1",
            "hardwareInfo": {
                "cpuUsage": 15.2,
                "memoryUsage": 45.8,
                "temperature": 42.5,
                "diskSpace": 75.3
            }
        }
    ]
}
```

#### 设备状态更新 (device_management/status_update)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440004",
    "senderId": "PCServer_Main",
    "timestamp": "2024-07-24T10:35:00.000Z",
    "messageType": "update",
    "deviceId": "device_001",
    "updates": {
        "batteryLevel": 82,
        "gameStatus": "Playing",
        "inGame": true,
        "currentGameSession": "session_20240724_103000",
        "gameProgress": 35.5,
        "playerCount": 1,
        "hardwareInfo": {
            "cpuUsage": 68.3,
            "memoryUsage": 78.2,
            "temperature": 55.8
        }
    }
}
```

### 系统状态消息

#### 心跳消息 (system_status/heartbeat)
```json
{
    "messageId": "550e8400-e29b-41d4-a716-446655440005",
    "senderId": "AndroidClient_PICO_1642567890123",
    "timestamp": "2024-07-24T10:40:00.000Z",
    "messageType": "notification",
    "status": "OK",
    "clientInfo": {
        "appVersion": "1.0.0",
        "androidVersion": "11",
        "deviceModel": "PICO 4",
        "networkType": "WiFi",
        "signalStrength": -45,
        "batteryLevel": 95
    }
}
```

## 🔧 Java API接口

### MqttGameClient 核心方法

#### 连接管理
```java
/**
 * 连接到MQTT Broker
 * @throws MqttException 连接异常
 */
public void connect() throws MqttException

/**
 * 断开MQTT连接
 */
public void disconnect()

/**
 * 检查连接状态
 * @return 是否已连接
 */
public boolean isConnected()
```

#### 游戏控制
```java
/**
 * 请求启动游戏
 * @param gameInfo 游戏信息
 * @param selectedDeviceIds 选中的设备ID列表
 * @param callback 响应回调
 */
public void requestStartGame(GameInfo gameInfo, 
                           List<String> selectedDeviceIds, 
                           ResponseCallback callback)

/**
 * 请求停止游戏
 * @param gameSessionId 游戏会话ID
 * @param callback 响应回调
 */
public void requestStopGame(String gameSessionId, ResponseCallback callback)
```

#### 设备管理
```java
/**
 * 请求设备列表
 * @param onlineOnly 是否只获取在线设备
 * @param callback 响应回调
 */
public void requestDeviceList(boolean onlineOnly, ResponseCallback callback)

/**
 * 发送设备选择变更
 * @param selectedDeviceIds 选中的设备ID列表
 */
public void sendDeviceSelectionChanged(List<String> selectedDeviceIds)
```

### 回调接口定义

#### GameControlListener
```java
public interface GameControlListener {
    void onGameStartResponse(GameStartResponseMessage response);
    void onGameStopResponse(GameStopResponseMessage response);
    void onGameProgressUpdate(GameProgressUpdateMessage update);
}
```

#### DeviceStatusListener
```java
public interface DeviceStatusListener {
    void onDeviceListReceived(DeviceListResponseMessage response);
    void onDeviceStatusUpdate(DeviceStatusUpdateMessage update);
}
```

#### ConnectionListener
```java
public interface ConnectionListener {
    void onConnected();
    void onDisconnected(String reason);
    void onError(String error);
}
```

## 📊 数据模型

### DeviceInfo
```java
public class DeviceInfo {
    public int id;                  // 设备ID
    public String sn;               // 设备序列号
    public String name;             // 设备名称
    public boolean isOnline;        // 在线状态
    public String battery;          // 电池电量
    public boolean isSelected;      // 选中状态
    public String gameStatus;       // 游戏状态
    public boolean inGame;          // 游戏中状态
    public String ip;               // IP地址
    public DeviceType type;         // 设备类型
}
```

### DeviceType 枚举
```java
public enum DeviceType {
    HMD,                // VR头显
    MotionPlatform,     // 动感平台
    Fan,                // 风扇
    WaterSpray,         // 喷水设备
    DoorManual,         // 可推拉门
    ThermalSensor,      // 热感设备
    CommandDoor         // 指令控制门
}
```

## 🚨 错误码定义

### 系统错误码
```java
public class ErrorCodes {
    // 连接错误 (1000-1099)
    public static final int MQTT_CONNECTION_FAILED = 1001;
    public static final int MQTT_CONNECTION_LOST = 1002;
    public static final int NETWORK_UNAVAILABLE = 1003;
    
    // 设备错误 (2000-2099)
    public static final int DEVICE_OFFLINE = 2001;
    public static final int DEVICE_LOW_BATTERY = 2002;
    public static final int DEVICE_BUSY = 2003;
    
    // 游戏错误 (3000-3099)
    public static final int GAME_START_FAILED = 3001;
    public static final int GAME_ALREADY_RUNNING = 3002;
    public static final int GAME_NOT_FOUND = 3003;
    
    // 系统错误 (9000-9099)
    public static final int UNKNOWN_ERROR = 9000;
    public static final int TIMEOUT_ERROR = 9001;
    public static final int PERMISSION_DENIED = 9002;
}
```

## 📋 使用示例

### 完整的游戏启动流程
```java
// 1. 初始化MQTT客户端
MqttGameClient mqttClient = new MqttGameClient(context, "192.168.1.100", 1883);

// 2. 设置监听器
mqttClient.setGameControlListener(new MqttGameClient.GameControlListener() {
    @Override
    public void onGameStartResponse(GameStartResponseMessage response) {
        if (response.isSuccess()) {
            Log.i("Game", "游戏启动成功: " + response.getGameSessionId());
        } else {
            Log.e("Game", "游戏启动失败: " + response.getMessage());
        }
    }
});

// 3. 连接MQTT
mqttClient.connect();

// 4. 请求设备列表
mqttClient.requestDeviceList(true, response -> {
    // 解析设备列表
    List<DeviceInfo> devices = parseDeviceList(response);
    
    // 5. 选择设备并启动游戏
    GameInfo gameInfo = new GameInfo("vr_game_001", "VR冒险游戏");
    List<String> selectedDevices = Arrays.asList("device_001", "device_002");
    
    mqttClient.requestStartGame(gameInfo, selectedDevices, gameResponse -> {
        Log.i("Game", "游戏启动请求已发送");
    });
});
```

---

**API文档版本**: v1.0  
**最后更新**: 2024-07-24  
**适用版本**: Android App v1.0+
