package com.gzcec.xrandroidclient.communication.data;

/**
 * 游戏信息数据类
 */
public class GameInfo {
    private int id;
    private String name;
    private String packageName;
    private String version;
    private String description;
    private String iconPath;
    private int duration;
    private boolean isAvailable;
    private String category; // 游戏分类
    private String developer; // 开发商
    private long fileSize; // 文件大小
    private String installPath; // 安装路径
    private boolean isEnabled; // 是否启用
    private String lastUpdated; // 最后更新时间
    private int minPlayers; // 最少玩家数
    private int maxPlayers; // 最多玩家数
    private String difficulty; // 难度等级

    // Getter和Setter方法
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getPackageName() { return packageName; }
    public void setPackageName(String packageName) { this.packageName = packageName; }
    
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getIconPath() { return iconPath; }
    public void setIconPath(String iconPath) { this.iconPath = iconPath; }
    
    public int getDuration() { return duration; }
    public void setDuration(int duration) { this.duration = duration; }
    
    public boolean isAvailable() { return isAvailable; }
    public void setAvailable(boolean available) { isAvailable = available; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getDeveloper() { return developer; }
    public void setDeveloper(String developer) { this.developer = developer; }

    public long getFileSize() { return fileSize; }
    public void setFileSize(long fileSize) { this.fileSize = fileSize; }

    public String getInstallPath() { return installPath; }
    public void setInstallPath(String installPath) { this.installPath = installPath; }

    public boolean isEnabled() { return isEnabled; }
    public void setEnabled(boolean enabled) { isEnabled = enabled; }

    public String getLastUpdated() { return lastUpdated; }
    public void setLastUpdated(String lastUpdated) { this.lastUpdated = lastUpdated; }

    public int getMinPlayers() { return minPlayers; }
    public void setMinPlayers(int minPlayers) { this.minPlayers = minPlayers; }

    public int getMaxPlayers() { return maxPlayers; }
    public void setMaxPlayers(int maxPlayers) { this.maxPlayers = maxPlayers; }

    public String getDifficulty() { return difficulty; }
    public void setDifficulty(String difficulty) { this.difficulty = difficulty; }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取玩家数量描述
     */
    public String getPlayersDescription() {
        if (minPlayers == maxPlayers) {
            return minPlayers + "人";
        } else {
            return minPlayers + "-" + maxPlayers + "人";
        }
    }

    @Override
    public String toString() {
        return "GameInfo{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", version='" + version + '\'' +
            ", category='" + category + '\'' +
            ", isAvailable=" + isAvailable +
            ", isEnabled=" + isEnabled +
            '}';
    }
}
