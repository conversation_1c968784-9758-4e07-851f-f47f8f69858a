namespace PICOSDK
{
    using System;

    /// <summary>
    /// 设备事件参数类，用于传递设备相关事件数据
    /// </summary>
    public class DeviceEventArgs : EventArgs
    {
        /// <summary>
        /// 设备ID（序列号）
        /// </summary>
        public string DevID { get; set; }

        /// <summary>
        /// 电池电量（0-100）
        /// </summary>
        public uint Battery { get; set; }

        /// <summary>
        /// 监控帧数据
        /// </summary>
        public PXREAFrameBlob Frame { get; set; }
    }
} 