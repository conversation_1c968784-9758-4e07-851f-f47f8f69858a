using System;
using System.Threading.Tasks;
using XRSvc.Utils;
using Jskj.AppLog;

namespace XRSvc
{
    /// <summary>
    /// UI优化测试类，用于验证优化是否正常工作
    /// </summary>
    public static class TestUIOptimization
    {
        /// <summary>
        /// 测试UI线程调度器基本功能
        /// </summary>
        public static async Task TestBasicFunctionality()
        {
            try
            {
                Log.Write(Level.INFO, "开始测试UI线程调度器基本功能");
                
                var scheduler = UIThreadScheduler.Instance;
                
                // 测试同步调用
                scheduler.InvokeOnUIThread(() =>
                {
                    Log.Write(Level.INFO, "同步UI操作测试成功");
                });
                
                // 测试异步调用
                await scheduler.InvokeOnUIThreadAsync(() =>
                {
                    Log.Write(Level.INFO, "异步UI操作测试成功");
                });
                
                // 测试批量调用
                for (int i = 0; i < 5; i++)
                {
                    var index = i;
                    scheduler.BatchInvokeOnUIThread(() =>
                    {
                        Log.Write(Level.INFO, $"批量UI操作 {index} 测试成功");
                    }, $"batch_test_{index}");
                }
                
                // 刷新批量操作
                await scheduler.FlushBatchAsync();
                
                Log.Write(Level.INFO, "UI线程调度器基本功能测试完成");
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"UI线程调度器测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 运行完整测试
        /// </summary>
        public static async Task RunAllTests()
        {
            Log.Write(Level.INFO, "=== 开始UI优化完整测试 ===");
            
            await TestBasicFunctionality();
            
            // 如果基本功能测试成功，运行性能测试
            try
            {
                await UIOptimizationTest.RunBasicTestSuite();
            }
            catch (Exception ex)
            {
                Log.Write(Level.ERROR, $"性能测试失败: {ex.Message}");
            }
            
            Log.Write(Level.INFO, "=== UI优化完整测试结束 ===");
        }
    }
}
