{"RootPath": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc", "ProjectFileName": "XRSvc.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "DataPack\\DeviceInfo.cs"}, {"SourceFile": "DataPack\\DeviceType.cs"}, {"SourceFile": "DataSource\\DeviceSource.cs"}, {"SourceFile": "DataSource\\GameSource.cs"}, {"SourceFile": "RelayCommand.cs"}, {"SourceFile": "Utils\\JsonFileHelper.cs"}, {"SourceFile": "ViewModels\\BaseViewModel.cs"}, {"SourceFile": "ViewModels\\DeviceViewModel.cs"}, {"SourceFile": "ViewModels\\GameViewModel.cs"}, {"SourceFile": "CustomControl\\DeviceServerStyle.xaml.cs"}, {"SourceFile": "CustomControl\\DeviceStyle.xaml.cs"}, {"SourceFile": "CustomControl\\GameServerStyle.xaml.cs"}, {"SourceFile": "CustomControl\\GameStyle.xaml.cs"}, {"SourceFile": "CustomControl\\XRClientStyle.xaml.cs"}, {"SourceFile": "DataPack\\GameInfo.cs"}, {"SourceFile": "DataPack\\GameType.cs"}, {"SourceFile": "DataPack\\VideoFormat.cs"}, {"SourceFile": "HBAPP.cs"}, {"SourceFile": "MainWindow.xaml.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\CustomControl\\DeviceServerStyle.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\CustomControl\\DeviceStyle.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\CustomControl\\GameServerStyle.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\CustomControl\\GameStyle.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\CustomControl\\XRClientStyle.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\MainWindow.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\XRSvc_Content.g.cs"}, {"SourceFile": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\obj\\Debug\\GeneratedInternalTypeHelper.g.cs"}], "References": [{"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\Library\\Jskj.AppLog.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\Library\\Jskj.Win32Api.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\packages\\Microsoft.Bcl.AsyncInterfaces.9.0.6\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\packages\\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.6\\lib\\net462\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\packages\\Microsoft.Extensions.DependencyInjection.9.0.6\\lib\\net462\\Microsoft.Extensions.DependencyInjection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationFramework.Aero2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\packages\\System.Runtime.CompilerServices.Unsafe.4.5.3\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\01-Project\\Unity Project\\XRSystem\\XRSvc\\bin\\Debug\\XRSvc.exe", "OutputItemRelativePath": "XRSvc.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}